{"private": true, "engines": {"node": ">=5.0 <7", "npm": ">=3.3 <4"}, "dependencies": {"async": "2.6.2", "axios": "0.14.0", "babel-polyfill": "6.13.0", "babel-runtime": "6.11.6", "bcrypt": "3.0.6", "bcryptjs": "2.4.3", "bluebird": "3.4.6", "body-parser": "1.15.2", "cachegoose": "8.0.0", "classnames": "2.2.5", "connect-multiparty": "2.0.0", "core-js": "3.9.0", "crypto": "1.0.1", "dotenv": "16.0.3", "eventemitter3": "1.2.0", "express": "4.14.0", "express-session": "1.16.1", "express-validator": "2.21.0", "fastclick": "1.0.6", "fb": "1.1.1", "fbgraph": "1.4.4", "fbjs": "0.8.4", "front-matter": "2.1.0", "fs": "0.0.1-security", "fs.extra": "1.3.2", "geoip-lite": "1.3.7", "guid": "0.0.12", "history": "3.0.0", "ioredis": "4.28.5", "isomorphic-style-loader": "1.0.0", "joi": "10.6.0", "jsonwebtoken": "7.1.9", "knwl.js": "1.0.2", "lodash": "4.17.3", "markdown-it": "7.0.1", "memory-cache": "0.1.6", "moment": "2.24.0", "mongoose": "5.13.21", "ms": "0.7.3", "multer": "1.4.1", "nanoid": "1.2.5", "node-fetch": "1.6.0", "node-horseman": "3.3.0", "nodemailer": "6.4.10", "normalize.css": "4.2.0", "pretty-error": "2.0.0", "react": "15.3.1", "react-dom": "15.3.1", "react-modal": "1.9.7", "redis": "2.8.0", "request": "2.88.0", "request-promise": "4.2.2", "sass-loader": "4.1.1", "source-map-support": "0.4.2", "sse-nodejs": "git+https://github.com/tuanhm93/Server-Sent-Events-Nodejs.git", "universal-router": "1.2.2", "util": "0.10.4", "validator": "9.4.1", "whatwg-fetch": "1.0.0", "winston": "2.3.0", "winston-daily-rotate-file": "1.4.0"}, "devDependencies": {"assets-webpack-plugin": "3.5.1", "autoprefixer": "6.7.7", "babel-cli": "6.24.1", "babel-core": "6.25.0", "babel-eslint": "6.1.2", "babel-loader": "6.4.1", "babel-plugin-react-transform": "2.0.2", "babel-plugin-rewire": "1.1.0", "babel-plugin-transform-react-constant-elements": "6.23.0", "babel-plugin-transform-react-inline-elements": "6.22.0", "babel-plugin-transform-react-remove-prop-types": "0.2.12", "babel-plugin-transform-runtime": "6.23.0", "babel-preset-es2015": "6.24.1", "babel-preset-node5": "11.1.0", "babel-preset-react": "6.24.1", "babel-preset-stage-0": "6.24.1", "babel-register": "6.24.1", "babel-template": "6.25.0", "babel-types": "6.25.0", "browser-sync": "2.18.13", "chai": "3.5.0", "css-loader": "0.25.0", "del": "2.2.2", "enzyme": "2.9.1", "eslint": "3.19.0", "eslint-config-airbnb": "11.2.0", "eslint-loader": "1.9.0", "eslint-plugin-babel": "4.1.2", "eslint-plugin-flowtype": "2.35.0", "eslint-plugin-import": "1.16.0", "eslint-plugin-jsx-a11y": "2.2.3", "eslint-plugin-react": "6.10.3", "eslint-plugin-react-native": "2.3.2", "extend": "3.0.1", "file-loader": "0.9.0", "gaze": "1.1.2", "git-repository": "0.1.4", "glob": "7.1.2", "json-loader": "0.5.7", "mkdirp": "0.5.1", "mocha": "3.5.0", "ncp": "2.0.0", "node-pre-gyp": "0.12.0", "node-sass": "3.4.2", "pixrem": "3.0.2", "pleeease-filters": "3.0.1", "postcss": "5.2.17", "postcss-calc": "5.3.1", "postcss-color-function": "2.0.1", "postcss-custom-media": "5.0.1", "postcss-custom-properties": "5.0.2", "postcss-custom-selectors": "3.0.0", "postcss-flexbugs-fixes": "2.1.1", "postcss-import": "8.2.0", "postcss-loader": "0.13.0", "postcss-media-minmax": "2.1.2", "postcss-nesting": "2.3.1", "postcss-pseudoelements": "3.0.0", "postcss-selector-matches": "2.0.5", "postcss-selector-not": "2.0.0", "raw-loader": "0.5.1", "react-addons-test-utils": "15.3.1", "react-transform-catch-errors": "1.0.2", "react-transform-hmr": "1.0.4", "redbox-react": "1.5.0", "sass-loader": "4.1.1", "sinon": "2.4.1", "stylelint": "7.13.0", "stylelint-config-standard": "13.0.2", "url-loader": "0.5.9", "webpack": "1.15.0", "webpack-hot-middleware": "2.18.2", "webpack-middleware": "1.5.1"}, "babel": {"presets": ["react", "node5", "stage-0"], "env": {"test": {"plugins": ["rewire"]}}}, "eslintConfig": {"parser": "babel-es<PERSON>", "extends": "airbnb", "globals": {"__DEV__": true}, "env": {"browser": true}, "rules": {"arrow-parens": "off", "generator-star-spacing": "off", "import/no-extraneous-dependencies": "off", "react/forbid-prop-types": "off", "react/jsx-filename-extension": "off", "react/no-danger": "off", "react/no-unused-prop-types": "off", "react/require-extension": "off"}}, "stylelint": {"extends": "stylelint-config-standard", "rules": {"string-quotes": "single", "property-no-unknown": [true, {"ignoreProperties": ["composes"]}], "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["global", "local"]}]}}, "scripts": {"eslint": "eslint src tools", "stylelint": "stylelint \"src/**/*.css\"", "lint": "npm run eslint && npm run stylelint", "test": "mocha \"src/**/*.test.js\" --require test/setup.js --compilers js:babel-register", "test:watch": "npm run test -- --reporter min --watch", "clean": "babel-node tools/run clean", "copy": "babel-node tools/run copy", "bundle": "babel-node tools/run bundle", "build": "babel-node tools/run build", "deploy": "babel-node tools/run deploy", "render": "babel-node tools/run render", "start": "babel-node tools/run start"}}