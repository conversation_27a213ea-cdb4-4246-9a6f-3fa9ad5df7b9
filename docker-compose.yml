version: '2.2'

services:
  api:
    restart: on-failure
    build: ./
    volumes:
      - ./:/app
      - /app/node_modules
    environment:
      - MONGODB_MASTER_INSTANCES=${MONGODB_MASTER_INSTANCES}
      - MONGODB_MASTER_HOST=${MONGODB_MASTER_HOST}
      - MONG<PERSON>B_MASTER_PORT=${MONGODB_MASTER_PORT}
      - MONGODB_MASTER_DATABASE=${MONGODB_MASTER_DATABASE}
      - MONGODB_MASTER_USERNAME=${MONGODB_MASTER_USERNAME}
      - MONGODB_MASTER_PASSWORD=${MONGODB_MASTER_PASSWORD}
      - MONGODB_MASTER_MODE=${MONGODB_MASTER_MODE}
      - MONGODB_SUB_INSTANCES=${MONGODB_SUB_INSTANCES}
      - MONGODB_SUB_HOST=${MONG<PERSON>B_SUB_HOST}
      - MONGODB_SUB_PORT=${MONGODB_SUB_PORT}
      - MONGOD<PERSON>_SUB_DATABASE=${MONGODB_SUB_DATABASE}
      - MONGODB_SUB_USERNAME=${MONGODB_SUB_USERNAME}
      - MONGODB_SUB_PASSWORD=${MONGODB_SUB_PASSWORD}
      - MONGODB_SUB_MODE=${MONGODB_SUB_MODE}
      - MONGODB_TRACKINGACTION_INSTANCES=${MONGODB_TRACKINGACTION_INSTANCES}
      - MONGODB_TRACKINGACTION_HOST=${MONGODB_TRACKINGACTION_HOST}
      - MONGODB_TRACKINGACTION_PORT=${MONGODB_TRACKINGACTION_PORT}
      - MONGODB_TRACKINGACTION_DATABASE=${MONGODB_TRACKINGACTION_DATABASE}
      - MONGODB_TRACKINGACTION_USERNAME=${MONGODB_TRACKINGACTION_USERNAME}
      - MONGODB_TRACKINGACTION_PASSWORD=${MONGODB_TRACKINGACTION_PASSWORD}
      - MONGODB_TRACKINGACTION_MODE=${MONGODB_TRACKINGACTION_MODE}
      - MONGODB_CMS_INSTANCES=${MONGODB_CMS_INSTANCES}
      - MONGODB_CMS_HOST=${MONGODB_CMS_HOST}
      - MONGODB_CMS_PORT=${MONGODB_CMS_PORT}
      - MONGODB_CMS_DATABASE=${MONGODB_CMS_DATABASE}
      - MONGODB_CMS_USERNAME=${MONGODB_CMS_USERNAME}
      - MONGODB_CMS_PASSWORD=${MONGODB_CMS_PASSWORD}
      - MONGODB_CMS_MODE=${MONGODB_CMS_MODE}
      - SENTINEL_INSTANCES=${SENTINEL_INSTANCES}
      - SENTINEL_PASSWORD=${SENTINEL_PASSWORD}
      - REDIS_MASTER_NAME=${REDIS_MASTER_NAME}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - REDIS_DATABASE=${REDIS_DATABASE}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_MODE=${REDIS_MODE}
      - PORT=${PORT}
    ports:
      - "${PORT}:${PORT}" # phần này ta định nghĩa ở file .env nhé
  nginx:
    build: ./nginx
    ports:
    - '80:80'
    depends_on:
    - api
