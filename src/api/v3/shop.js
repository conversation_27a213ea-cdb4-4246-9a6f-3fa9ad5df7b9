import _ from 'lodash';
import async from 'async';
import util from 'util';
import CONSTANTS from '../../const';
import MESSAGE from '../../message';
import Joi from 'joi'
import fs from 'fs.extra'
import uuid from 'uuid/v4'
import rp from 'request-promise'
import { currentServerAddr, amazonS3Addr, cdnServerAddr } from '../../config'

export default {
  authen(req, res) {
    const userId = _.get(req, 'user.id', '');
    const avatar = _.get(req, 'body.avatar', '');
    const frontCer = _.get(req, 'body.frontCer', '');
    const backCer = _.get(req, 'body.backCer', '');
    const phone = _.get(req, 'body.phone', '');
    let oldData;
    let newData;

    const checkParams = (next) => {
      if (!avatar || !frontCer || !backCer) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGE.SYSTEM.WRONG_PARAMS
        })
      }

      next();
    }

    const updateDb = (next) => {
      AuthenShopInf
        .create({
          avatar,
          frontCer,
          backCer,
          member: userId,
          phone
        }, (err, result) => {
          next({
            code: CONSTANTS.CODE.SUCCESS
          })
        })
    }

    const updateJob = (next) => {
      next({
        code: CONSTANTS.CODE.SUCCESS
      })

      AuthenticationJob
        .findOneAndUpdate({
          phone
        }, {
          potentialPhoneStatus: 2,
          updatedAt: Date.now()
        })
        .lean()
        .exec((err, result) => {
          if (result) {
            AuthenticationJobLog
              .create({
                job: result._id,
                action: 'SUPPLEMENT_POTENTIAL_PHONE',
                oldData,
                newData
              }, (err, result) => { })
          }
        })
    }

    async.waterfall([
      checkParams,
      updateDb,
      // updateJob
    ], (err, data) => {
      console.log('haha:err', err, data)
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGE.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  },

  getAuthenInf(req, res) {
    const userId = _.get(req, 'user.id', '');

    AuthenShopInf
      .findOne({ member: userId }, "-member")
      .lean()
      .exec((err, result) => {
        if (_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        if (err) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGE.SYSTEM.ERROR
          })
        }

        res.json({
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        });
      })
  },

  modifyAuthenInf(req, res) {
    const userId = _.get(req, 'user.id', '');
    const id = _.get(req, 'body.id', '');
    const avatar = _.get(req, 'body.avatar', '');
    const frontCer = _.get(req, 'body.frontCer', '');
    const backCer = _.get(req, 'body.backCer', '');

    const checkParams = (next) => {
      if (!id) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGE.SYSTEM.WRONG_PARAMS
        })
      }

      next();
    }

    const updateAuthenInf = (next) => {
      const objUpdate = {
        status: 0,
        updatedAt: Date.now()
      }

      if (avatar) {
        objUpdate.avatar = avatar
      }

      if (frontCer) {
        objUpdate.frontCer = frontCer
      }

      if (backCer) {
        objUpdate.backCer = backCer
      }

      AuthenShopInf
        .findOneAndUpdate({
          _id: id,
          member: userId,
          status: { $ne: 1 }
        }, objUpdate, { 'new': false })
        .lean()
        .exec((err, result) => {
          if (err) {
            return next(err);
          }

          if (!result) {
            return next(new Error(`Not found authen inf`));
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS
          });
        })
    }

    async.waterfall([
      checkParams,
      updateAuthenInf
    ], (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGE.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  },

  configAuthenShop(req, res) {
    const userId = _.get(req, 'user.id', '');
    const regionName = _.get(req, 'body.regionName', '');
    let isOpen = 0;
    let status = 0;
    let hasAuthenInf = 0;
    let phone;

    const getConfig = (next) => {
      Config
        .get(CONSTANTS.CONFIG_TYPE.AUTHEN_SHOP, regionName, (err, result) => {
          if (err || !result || !result.config) {
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              data: { isOpen }
            });
          }

          isOpen = result.config.isOpen;

          next();
        })
    }

    const getMemberInf = (next) => {
      Members
        .findOne({ _id: userId }, 'phone')
        .lean()
        .exec((err, result) => {
          if (err || !result || !result.phone) {
            return next(err || new Error('Member not found'));
          }

          phone = result.phone;

          next();
        })
    }

    const getAuthenInf = (next) => {
      AuthenShopInf
        .findOne({ member: userId }, '_id')
        .lean()
        .exec((err, result) => {
          if (result) {
            hasAuthenInf = 1;
          }

          next();
        })
    }

    const getTrackingPhone = (next) => {
      if (!isOpen) {
        return next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: { isOpen, hasAuthenInf }
        })
      }

      TrackingPhone
        .findOne({
          phone,
          status: 1
        })
        .lean()
        .exec((err, result) => {
          if (!result) {
            isOpen = 0;
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: { isOpen, hasAuthenInf }
          })
        })
    }

    async.waterfall([
      getConfig,
      getMemberInf,
      getAuthenInf,
      getTrackingPhone
    ], (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR
      });

      res.json(data || err);
    })
  },
}
