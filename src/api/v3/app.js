import _ from 'lodash';
import async from 'async';
import util from 'util';
import fs from 'fs.extra'
import gatewayCardCharging from "gateway-1pay";
import { amazonS3Addr, notifyServiceAddr, mediaServerAddr, service, locationServerAddr, environment, orderTypeGroup } from '../../config';
import CONSTANTS from '../../const';
import MESSAGE from '../../message';
import uuid from 'uuid/v4';
import rp from 'request-promise'
const ms = require('ms')

export default {
  getBanner(req, res) {
    const region = _.get(req, 'body.regionName', '');
    const provider = _.get(req, 'body.provider', '');

    if (!region) {
      return res.json({
        code: CONSTANTS.CODE.WRONG_PARAMS
      });
    }

    let func = Banner.findOne({
      $or: [
        {
          'region.allow': region
        },
        {
          'region.allow': 'all',
          'region.deny': {
            $ne: region
          }
        }
      ],
      provider
    }, 'config').lean()

    if (environment === 'production') {
      func = func.cache(ms('15m') / 1000, `banner:${provider}:${region}`)
    }

    func.exec((err, result) => {
      if (err) {
        return res.json({
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });
      }
      return res.json({
        code: CONSTANTS.CODE.SUCCESS,
        data: result.config
      });
    })
  }
}
