import _ from 'lodash';
import CONSTANTS from '../../const';
import MESSAGES from '../../message';
import * as async from 'async';

export default {
  getNewOrderMessage(req, res) {

    const receiverId = req.body.receiverId || [];
    const userId = req.user.id;
    let conversationResult = [];

    const checkParams = (next) => {
      if (receiverId.length === 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS
        });
      }
      next();
    }

    const findConversations = (next) => {
      let queryArray = []
      receiverId.forEach((rId) => {
        queryArray.push([
          userId, rId
        ])
      })
      const arrayQuery = []
      queryArray.forEach((query) => {
        arrayQuery.push({
          users: { $all: query }
        })
      })
      Conversations.find({
        $or: arrayQuery
      })
        .lean()
        .select({
          latestMessage: 1
        })
        .exec((err, result) => {
          if (err) {
            return next(err);
          }
          conversationResult = result;
          if (conversationResult.length === 0) {
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              data: []
            });
          }
          next();
        })
    }

    const findNewMessage = (next) => {

      let queryArray = [];
      conversationResult.forEach((conversation) => {
        queryArray.push({
          _id: conversation.latestMessage,
        })
      })
      Messages.find({
        seen: 0,
        $or: queryArray,
        senderId: { $ne: userId }
      })
        .lean()
        .exec((err, result) => {
          if (err) {
            return next(err);
          }

          if (result.length === 0) {
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              data: []
            })
          }
          let senderArray = [];
          result.forEach((message) => {
            senderArray.push(message.senderId)
          })
          next({
            code: CONSTANTS.CODE.SUCCESS,
            data: senderArray
          })
        })
    }

    async.waterfall([
      checkParams,
      findConversations,
      findNewMessage
    ], (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR
      });

      res.json(data || err);
    });
  }
}
