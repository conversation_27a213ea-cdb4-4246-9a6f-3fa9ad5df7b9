import { Router } from 'express';
import cache from 'memory-cache';
import moment from 'moment';
const router = new Router();

export default {

    getInitFeeds() {

        return router.post('/api/v1.0/feeds/get', (req, res) => {
            let limit = req.body.limit ? parseInt(req.body.limit) - 1 : 79;
            if(!req.body.newVersion) {
              return redisConnection.get('feedfake', (err, feedFake) => {
                let newFeeds = [];
                if(!err) {
                  newFeeds.push(JSON.parse(feedFake));
                }
                res.json({
                  code: 200,
                  feeds: newFeeds
                })
              })
            }

            redisConnection.lrange("feedsFacebook", 0, limit, function (err, posts) {
                if(err) {
                    return res.json({
                        code: CONSTANTS.CODE.SYSTEM_ERROR
                    });
                }

                const postsFacebook = [];

                posts.forEach(function (post) {
                    post = JSON.parse(post);
                    if(post.type === 0) {
                        postsFacebook.push(post);
                    }
                });

                res.json({
                    code: 200,
                    feeds: postsFacebook
                });
            });

        });

    },

    getFeedsByShop() {

        return router.post('/api/v1.0/feeds-system/get', (req, res) => {

            req.checkBody('memberToken', 'memberToken is required').notEmpty();

            let errors = req.validationErrors();

            if (errors) {
                return res.json({
                    code : 400,
                    error : util.inspect(errors),
                    login: cache.get('member_login_state')
                })
            }

            Members.findOne({memberToken : req.body.memberToken}, (err, member) => {

                if(member) {

                    NewFeedSystem.find({member: member._id}, (err, feedsSystem) => {

                        return res.json({
                            code : 200,
                            feedsSystem : feedsSystem,
                            login: cache.get('member_login_state'),
                        })

                    });

                } else {

                    return res.json({
                        code : 404,
                        error : 'User not found',
                        login: cache.get('member_login_state'),
                    })

                }

            });

        });

    },

    createNewFeed() {

        return router.post('/api/v1.0/feed/create', (req, res) => {

            req.checkBody('memberToken', 'memberToken is required').notEmpty();
            req.checkBody('phone', 'phone is required').notEmpty();
            req.checkBody('salary', 'salary is required').notEmpty();
            req.checkBody('deposit', 'deposit is required').notEmpty();
            req.checkBody('origin_place', 'origin_place is required').notEmpty();
            req.checkBody('destination_places', 'origin_place is required').notEmpty();
            req.checkBody('status', 'status is required').notEmpty();

            let errors = req.validationErrors();

            if (errors) {
                return res.json({
                    code : 400,
                    error : util.inspect(errors),
                    login: cache.get('member_login_state')
                })
            }

            Members.findOne({memberToken: req.body.memberToken}, (err, member) => {

                if(err) {
                    return res.json({
                        code : 500,
                        error : 'Server Error',
                        login: cache.get('member_login_state')
                    })
                }

                if(member) {

                    let postObj = {
                        member: member._id,
                        origin_place : req.body.origin_place,
                        destination_places : req.body.destination_places,
                        deposit: req.body.deposit,
                        salary: req.body.salary,
                        note: req.body.note ? req.body.note : null,
                        phone: req.body.phone,
                        status: req.body.status,
                        created_time: moment().utcOffset(420).unix(),
                        error: req.body.error ? req.body.error : null,
                        result: req.body.result ? req.body.result : null
                    }

                    NewFeedSystem.create(postObj, (err, feed) => {

                        return res.json({
                            code: 200,
                            newfeed: feed,
                            login: cache.get('member_login_state'),
                        })
                    });

                } else {

                    return res.json({
                        code : 404,
                        error : 'User not found',
                        login: cache.get('member_login_state'),
                    })

                }


            });

        });

    }

}
