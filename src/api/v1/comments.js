import { Router } from 'express';
import { auth, comment_access_token_1, comment_access_token_2, comment_access_token_3, comment_access_token_4, comment_access_token_5 } from '../../config';
import cache from 'memory-cache';
const router = new Router();

const commentTokenArr = [];
commentTokenArr.push(comment_access_token_1, comment_access_token_2, comment_access_token_3, comment_access_token_4, comment_access_token_5);

export default {

    postComment() {

        return router.post('/api/v1.0/comment/post', (req, res) => {

            if(req.body.comment_id) {
                req.checkBody('comment_id', 'comment_id is required').notEmpty();
            }

            req.checkBody('message', 'member_token is required').notEmpty();
            req.checkBody('feed_id', 'feed_id is required').notEmpty();
            req.checkBody('member_token', 'member_token is required').notEmpty();

            let errors = req.validationErrors();

            if (errors) {
                return res.json({
                    code : 400,
                    error : util.inspect(errors),
                    login: cache.get('member_login_state'),
                })
            };

            Members.findOne({ memberToken: req.body.member_token }, (err, member) => {

                if (err) {
                    return res.json({
                        code : 500,
                        error : 'Server Error',
                        login: cache.get('member_login_state'),
                    })
                };


                if(member) {

                    let commentTo = req.body.comment_id ? req.body.comment_id : req.body.feed_id;

                    let FB_API_URI = `${auth.facebook.FB_API}/${commentTo}/comments?access_token=${member.facebook.token}`;

                    let options = {
                        url: FB_API_URI,
                        form: {
                            message: req.body.message,
                        }
                    };

                    request.post(options, (error, response, body) => {

                        let data = body && typeof body === 'string' ? JSON.parse(body) : {};

                        if(data.error && data.error.message === '(#100) Error finding the requested story' && data.error.code === 100) {

                            return res.json({
                                code: 101,
                                message: 'Feed Deleted',
                                login: cache.get('member_login_state'),
                            }); // Bài viết đã bị khoá

                        } else if(data.error && data.error.message === '(#200) Permissions error' && data.error.code === 200) {

                            return res.json({
                                code: 100,
                                message: 'Permissions error',
                                login: cache.get('member_login_state'),
                            }); // Chưa gia nhập group

                        } else if(data.error && data.error.message === '(#100) Error getting the message' && data.error.code === 100) {

                            return res.json({
                                code: 102,
                                message: 'This member has been blocked from this group',
                                login: cache.get('member_login_state'),
                            }); // Bị block khỏi group

                        } else if(data.error && data.error.message === '(#200) You do not have sufficient permissions to perform this action' && data.error.code === 200) {

                            return res.json({
                                code: 1993,
                                message: 'publish_actions is required'
                            }); // chưa cho quyền publish_actions

                        } else if(data.error && data.error.message === 'Error validating access token: The user is enrolled in a blocking, logged-in checkpoint' && data.error.code === 190) {

                            return res.json({
                                code: 1993,
                                message: 'Error validating access token'
                            }); // token chết

                        } else if (!error && response.statusCode == 200) {

                            return res.json({
                                code: 200,
                                login: cache.get('member_login_state'),
                            });

                        } else {

                            return res.json({
                                code: 1993
                            }); // vì lý do nào đó đưa lại luồng đăng nhập lại

                        }

                    })

                } else {

                    return res.json({
                        code : 404,
                        error : 'User not found',
                        login: cache.get('member_login_state'),
                    })
                }
            });

        });

    },

    getCommentsByFeedID() {

        return router.post('/api/v1.0/comment/get-comments', (req, res) => {

            req.checkBody('feed_id', 'feed_id is required').notEmpty();

            let errors = req.validationErrors();
            if (errors) {
                return res.json({
                    code : 400,
                    error : util.inspect(errors),
                    login: cache.get('member_login_state')
                });
            };

            getCommentOfFeed(req.body.feed_id, (err, comments) => {

                if(err) {
                    return res.json({
                        code : 105,
                        error: err,
                        login: cache.get('member_login_state'),
                    });
                }

                res.json({
                    code: 200,
                    comments: comments,
                    login: cache.get('member_login_state'),
                });


            });

        })

    }

}

function getCommentOfFeed(feed_id, callback) {

    let access_token = _.sample(commentTokenArr);

    let FB_API_URI = `${auth.facebook.FB_API}/${feed_id}/comments?fields=message,from,created_time,comments&access_token=${access_token}`;

    request(FB_API_URI, (error, response, body) => {

        let data = body && typeof body === 'string' ?  JSON.parse(body) : {};

        if(data.error) {
            callback(data.error.message, null);
        } else if (!error && response.statusCode == 200) {
            callback(null, data);
        } else {
            callback('error', null);
        }

    });
}
