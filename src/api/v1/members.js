import { Router } from 'express';
import { auth } from '../../config';
import cache from 'memory-cache';
import _ from 'lodash';
import CONSTANTS from '../../const';
import message from '../../message';
import async from 'async';
import ms from 'ms';
const router = new Router();
import { staffTransactionType } from '../../config';

export default {

    login() {

        return router.post('/api/v1.0/member/login', (req, res) => {

            req.checkBody('profile', 'profile is required').notEmpty();
            req.checkBody('access_token', 'facebook_id is required').notEmpty();

            let errors = req.validationErrors();

            if (errors) {
                return res.json({
                    code: 400,
                    error: util.inspect(errors)
                })
            }

            let profile = req.body.profile;
            let deviceInfo = req.body.deviceInfo;
            let access_token = req.body.access_token;

            Members.findOne({ 'facebook.id': profile.id }).populate('balance').exec((err, member) => {

                if (err) {
                    console.log(err);
                    return res.json({
                        code: 500,
                        error: 'Server Error',
                        login: cache.get('member_login_state')
                    })
                }

                if (member) {

                    async.waterfall([
                        callback => {
                            if (!member.granted) {
                                checkGranted(access_token, (err, grantedArr) => {
                                    let granted = _.some(grantedArr, { permission: 'publish_actions', status: 'granted' });
                                    if (granted) {
                                        callback(null, true);
                                    } else {
                                        callback(null, false);
                                    }
                                });
                            } else {
                                callback(null, true)
                            }
                        },
                        (granted, callback) => {
                            member.granted = granted;
                            member.save((err) => { });
                            callback(null, true);
                        }
                    ], (err, result) => {

                        if (result) {
                            let memberObj = {
                                "status": member.status,
                                "memberToken": member.memberToken,
                                "balance": member.balance,
                                "phone": member.phone,
                                "address": member.address,
                                "email": member.email,
                                "name": member.name,
                                "dislikes": member.dislikes,
                                "likes": member.likes,
                                "facebook": {
                                    "id": member.facebook.id,
                                    "name": member.facebook.name,
                                    "picture": member.facebook.picture,
                                },
                                "birthday": member.birthday,
                                "location": member.location,
                                "app_version": member.app_version,
                                "device_id": member.device_id,
                                "os_version": member.os_version,
                                "granted": member.granted
                            }

                            return res.json({
                                code: 200,
                                member: memberObj
                            });
                        } else {
                            return res.json({
                                code: 105,
                                message: err
                            });
                        }

                    })

                } else {
                    createNewMember(profile, deviceInfo, access_token, res);
                }

            });

        });

    },

    updatePhone() {
        return router.post('/api/v1.0/member/update-phone', (req, res) => {
            req.checkBody('phone', 'phone is required').notEmpty();
            req.checkBody('memberToken', 'memberToken is required').notEmpty();

            let errors = req.validationErrors();
            if (errors) {
                return res.json({
                    code: 400,
                    error: util.inspect(errors)
                })
            }

            let body = req.body;
            let query = { memberToken: body.memberToken };
            let update = {
                phone: body.phone,
                status: 1
            }

            Members
                .findOneAndUpdate(query, update)
                .lean()
                .exec((err, member) => {
                    if (err || !member) {
                        return res.json({
                            code: 500,
                            error: 'Server Error',
                        })
                    }
                    _.unset(member, 'facebook.token')
                    res.json({ code: 200, member: member })
                });
        });
    },

    updateProfile() {
        return router.post('/api/v1.0/member/update-profile', (req, res) => {
            req.checkBody('memberToken', 'memberToken is required').notEmpty();
            req.checkBody('profile', 'profile is required').notEmpty();
            req.checkBody('profile.name', 'name is required').notEmpty();
            req.checkBody('profile.email', 'email is required').notEmpty();
            req.checkBody('profile.address', 'address is required').notEmpty();
            req.checkBody('profile.birthday', 'birthday is required').notEmpty();

            let errors = req.validationErrors();
            if (errors) {
                return res.json({
                    code: 400,
                    message: message.SYSTEM.ERROR
                })
            }

            const userId = req.user.id;

            Members
                .findById(userId)
                .lean()
                .exec((err, result) => {
                    if (err || !result) {
                        return res.json({
                            code: 500,
                            message: message.SYSTEM.ERROR
                        })
                    }

                    const objUpdate = {
                        name: req.body.profile.name,
                        email: req.body.profile.email,
                        address: req.body.profile.address,
                        birthday: req.body.profile.birthday
                    }

                    if (!result.facebook || !result.facebook.id) {
                        objUpdate.facebook = result.facebook || {};
                        objUpdate.facebook.name = req.body.profile.name;
                        objUpdate.facebook.email = req.body.profile.email;
                        if (req.body.profile.access_token) {
                            objUpdate.facebook.token = req.body.profile.access_token;
                        }
                        if (req.body.profile.id) {
                            objUpdate.facebook.id = req.body.profile.id;
                        }
                        if (req.body.profile.picture) {
                            objUpdate.facebook.picture = req.body.profile.picture;
                        }
                    }

                    Members
                        .findOneAndUpdate({
                            _id: userId
                        }, objUpdate, {
                            'new': true
                        })
                        .lean()
                        .exec((err, member) => {
                            if (err) {
                                return res.json({
                                    code: 500,
                                    message: message.SYSTEM.ERROR
                                })
                            }
                            _.unset(member, 'facebook.token');
                            _.unset(member, 'password');
                            res.json({ code: 200, member: member });
                        });
                });
        });
    },

    saveFeed() {

        return router.post('/api/v1.0/member/feed/save', (req, res) => {

            req.checkBody('memberToken', 'memberToken is required').notEmpty();
            req.checkBody('message', 'message is required').notEmpty();
            req.checkBody('permalink_url', 'permalink_url is required').notEmpty();
            req.checkBody('from', 'from is required').notEmpty();
            req.checkBody('from.name', 'name is required').notEmpty();
            req.checkBody('from.id', 'facebook id is required').notEmpty();
            req.checkBody('created_time', 'created_time is required').notEmpty();
            req.checkBody('feed_id', 'feed_id is required').notEmpty();

            let errors = req.validationErrors();

            if (errors) {
                return res.json({
                    code: 400,
                    error: util.inspect(errors),
                    login: cache.get('member_login_state')
                })
            }
            req.body.type = req.body.type || 0; // Default now is facebook type

            Members.findOne({ memberToken: req.body.memberToken }, (err, member) => {

                if (err) {

                    return res.json({
                        code: 500,
                        error: 'Server Error',
                        login: cache.get('member_login_state')
                    })

                }

                if (member) {

                    req.body.member = member._id
                    req.body.createdAt = new Date();
                    Feeds.findOne({ feed_id: req.body.feed_id, member: member._id }, (err, feed) => {

                        if (!feed) {
                            Feeds.create(req.body, (err) => { })
                            return res.json({ code: 200 });
                        } else {
                            return res.json({ code: 405, message: 'Existed' });
                        }
                    })



                } else {

                    return res.json({
                        code: 404,
                        error: 'User not found',
                        login: cache.get('member_login_state')
                    })

                }

            });

        });

    },

    getFeeds() {

        return router.post('/api/v1.0/member/feed/all', (req, res) => {

            req.checkBody('memberToken', 'memberToken is required').notEmpty();

            let errors = req.validationErrors();
            if (errors) {
                return res.json({
                    code: 400,
                    error: util.inspect(errors),
                    login: cache.get('member_login_state'),
                })
            }


            Members.findOne({ memberToken: req.body.memberToken }, (err, member) => {
                if (err) {

                    return res.json({
                        code: 500,
                        error: 'Server Error',
                        login: cache.get('member_login_state')
                    })

                }

                if (member) {
                    Feeds
                        .find({ member: member._id })
                        .sort("-createdAt")
                        .populate("feedInf")
                        .lean()
                        .exec((err, feeds) => {
                            if (err) {
                                return res.json({
                                    code: 500,
                                    error: 'Server Error'
                                });
                            }

                            const feedsStillExist = [];
                            feeds.forEach((feed) => {
                                if (feed.feedInf.length !== 0) {
                                    const feedInf = feed.feedInf[0];
                                    feedInf.feed_id = feedInf.id;
                                    feedsStillExist.push(feedInf);
                                }
                            });

                            res.json({
                                code: 200,
                                feeds: feedsStillExist
                            });
                        });
                } else {

                    return res.json({
                        code: 404,
                        error: 'User not found',
                        login: cache.get('member_login_state'),
                    })

                }

            });

        });

    },

    deleteAFeed() {

        return router.post('/api/v1.0/member/feed/delete', (req, res) => {

            req.checkBody('feed_id', 'feed_id is required').notEmpty();
            req.checkBody('memberToken', 'memberToken is required').notEmpty();

            let errors = req.validationErrors();
            if (errors) {
                return res.json({
                    code: 400,
                    error: util.inspect(errors),
                    login: cache.get('member_login_state')
                })
            }


            Feeds.findOneAndRemove({ _id: req.body.feed_id }, (err) => {

                if (err) {

                    return res.json({
                        code: 500,
                        error: 'Server Error',
                        login: cache.get('member_login_state')
                    })

                }

                return res.json({
                    code: 200,
                })

            });
        })

    },

    deleteAllFeeds() {

        return router.post('/api/v1.0/member/feed/delete-all', (req, res) => {

            req.checkBody('memberToken', 'memberToken is required').notEmpty();

            let errors = req.validationErrors();

            if (errors) {
                return res.json({
                    code: 400,
                    error: util.inspect(errors),
                    login: cache.get('member_login_state')
                })
            }

            Members.findOne({ memberToken: req.body.memberToken }, (err, member) => {

                if (err) {
                    return res.json({
                        code: 500,
                        error: 'Server Error',
                        login: cache.get('member_login_state')
                    })
                };

                if (member) {

                    Feeds.remove({ member: member._id }, (error) => {
                        return res.json({
                            code: 200,
                        })
                    });

                } else {
                    return res.json({
                        code: 404,
                        error: 'User not found',
                        login: cache.get('member_login_state'),
                    })
                }

            })

        })

    },

    updateLocation() {
        return router.post('/api/v1.0/member/update-location', (req, res) => {
            return res.json({ code: 200 })
            Members.updateLocation(req.body.memberToken, req.body.location)
        });
    },
    addLoyalCustomer(req, res) {
        const nameSender = req.body.nameSender || '';
        const phoneSender = req.body.phoneSender || '';

        const userId = req.user.id;
        if (!req.body.location || !req.body.name || !req.body.location.lat || !req.body.location.lng || !nameSender || !phoneSender) {
            return res.json({
                code: CONSTANTS.CODE.WRONG_PARAMS
            })
        }

        let objLocation = {};
        objLocation.location = req.body.location;
        objLocation.name = req.body.name;
        objLocation.member = req.user.id;
        objLocation.subName = req.body.subName || '';
        objLocation.nameSender = req.body.nameSender || '';
        objLocation.phoneSender = req.body.phoneSender || '';
        objLocation.nameMain = req.body.nameMain || '';
        objLocation.nameSecondary = req.body.nameSecondary || '';
        LoyalCustomer
            .create(objLocation, (err, result) => {
                if (err) {
                    return res.json({
                        code: CONSTANTS.CODE.WRONG_PARAMS
                    })
                }
                res.json({
                    code: CONSTANTS.CODE.SUCCESS,
                    data: result
                })
            })
    },
    removeLoyalCustomer(req, res) {
        const locationId = req.body.id || '';

        if (!req.body.id) {
            return res.json({
                code: CONSTANTS.CODE.WRONG_PARAMS
            })
        }

        LoyalCustomer
            .findOneAndUpdate(
                {
                    _id: locationId,
                    status: 1
                },
                {
                    status: 0,
                    updatedAt: Date.now()
                }
            )
            .lean()
            .exec((err, result) => {
                if (err) {
                    return res.json({
                        code: CONSTANTS.CODE.SYSTEM_ERROR
                    })
                }
                res.json({
                    code: CONSTANTS.CODE.SUCCESS
                })
            });
    },
    modifyLoyalCustomer(req, res) {
        const locationId = req.body.id || '';
        const name = req.body.name || '';
        const nameDefault = req.body.nameDefault || '';
        const subName = req.body.subName || '';
        const location = req.body.location || '';
        const nameSender = req.body.nameSender || '';
        const phoneSender = req.body.phoneSender || '';
        const nameMain = req.body.nameMain || '';
        const nameSecondary = req.body.nameSecondary || '';

        if (!locationId) {
            return res.json({
                code: CONSTANTS.CODE.WRONG_PARAMS
            })
        }
        let objUpdate = {
            updatedAt: Date.now(),
            subName: subName
        };
        if (name) {
            objUpdate.name = name
        }
        if (location) {
            objUpdate.location = location
        }

        if (nameSender) {
            objUpdate.nameSender = nameSender;
        }

        if (phoneSender) {
            objUpdate.phoneSender = phoneSender;
        }

        if (nameDefault) {
            objUpdate.nameDefault = nameDefault;
        }

        if (nameMain) {
            objUpdate.nameMain = nameMain;
        }

        if (nameSecondary) {
            objUpdate.nameSecondary = nameSecondary;
        }

        LoyalCustomer
            .findOneAndUpdate(
                {
                    _id: locationId,
                    status: 1
                },
                objUpdate
            )
            .lean()
            .exec((err, result) => {
                if (err) {
                    return res.json({
                        code: CONSTANTS.CODE.SYSTEM_ERROR
                    })
                }
                res.json({
                    code: CONSTANTS.CODE.SUCCESS
                })
            });
    },
    listLoyalCustomers(req, res) {
        const userId = req.user.id || '';
        LoyalCustomer
            .find({
                member: userId,
                status: 1
            })
            .sort({
                createdAt: -1
            })
            .limit(50)
            .lean()
            .exec((err, result) => {
                if (err) {
                    return res.json({
                        code: CONSTANTS.CODE.SYSTEM_ERROR
                    })
                }
                res.json({
                    code: CONSTANTS.CODE.SUCCESS,
                    data: result
                })
            })
    },
    modifyListLoyalCustomers(req, res) {
        const arr = req.body.newList || []
        async.eachSeries(arr, (item, callback) => {
            const locationId = item._id || ''
            const createdAt = item.createdAt || '';
            let objUpdate = {};
            if (createdAt) {
                objUpdate.createdAt = createdAt;
            }
            LoyalCustomer
                .update(
                    {
                        _id: locationId,
                        status: 1,
                    },
                    objUpdate
                )
                .lean()
                .exec((err, result) => {
                    if (err) {
                        callback(err)
                    }
                    callback(null)
                });
        }, (err) => {
            if (err) {
                return res.json({
                    code: CONSTANTS.CODE.SYSTEM_ERROR
                })
            }
            res.json({
                code: CONSTANTS.CODE.SUCCESS,
            })
        })
    },

    unshiftLoyalCustomers(req, res) {
        const id = req.body.id || '';
        LoyalCustomer
            .update({
                _id: id
            }, {
                createdAt: Date.now()
            }, (err, result) => {
                if (err) {
                    return res.json({
                        code: CONSTANTS.CODE.SYSTEM_ERROR
                    })
                }
                res.json({
                    code: CONSTANTS.CODE.SUCCESS
                })
            })
    },

    historyTransactionStaff(req, res) {
        const from = req.body.from || Date.now();
        const limit = req.body.limit || 10;
        const userId = req.user.id;

        TransactionLog
            .find({
                member: userId,
                createdAt: {
                    $lt: from
                },
                'data.type': { $in: staffTransactionType }
            })
            .sort("-createdAt")
            .limit(limit)
            .lean()
            .exec((err, results) => {
                if (err) {
                    return res.json({
                        code: CONSTANTS.CODE.SYSTEM_ERROR,
                        message: message.SYSTEM.ERROR
                    })
                }

                return res.json({
                    code: CONSTANTS.CODE.SUCCESS,
                    data: results
                })
            })
    },

    transferSSMToCointStaff(req, res) {
        const amount = Math.round(Math.abs(req.body.amount || 0));
        const userId = req.user.id;
        let amountShop = 0
        let regionTran;

        const checkAmount = (next) => {
            if (typeof amount !== 'number' || amount <= 0) {
                return next({
                    code: CONSTANTS.CODE.WRONG_PARAMS,
                    message: message.SYSTEM.ERROR
                });
            }

            if (amount < 50000) {
                return next({
                    code: CONSTANTS.CODE.WRONG_PARAMS,
                    message: message.USER.MIN_MONEY_TRANSFER
                });
            }

            next();
        }

        const checkTime = (next) => {
            // Check time
            const currentDate = new Date();
            if (currentDate.getHours() < 22 && currentDate.getHours() >= 9) {
                return next({
                    code: CONSTANTS.CODE.FAIL,
                    message: message.USER.TIME_TRANSACTION_SSM
                })
            }

            next();
        }

        const checkRegionTransaction = (next) => {
            Members
                .findById(userId)
                .select('regionTransaction realMoneyStaff realMoneyShopStaff')
                .lean()
                .exec((err, result) => {
                    if (err) {
                        return next(err);
                    }

                    if (!result || !result.regionTransaction) {
                        return next({
                            code: CONSTANTS.CODE.SYSTEM_ERROR,
                            message: message.SYSTEM.ERROR
                        })
                    }

                    regionTran = result.regionTransaction
                    if (result.realMoneyShopStaff) {
                        if ((result.realMoneyStaff - result.realMoneyShopStaff) < amount) {
                            amountShop = amount - (result.realMoneyStaff - result.realMoneyShopStaff)
                        }
                    }
                    next();
                })
        }

        const updateMoney = (next) => {
            Members
                .findOneAndUpdate({
                    _id: userId,
                    realMoneyStaff: {
                        $gte: amount
                    }
                }, {
                    $inc: {
                        realMoneyStaff: -amount,
                        cointsStaff: amount,
                        realMoneyShopStaff: -amountShop
                    }
                }, { 'new': true }, (err, result) => {
                    if (err || !result) {
                        return next({
                            code: CONSTANTS.CODE.SYSTEM_ERROR,
                            message: message.SYSTEM.ERROR
                        });
                    }

                    TransactionLog
                        .create({
                            member: userId,
                            region: regionTran,
                            data: {
                                amount: -amount,
                                discount: 0,
                                bonus: 0,
                                initialCointsStaff: result.cointsStaff - amount,
                                finalCointsStaff: result.cointsStaff,
                                initialRealMoneyStaff: result.realMoneyStaff + amount,
                                finalRealMoneyStaff: result.realMoneyStaff,
                                initialRealMoneyShopStaff: result.realMoneyShopStaff + amountShop,
                                finalRealMoneyShopStaff: result.realMoneyShopStaff,
                                initialCoints: result.coints,
                                finalCoints: result.coints,
                                initialRealMoney: result.realMoney,
                                finalRealMoney: result.realMoney,
                                type: 60
                            },
                            message: "Chuyển SSM sang Coints"
                        }, (err) => {
                            if (err) {
                                return next(err);
                            }

                            return next({
                                code: CONSTANTS.CODE.SUCCESS,
                                data: {
                                    cointsStaff: result.cointsStaff,
                                    realMoneyStaff: result.realMoneyStaff
                                },
                                message: message.USER.TRANSFER_SSM_TO_COINT
                            })
                        })
                });
        }

        async.waterfall([
            checkAmount,
            checkTime,
            checkRegionTransaction,
            updateMoney
        ], (err, data) => {
            if (_.isError(err)) {
                logger.logError([err], req.originalUrl, req.body);
            }

            err && _.isError(err) && (data = {
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: message.SYSTEM.ERROR
            })

            res.json(data || err)
        })
    },

    listPackage(req, res) {
        const region = req.body.regionName || '';
        const limit = req.body.limit || 10;
        const skip = req.body.skip || 0;

        const checkParams = (next) => {
            if (!region) {
                return next({
                    code: CONSTANTS.CODE.WRONG_PARAMS
                })
            }

            next();
        }

        const listMembershipPackage = (next) => {
            MembershipPackage
                .find({
                    status: 1,
                    $and: [{
                        $or: [
                            {
                                'region.allow': 'all',
                                'region.deny': {
                                    $ne: region
                                }
                            },
                            {
                                'region.allow': region
                            }
                        ]
                    }, {
                        $or: [
                            {
                                expiredDate: { $gte: Date.now() }
                            },
                            {
                                expiredDate: { $exists: false }
                            }
                        ]
                    }]
                })
                .select("-active -region")
                .sort('order')
                .skip(skip)
                .limit(limit)
                .lean()
                .exec((err, results) => {
                    if (err) {
                        return next(err);
                    }

                    next(null, {
                        code: CONSTANTS.CODE.SUCCESS,
                        data: results
                    })
                })
        }

        // const getConfigPrice = (next) => {
        //     ConfigUrBox
        //         .findOne({
        //             status: 1
        //         })
        //         .lean()
        //         .exec((err, result) => {
        //             if (err) {
        //                 return next(err)
        //             }
        //             if (result && result.config && result.config.price) {
        //                 priceRate = result.config.price.rate;
        //                 specialPrice = result.config.price.special
        //             }

        //             if (result && result.config && result.config.priceHeywow && appName === 'heywow') {
        //                 priceRate = result.config.priceHeywow.rate;
        //                 specialPrice = result.config.priceHeywow.special
        //             }
        //             next();
        //         })
        // }

        // const getDataFromRedis = (next) => {
        //     if (type !== 'urbox') {
        //         return next({
        //             code: CONSTANTS.CODE.SUCCESS,
        //             data
        //         });
        //     }

        //     redisConnections('sub').getConnection().get(`urbox${config.env}${appName}:gift:${category}-${urboxUtil.getCityCode(region)}-${skip}-${limit}`, (err, result) => {
        //         if (err) {
        //             return next(err);
        //         }

        //         if (result) {
        //             dataUrbox = JSON.parse(result);
        //         } else {
        //             isRedisDataAlive = false;
        //         }

        //         next();
        //     })
        // }

        // const handleData = (next) => {
        //     dataUrbox.forEach((item, i) => {
        //         let subTitle = item.title;
        //         if (subTitle.indexOf(" đ") !== -1 && subTitle.indexOf("Thẻ Nạp") === -1) {
        //             subTitle = subTitle.replace("[UrBox Voucher]", "[Phiếu giảm thẳng]")
        //         } else {
        //             subTitle = subTitle.replace("[UrBox Voucher]", "[Voucher]")
        //         }

        //         subTitle = subTitle.trim();
        //         data.push({
        //             _id: item.id,
        //             id: item.id,
        //             title: item.brand_name,
        //             subTitle: subTitle,
        //             price: specialPrice && specialPrice[item.id] ? specialPrice[item.id] : Math.round(item.price * priceRate),
        //             image: item.images_rectangle['320'] ? item.images_rectangle['320'] : item.image,
        //             source: 'urbox'
        //         })
        //     });

        //     next(null, {
        //         code: CONSTANTS.CODE.SUCCESS,
        //         data
        //     })
        // }

        async.waterfall([
            checkParams,
            listMembershipPackage
        ], (err, data) => {
            if (_.isError(err)) {
                logger.logError([err], req.originalUrl, req.body);
            }

            err && _.isError(err) && (data = {
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: message.SYSTEM.ERROR
            });

            res.json(data || err);
        })
    },

    getPackage(req, res) {
        const id = req.body.id || '';

        const checkParams = (next) => {
            if (!id) {
                return next({
                    code: CONSTANTS.CODE.WRONG_PARAMS
                })
            }

            next();
        }

        const getMembershipPackage = (next) => {
            MembershipPackage
                .findOne({
                    _id: id,
                    status: 1
                })
                .select("-active -region")
                .lean()
                .exec((err, result) => {
                    if (err) {
                        return next(err);
                    }

                    if (!result) {
                        return next({
                            code: CONSTANTS.CODE.FAIL
                        });
                    }

                    next(null, {
                        code: CONSTANTS.CODE.SUCCESS,
                        data: result
                    })
                })
        }

        async.waterfall([
            checkParams,
            getMembershipPackage
        ], (err, data) => {
            if (_.isError(err)) {
                logger.logError([err], req.originalUrl, req.body);
            }

            err && _.isError(err) && (data = {
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: message.SYSTEM.ERROR
            });

            res.json(data || err);
        })
    },

    buyPackage(req, res) {
        const region = _.get(req, 'body.regionName', '');
        const id = _.get(req, 'body.id', '')
        const userId = _.get(req, 'user.id', '')
        let nativeVersion = _.get(req, 'body.nativeVersion', 0)
        const platform = _.get(req, 'body.platform', 0)
        nativeVersion = parseInt(nativeVersion)
        const appName = _.get(req, 'body.appName', '');

        let membershipPackage
        let number = 0
        let promoteCode;
        let memberCode;
        let memberInfo;

        let idPromote;

        const checkParams = (next) => {
            if (!region || !id || !userId) {
                return next({
                    code: CONSTANTS.CODE.WRONG_PARAMS
                })
            }

            next();
        }

        const checkMember = (next) => {
            Members
                .findOne({
                    _id: userId,
                    'ship.isAuthen': 1
                })
                .lean()
                .exec((err, result) => {
                    if (err) {
                        return next(err)
                    }

                    if (result) {
                        return next({
                            code: CONSTANTS.CODE.FAIL,
                            message: message.PACKAGE.INVALID_MEMBER
                        })
                    }

                    next();
                })
        }

        const getMembershipPackage = (next) => {
            MembershipPackage
                .findOne({
                    _id: id,
                    status: 1,
                    $and: [{
                        $or: [
                            {
                                'region.allow': 'all',
                                'region.deny': {
                                    $ne: region
                                }
                            },
                            {
                                'region.allow': region
                            }
                        ]
                    }, {
                        $or: [
                            {
                                expiredDate: { $gte: Date.now() }
                            },
                            {
                                expiredDate: { $exists: false }
                            }
                        ]
                    }]
                })
                .lean()
                .exec((err, result) => {
                    if (err) {
                        return next(err);
                    }

                    if (!result) {
                        return next({
                            code: CONSTANTS.CODE.FAIL,
                            message: message.PACKAGE.PACKAGE_NOT_EXISTS
                        })
                    }

                    membershipPackage = result
                    if (membershipPackage.promote && membershipPackage.promote.length) {
                        idPromote = membershipPackage.promote[0].id
                    }

                    if (membershipPackage.nativeVersion && membershipPackage.nativeVersion[platform] && membershipPackage.nativeVersion[platform] > nativeVersion) {
                        return next({
                            code: CONSTANTS.CODE.FAIL,
                            message: message.PACKAGE.INVALID_VERSION
                        })
                    }

                    if (membershipPackage.startDate && membershipPackage.startDate > Date.now()) {
                        return next({
                            code: CONSTANTS.CODE.FAIL,
                            message: {
                                head: 'Thông báo',
                                body: `Gói ưu đãi chưa ra mắt, vui lòng đợi đến ngày ${moment(membershipPackage.startDate).format('DD/MM/YY')} để mua và tận hưởng gói ưu đãi. Xin cảm ơn!`
                            }
                        })
                    }

                    if (typeof membershipPackage.timeBuyLeft !== 'undefined' && membershipPackage.timeBuyLeft <= 0) {
                        return next({
                            code: CONSTANTS.CODE.FAIL,
                            message: message.PACKAGE.FULL
                        })
                    }

                    next();
                })

        }

        const checkPackageForNewShop = (next) => {

            if (!idPromote || !membershipPackage.promote[0].forNewShop) {
                return next();
            }
            const options = {
                method: 'POST',
                uri: `${config.proxyRequestServer.promote}/api/v2.0/promote/list`,
                body: {
                    memberToken: req.body.memberToken,
                    regionName: region
                },
                timeout: 10000,
                json: true // Automatically stringifies the body to JSON
            };

            rp(options)
                .then((result) => {
                    if (result.code !== CONSTANTS.CODE.SUCCESS) {
                        return next({
                            code: CONSTANTS.CODE.FAIL,
                            message: message.PACKAGE.FAIL
                        })
                    }
                    let valid = false
                    if (result.data && result.data.length) {
                        for (var i = 0; i < result.data.length; i++) {
                            if (result.data[i]._id.toString() === idPromote.toString()) {
                                valid = true;
                                break;
                            }
                        }
                    }
                    if (valid) {
                        return next({
                            code: CONSTANTS.CODE.SUCCESS,
                            message: message.PACKAGE.SUCCESS
                        })
                    }
                    next({
                        code: CONSTANTS.CODE.FAIL,
                        message: message.PACKAGE.FAIL
                    })
                })
                .catch((err) => {
                    return next(err)
                });
        }

        const checkCanExchange = (next) => {
            PromoteUtil
                .checkTotalBuyByDevices(userId, id, membershipPackage, (err, valid) => {
                    if (err) {
                        return next(err);
                    }

                    if (!valid) {
                        return next({
                            code: CONSTANTS.CODE.FAIL,
                            message: message.PACKAGE.OVER
                        })
                    }

                    next();
                });
        }

        const checkLimit = (next) => {

            MembershipPackageLog
                .count({
                    member: userId,
                    package: id
                })
                .lean()
                .exec((err, count) => {
                    if (err) {
                        return next(err);
                    }

                    number = count + 1

                    if (membershipPackage.timeBuyPerUser && membershipPackage.timeBuyPerUser < number) {
                        return next({
                            code: CONSTANTS.CODE.FAIL,
                            message: message.PACKAGE.OVER
                        })
                    }
                    next();
                })
        }

        const checkOrder = (next) => {
            if (!membershipPackage.needOrder) {
                return next();
            }

            OrderSystem
                .count({
                    updatedAt: {
                        $gt: membershipPackage.createdAt
                    },
                    region,
                    shop: userId,
                    status: { $in: [3, 10] }
                })
                .limit(1)
                .sort('updatedAt')
                .lean()
                .exec((err, count) => {
                    if (err) {
                        return next(err)
                    }

                    if (!count) {
                        return next({
                            code: CONSTANTS.CODE.FAIL,
                            message: message.PACKAGE.ORDER
                        })
                    }

                    next();
                })
        }

        const findMember = (next) => {

            if (idPromote) {
                return next();
            }

            Members
                .findOne({
                    _id: userId
                })
                .lean()
                .exec((err, result) => {

                    if (err) {
                        return next(err);
                    }

                    if (!result || !result.code) {
                        return next({
                            code: CONSTANTS.CODE.SYSTEM_ERROR
                        })
                    }

                    memberCode = result.code

                    next();

                })
        }


        const checkPromoteExists = (next) => {

            if (idPromote) {
                return next();
            }

            promoteCode = membershipPackage.promote && membershipPackage.promote.length && membershipPackage.promote[0].code ? `${membershipPackage.promote[0].code}${memberCode}${number}` : '';
            if (!promoteCode) {
                return next({
                    code: CONSTANTS.CODE.FAIL,
                    message: message.PACKAGE.PROMOTE_EXISTS
                })
            }

            PromoteCode
                .count({
                    code: promoteCode
                })
                .lean()
                .exec((err, count) => {
                    if (err) {
                        return next(err)
                    }
                    if (count) {
                        return next({
                            code: CONSTANTS.CODE.FAIL,
                            message: message.PACKAGE.PROMOTE_EXISTS
                        })
                    }
                    next();
                })
        }

        const decreaseDeposit = (next) => {
            if (appName === 'heywow') {
                return next();
            }

            Members
                .findOneAndUpdate({
                    _id: userId,
                    deposit: {
                        $gte: membershipPackage.price
                    }
                }, {
                    $inc: {
                        deposit: -membershipPackage.price
                    }
                })
                .exec((err, result) => {
                    if (err) {
                        return next(err);
                    }

                    if (!result) {
                        return next({
                            code: CONSTANTS.CODE.FAIL,
                            message: {
                                'head': 'Thông báo',
                                'body': `Hiện tại bạn không còn đủ ${membershipPackage.price.toLocaleString()}đ để mua gói ưu đãi.`
                            }
                        })
                    }

                    memberInfo = result;

                    next();
                })
        }

        const createPromote = (next) => {

            if (idPromote) {
                return next();
            }

            let objCreate = {
                ...membershipPackage.promote[0]
            };
            objCreate.code = promoteCode
            objCreate.status = 1
            objCreate.createdAt = Date.now();

            const duration = membershipPackage.promote[0].condition.time.duration;
            let toTime = 0
            let fromTime = Date.now()
            if (membershipPackage.promote[0].condition.time.type === 'exactly') {
                toTime = duration
                if (membershipPackage.promote[0].condition.time.fromTime) {
                    fromTime = membershipPackage.promote[0].condition.time.fromTime
                }
            }

            if (membershipPackage.promote[0].condition.time.type === 'range') {
                toTime = fromTime + ms(duration)
            }

            objCreate.condition.time = {
                type: 'range',
                value: {
                    from: fromTime,
                    to: toTime
                }
            }

            objCreate.notification = "60737ffe19be9a7c69878485"
            objCreate.condition.member.whiteList = [userId]
            objCreate.alwaysShow = 1;

            PromoteCode
                .create(objCreate, (err, result) => {
                    if (err) {
                        return next(err);
                    }

                    MembershipPackageLog
                        .create({
                            member: userId,
                            type: 'promote',
                            data: {
                                code: promoteCode,
                                idPromote: result._id
                            },
                            point: membershipPackage.price,
                            package: membershipPackage._id,
                            appName
                        }, () => { })

                    if (membershipPackage.timeBuyLeft) {
                        MembershipPackage
                            .update({
                                _id: id
                            }, {
                                $inc: {
                                    timeBuyLeft: -1
                                }
                            }, (err, result) => {
                            })
                    }

                    next();
                })
        }

        const updatePromote = (next) => {
            if (!idPromote) {
                return next();
            }

            PromoteCode
                .update({
                    _id: idPromote
                }, {
                    $push: {
                        'condition.member.whiteList': userId
                    }
                }, (err, result) => {
                    if (err) {
                        return next(err)
                    }

                    MembershipPackageLog
                        .create({
                            member: userId,
                            type: 'promote',
                            data: {
                                idPromote: idPromote
                            },
                            point: membershipPackage.price,
                            loyalty: membershipPackage._id,
                            appName
                        }, () => { })

                    if (membershipPackage.timeBuyLeft) {
                        MembershipPackage
                            .update({
                                _id: id
                            }, {
                                $inc: {
                                    timeBuyLeft: -1
                                }
                            }, (err, result) => {
                            })
                    }

                    next();
                })
        }

        const writeLog = (next) => {

            next(null, {
                code: CONSTANTS.CODE.SUCCESS,
                message: message.PACKAGE.SUCCESS
            });

            // if (appName && appName === 'heywow') {
            //     HeywowTransactionLog
            //         .create({
            //             member: userId,
            //             region: region,
            //             message: "Đổi ưu đãi Mã Khuyến Mãi",
            //             data: {
            //                 amount: -membershipPackage.priceHeywow,
            //                 type: 7,
            //                 back: 0,
            //                 initialCashback: memberInfo.cashback,
            //                 finalCashback: memberInfo.cashback - membershipPackage.priceHeywow
            //             }
            //         }, (err) => {
            //         })
            // } else {
            //     PointTransactionLogModel
            //         .create({
            //             member: userId,
            //             region: region,
            //             message: "Đổi ưu đãi Mã Khuyến Mãi",
            //             data: {
            //                 point: -membershipPackage.price,
            //                 type: 7,
            //                 back: 0,
            //                 initialPoint: memberInfo.point,
            //                 finalPoint: memberInfo.point - membershipPackage.price
            //             }
            //         }, (err) => {
            //         })
            // }

            // PushNotifyManager.sendViaSocket(userId, 'profile_update', { title: '', description: '', data: { link: '', extras: {} } }, ["customer", "bookWeb"]);

        }

        async.waterfall([
            checkParams,
            // checkMember,
            getMembershipPackage,
            // checkPackageForNewShop,
            // checkCanExchange,
            checkLimit,
            checkOrder,
            findMember,
            checkPromoteExists,
            decreaseDeposit,
            // decreaseCashback,
            createPromote,
            updatePromote,
            writeLog
        ], (err, data) => {
            if (_.isError(err)) {
                logger.logError([err], req.originalUrl, req.body);
            }
            err && _.isError(err) && (data = {
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: MESSAGES.SYSTEM.ERROR
            });

            res.json(data || err);
        })
    },

    listMyPackage(req, res) {
        // const userId = req.user.id
        const userId = '62dbba0cdeb4f64c5915556c'
        const limit = req.body.limit || 10;
        const skip = req.body.skip || 0;
        let listPackage;

        const checkParams = (next) => {
            if (!userId) {
                return next({
                    code: CONSTANTS.CODE.WRONG_PARAMS
                })
            }

            next();
        }

        const listMyPackage = (next) => {
            MembershipPackageLog
                .find({
                    member: userId
                }, 'package data createdAt')
                .populate('package')
                .sort('-createdAt')
                .skip(skip)
                .limit(limit)
                .lean()
                .exec((err, results) => {
                    if (err) {
                        return next(err);
                    }

                    results.map(result => {
                        if (result.package) {
                            result.package.expiredDate = result.createdAt + ms(result.package.timeOfUse);
                        }
                    })

                    listPackage = results;

                    next();
                })
        }

        const checkTotalOrderUse = (next) => {
            async.eachSeries(listPackage, (item, done) => {
                if (!item.package || !item.package.promote || !item.package.promote.length) {
                    return done();
                }

                async.eachSeries(item.package.promote, (promote, cb) => {
                    if (!_.has(promote, 'condition.used.maximum')) {
                        return cb();
                    }

                    promote.maximumUses = promote.condition.used.maximum;

                    OrderSystem
                        .count({
                            shop: userId,
                            promote: '66f378675d796b74a91c239c',
                            status: {
                                $in: [-1, 0, 1, 2, 3, 7, 8, 9, 10]
                            }
                        })
                        .exec((err, count) => {
                            console.log('haha:err', err, count)
                            if (err) {
                                return cb(err);
                            }

                            promote.numberOfUses = promote.condition.used.maximum - count;

                            cb();
                        })
                }, (err) => {
                    if (err) {
                        return done(err)
                    }

                    done()
                })
            }, (err) => {
                if (err) {
                    return next(err)
                }

                next(null, {
                    code: CONSTANTS.CODE.SUCCESS,
                    data: listPackage
                })
            })
        }

        async.waterfall([
            checkParams,
            listMyPackage,
            checkTotalOrderUse
        ], (err, data) => {
            if (_.isError(err)) {
                logger.logError([err], req.originalUrl, req.body);
            }

            err && _.isError(err) && (data = {
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: message.SYSTEM.ERROR
            });

            res.json(data || err);
        })
    },

    getMyPackage(req, res) {
        const userId = req.user.id
        const id = req.body.id || '';

        const checkParams = (next) => {
            if (!userId || !id) {
                return next({
                    code: CONSTANTS.CODE.WRONG_PARAMS
                })
            }

            next();
        }

        const getMyPackage = (next) => {
            MembershipPackageLog
                .findOne({
                    _id: id,
                    member: userId
                })
                .populate('package')
                .lean()
                .exec((err, result) => {
                    if (err) {
                        return next(err);
                    }

                    if (result.package) {
                        result.package.expiredDate = result.createdAt + ms(result.package.timeOfUse);
                    }

                    next(null, {
                        code: CONSTANTS.CODE.SUCCESS,
                        data: result
                    })
                })
        }

        async.waterfall([
            checkParams,
            getMyPackage
        ], (err, data) => {
            if (_.isError(err)) {
                logger.logError([err], req.originalUrl, req.body);
            }

            err && _.isError(err) && (data = {
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: message.SYSTEM.ERROR
            });

            res.json(data || err);
        })
    }
}

//
//***** Helper *****
//
function createNewMember(profile, deviceInfo, access_token, res) {

    let memberToken = jwt.sign({ id: profile.id, name: profile.displayName }, auth.jwt.secret)

    let newMember = {
        facebook: {
            id: profile.id,
            name: profile.displayName,
            email: profile._json.email,
            token: access_token,
            locale: profile._json.locale,
            timezone: profile._json.timezone,
            picture: `https://graph.facebook.com/${profile.id}/picture?type=large`

        },
        status: 0,
        memberToken: memberToken
    };

    switch (deviceInfo.type) {

        case 'computer':
            newMember['os_version'] = {
                computer: deviceInfo.os
            }

            newMember['device_id'] = {
                computer: deviceInfo.device_id
            }
            break;

        case 'ios':
            newMember['os_version'] = {
                ios: deviceInfo.os
            }

            newMember['device_id'] = {
                ios: deviceInfo.device_id
            }
            newMember['app_version'] = {
                ios: deviceInfo.device_id
            }
            break;

        case 'android':
            newMember['os_version'] = {
                android: deviceInfo.os
            }

            newMember['device_id'] = {
                android: deviceInfo.device_id
            }

            newMember['app_version'] = {
                android: deviceInfo.device_id
            }
            break;
    }

    async.waterfall([

        callback => {

            Balance.create({ balance: 0 }, (err, balance) => {

                if (err) {
                    return res.json({
                        code: 500,
                        error: 'Server Error'
                    })
                };

                callback(null, balance);

            });

        },

        (balance, callback) => {

            newMember.balance = balance._id;

            checkGranted(access_token, (err, grantedArr) => {

                let granted = _.some(grantedArr, { permission: 'publish_actions', status: 'granted' });

                newMember.granted = granted;

                Members.create(newMember, (err, member) => {

                    if (err) {
                        return res.json({
                            code: 500,
                            error: 'Server Error'
                        })
                    };

                    member.balance = balance;
                    balance.member = member._id;
                    balance.save();

                    callback(null, member)

                })

            })
        }

    ], (err, result) => {

        if (err) {
            return res.json({
                code: 500,
                error: 'Server Error'
            })
        } else {
            return res.json({
                code: 200,
                member: result
            });

        }

    })

};

const sortByDateTime = (function () {

    //cached privated objects
    var _toString = Object.prototype.toString,

        _parser = function (x) { return x; },

        _getItem = function (x) {
            return this.parser((x !== null && typeof x === "object" && x[this.prop]) || x);
        };
    return function (array, o) {
        if (!(array instanceof Array) || !array.length)
            return [];
        if (_toString.call(o) !== "[object Object]")
            o = {};
        if (typeof o.parser !== "function")
            o.parser = _parser;
        o.desc = !!o.desc ? -1 : 1;
        return array.sort(function (a, b) {
            a = _getItem.call(o, a);
            b = _getItem.call(o, b);
            return o.desc * (a > b ? -1 : +(a < b));
        });
    };

}());

function checkGranted(token, callback) {

    let FB_API_URI = `${auth.facebook.FB_API}/me/permissions?access_token=${token}`

    let options = {
        url: FB_API_URI,
    };

    request.get(options, (error, response, body) => {

        let data = body && typeof body === 'string' ? JSON.parse(body) : {};

        if (data.error) {
            callback(data.error, null);
        } else if (!error && response.statusCode == 200) {
            callback(true, data.data);
        } else {
            callback('error', null);
        }

    })

}
