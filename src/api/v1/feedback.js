import { Router } from 'express';
const router = new Router();
import moment from 'moment';
import cache from 'memory-cache';

export default {

    postFeedBack() {

        return router.post('/api/v1.0/feedback/post', (req, res) => {

            req.checkBody('description', 'description is required').notEmpty();
            req.checkBody('version', 'version is required').notEmpty();

            let errors = req.validationErrors();
            if (errors) {
                return res.json({
                    code : 400,
                    error : util.inspect(errors),
                    login: cache.get('member_login_state')
                });
            };

            req.body.created = moment().utcOffset(420).unix()

            Feedback.create(req.body, (err) => {

                if(err) {
                    return res.json({
                        code : 400,
                        error : err
                    });
                }

                return res.json({
                    code : 200,
                });

            });

        });

    },

    getAllFeedback() {

        return router.get('/api/v1.0/feedback/get-all', (req, res) => {

            Feedback.find({}, (err, feedbacks) => {

                if(err) {
                    return res.json({
                        code : 400,
                        error : err
                    });
                }

                return res.json({
                    code : 200,
                    feedbacks: feedbacks
                });

            })

        })

    }

}
