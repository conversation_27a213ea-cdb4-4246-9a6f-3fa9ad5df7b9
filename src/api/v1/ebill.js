import { Router } from "express";
import { auth } from "../../config";
import cache from "memory-cache";
import _ from "lodash";
import MESSAGE from "../../message";
import CONSTANTS from "../../const";

const router = new Router();

export default {
    createBill(req, res) {
        const taxCode = _.get(req, "body.taxCode", "");
        const companyName = _.get(req, "body.companyName", "");
        const representative = _.get(req, "body.representative", "");
        const email = _.get(req, "body.email", "");
        const businessAddress = _.get(req, "body.businessAddress", "");
        const receiveAddress = _.get(req, "body.receiveAddress", "");
        const phone = _.get(req, "body.registeredPhoneNumber", []);
        const note = _.get(req, "body.note", "");
        const memberId = req.user.id;
        const checkParams = (next) => {
            if (
                !taxCode ||
                !companyName ||
                !representative ||
                !email ||
                !businessAddress ||
                !receiveAddress ||
                !phone.length
            ) {
                return next({
                    code: CONSTANTS.CODE.WRONG_PARAMS,
                    message: MESSAGE.SYSTEM.ERROR,
                });
            }
            next();
        };
        const hasCreated = (next) => {
            EBill.findOne({ member: memberId, active: 1 })
                .select("-member")
                .lean()
                .exec((err, result) => {
                    if (result) {
                        return next({
                            code: CONSTANTS.CODE.WRONG_PARAMS,
                            message: MESSAGE.EBILL.EXISTED
                        });
                    }
                    next();
                });
        };
        const createBill = (next) => {
            const obj = {
                taxCode: taxCode,
                companyName: companyName,
                representative: representative,
                email: email,
                businessAddress: businessAddress,
                receiveAddress: receiveAddress,
                registeredPhoneNumber: phone,
                note: note,
                member: memberId,
            };

            EBill.create(obj, (err, result) => {
                if (err || !result) {
                    return next(err);
                }
                next({
                    code: CONSTANTS.CODE.SUCCESS,
                    message: MESSAGE.EBILL.CREATE,
                    data: result,
                });
                EBillLog.create(
                    {
                        member: memberId,
                        type: CONSTANTS.TYPE_EBILL.SENDING,
                    },
                    (err) => {}
                );
            });
        };
        async.waterfall([checkParams, hasCreated, createBill], (err, data) => {
            if (_.isError(err)) {
                logger.logError([err], req.originalUrl, req.body);
            }
            err &&
                _.isError(err) &&
                (data = {
                    code: CONSTANTS.CODE.SYSTEM_ERROR,
                    message: MESSAGE.SYSTEM.ERROR,
                });
            res.json(data || err);
        });
    },
    updateBill(req, res) {
        const taxCode = _.get(req, "body.taxCode", "");
        const companyName = _.get(req, "body.companyName", "");
        const representative = _.get(req, "body.representative", "");
        const email = _.get(req, "body.email", "");
        const businessAddress = _.get(req, "body.businessAddress", "");
        const receiveAddress = _.get(req, "body.receiveAddress", "");
        const phone = _.get(req, "body.registeredPhoneNumber", []);
        const note = _.get(req, "body.note", "");
        const id = _.get(req, "body._id", "");
        const memberId = req.user.id;

        const checkParams = (next) => {
            if (
                !taxCode ||
                !companyName ||
                !representative ||
                !email ||
                !businessAddress ||
                !receiveAddress ||
                !phone
            ) {
                return next({
                    code: CONSTANTS.CODE.WRONG_PARAMS,
                    message: MESSAGE.SYSTEM.ERROR,
                });
            }
            next();
        };
        const updateBill = (next) => {
            const obj = {
                taxCode: taxCode,
                companyName: companyName,
                representative: representative,
                email: email,
                businessAddress: businessAddress,
                receiveAddress: receiveAddress,
                registeredPhoneNumber: phone,
                note: note,
                status: CONSTANTS.STATUS_EBILL.PENDING,
            };
            EBill.update(
                {
                    _id: id,
                    member: memberId,
                    active: 1,
                },
                obj,
                {
                    new: true,
                }
            )
                .lean()
                .exec((err, result) => {
                    if (err) {
                        return next(err);
                    }
                    next({
                        code: CONSTANTS.CODE.SUCCESS,
                        message: MESSAGE.EBILL.SUCCESS,
                    });
                    EBillLog.create(
                        {
                            member: memberId,
                            type: CONSTANTS.TYPE_EBILL.UPDATING,
                        },
                        (err) => {}
                    );
                });
        };
        async.waterfall([checkParams, updateBill], (err, data) => {
            if (_.isError(err)) {
                logger.logError([err], req.originalUrl, req.body);
            }
            err &&
                _.isError(err) &&
                (data = {
                    code: CONSTANTS.CODE.SYSTEM_ERROR,
                    message: MESSAGE.SYSTEM.ERROR,
                });
            res.json(data || err);
        });
    },
    approveBill(req, res) {
        const id = _.get(req, "body._id", "");
        const admin = _.get(req, "body.admin", "");
        const checkParams = (next) => {
            if (!id || !admin) {
                return next({
                    code: CONSTANTS.CODE.WRONG_PARAMS,
                    message: MESSAGE.SYSTEM.ERROR,
                });
            }
            next();
        };
        const approveBill = (next) => {
            EBill.update(
                {
                    _id: id,
                    active: 1,
                },
                {
                    status: CONSTANTS.STATUS_EBILL.APPROVED,
                    admin: admin,
                },
                {
                    new: true,
                }
            )
                .lean()
                .exec((err, result) => {
                    if (err) {
                        return next(err);
                    }
                    next({
                        code: CONSTANTS.CODE.SUCCESS,
                        message: MESSAGE.EBILL.SUCCESS,
                    });
                    EBillLog.create(
                        {
                            supporter: admin,
                            type: CONSTANTS.TYPE_EBILL.APPROVE,
                        },
                        (err) => {}
                    );
                });
        };
        async.waterfall([checkParams, approveBill], (err, data) => {
            if (_.isError(err)) {
                logger.logError([err], req.originalUrl, req.body);
            }
            err &&
                _.isError(err) &&
                (data = {
                    code: CONSTANTS.CODE.SYSTEM_ERROR,
                    message: MESSAGE.SYSTEM.ERROR,
                });
            res.json(data || err);
        });
    },
    rejectBill(req, res) {
        const id = _.get(req, "body._id", "");
        const admin = _.get(req, "body.admin", "");
        const message = _.get(req, "body.message", "");
        const checkParams = (next) => {
            if (!id || !admin || !message) {
                return next({
                    code: CONSTANTS.CODE.WRONG_PARAMS,
                    message: MESSAGE.SYSTEM.ERROR,
                });
            }
            next();
        };
        const rejectBill = (next) => {
            EBill.update(
                {
                    _id: id,
                    status: CONSTANTS.STATUS_EBILL.PENDING,
                    active: 1
                },
                {
                    status: CONSTANTS.STATUS_EBILL.REJECTED,
                    message: message,
                },
                {
                    new: true,
                }
            )
                .lean()
                .exec((err, result) => {
                    if (err) {
                        return next(err);
                    }
                    if (result.n) {
                        next({
                            code: CONSTANTS.CODE.SUCCESS,
                            message: MESSAGE.EBILL.SUCCESS,
                        });
                        EBillLog.create(
                            {
                                type: CONSTANTS.TYPE_EBILL.REJECTED,
                                supporter: admin,
                            },
                            (err) => {}
                        );
                    } else {
                        next({
                            code: CONSTANTS.CODE.SUCCESS,
                            message: MESSAGE.EBILL.FAILED,
                        });
                    }
                });
        };
        async.waterfall([checkParams, rejectBill], (err, data) => {
            if (_.isError(err)) {
                logger.logError([err], req.originalUrl, req.body);
            }
            err &&
                _.isError(err) &&
                (data = {
                    code: CONSTANTS.CODE.SYSTEM_ERROR,
                    message: MESSAGE.SYSTEM.ERROR,
                });
            res.json(data || err);
        });
    },
    deleteBill(req, res) {
        const id = _.get(req, "body._id", "");
        const memberId = req.user.id;
        const checkParams = (next) => {
            if (!id) {
                return next({
                    code: CONSTANTS.CODE.WRONG_PARAMS,
                    message: MESSAGE.SYSTEM.ERROR,
                });
            }
            next();
        };
        const deleteBill = (next) => {
            EBill.update(
                {
                    _id: id,
                    member: memberId,
                    active: 1
                },
                {
                    active: 0,
                    status: CONSTANTS.STATUS_EBILL.PENDING,
                },
                {
                    new: true,
                }
            )
                .lean()
                .exec((err, result) => {
                    if (err) {
                        return next(err);
                    }
                    next({
                        code: CONSTANTS.CODE.SUCCESS,
                        message: MESSAGE.EBILL.DELETE,
                    });
                    EBillLog.create(
                        {
                            type: CONSTANTS.TYPE_EBILL.DELETED,
                            member: memberId,
                        },
                        (err) => {}
                    );
                });
        };
        async.waterfall([checkParams, deleteBill], (err, data) => {
            if (_.isError(err)) {
                logger.logError([err], req.originalUrl, req.body);
            }
            err &&
                _.isError(err) &&
                (data = {
                    code: CONSTANTS.CODE.SYSTEM_ERROR,
                    message: MESSAGE.SYSTEM.ERROR,
                });
            res.json(data || err);
        });
    },
    getBill(req, res) {
        const memberId = req.user.id;
        const checkParams = (next) => {
            if (!memberId) {
                return next({
                    code: CONSTANTS.CODE.WRONG_PARAMS,
                    message: MESSAGE.SYSTEM.ERROR,
                });
            }
            next();
        };
        const getBill = (next) => {
            EBill.findOne({ member: memberId, active: 1 })
                .select("-member")
                .lean()
                .exec((err, result) => {
                    if (err) {
                        return next(err);
                    }
                    next({
                        code: CONSTANTS.CODE.SUCCESS,
                        data: result,
                    });
                });
        };
        async.waterfall([checkParams, getBill], (err, data) => {
            if (_.isError(err)) {
                logger.logError([err], req.originalUrl, req.body);
            }
            err &&
                _.isError(err) &&
                (data = {
                    code: CONSTANTS.CODE.SYSTEM_ERROR,
                    message: MESSAGE.SYSTEM.ERROR,
                });
            res.json(data || err);
        });
    },
};
