import { Router } from 'express';
import fsx from 'fs.extra';
import multipart from 'connect-multiparty';
const router = new Router();
const multipartMiddleware = multipart();
import moment from 'moment';

import { createID } from '../../helpers';

export default {

    createUpdate() {

        return router.post('/api/v1.0/app/create-new-hot-update', multipartMiddleware ,(req, res) => {

            req.checkBody('platform', 'platform is required').notEmpty();
            req.checkBody('nativeVersion', 'nativeVersion is required').notEmpty();

            let errors = req.validationErrors();

            if (errors) {
                return res.json({
                    code : 400,
                    error : util.inspect(errors)
                })
            }

            async.waterfall([
                callback => {
                    let extension = req.files.bundle['path'].split('.')[1];
                    let _path = '/uploads/hotupdate/' + createID() + '.' + extension;
                    fsx.copy(req.files.bundle.path, __dirname + '/public' + _path, {replace: true}, function (err) {
                        fsx.rmrfSync(req.files.bundle.path);
                        callback(err, _path);
                    });
                },

                (path, callback) => {

                    req.body.bundle = path;
                    req.body.created_time = moment().utcOffset(420).unix();
                    Hotupdate.create(req.body, (err) => {
                        if(err) {
                            return res.json({
                                code : 500,
                                error : 'Server Error',
                                login: cache.get('member_login_state')
                            })
                        }

                        callback(null, true)
                    })

                }
            ], (err, result) => {
                if(result) {
                    cachegoose.clearCache(`hotupdate:${req.body.platform}:${req.body.nativeVersion}`);
                    return res.json({code: 200});
                }
            })

        });

    },

    lastestUpdate() {

        return router.post('/api/v1.0/app/get-lastest-update' ,(req, res) => {

            req.checkBody('platform', 'platform is required').notEmpty();
            req.checkBody('nativeVersion', 'nativeVersion is required').notEmpty();

            let errors = req.validationErrors();

            if (errors) {
                return res.json({
                    code : 400,
                    error : util.inspect(errors)
                })
            }

            Hotupdate
                .findOne({platform: req.body.platform, nativeVersion: req.body.nativeVersion})
                .sort({created_time: -1})
                .lean()
                // .cache(0, `hotupdate:${req.body.platform}:${req.body.nativeVersion}`)
                .exec((err, newUpdate) => {
                    if(err) {
                        return res.json({
                            code : 500,
                            error : 'Server Error',
                            login: cache.get('member_login_state')
                        })
                    }

                    if(newUpdate) {
                        return res.json({
                            code: 200,
                            bundle: newUpdate.bundle,
                            created_time: newUpdate.created_time
                        });
                    } else {
                        return res.json({
                            code: 404,
                            status: 'Not found any files to update'
                        });
                    }
                })
        });

    }

}
