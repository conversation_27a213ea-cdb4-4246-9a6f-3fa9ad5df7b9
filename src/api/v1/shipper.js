import { Router } from 'express';
import _ from 'lodash';
const router = new Router();
const async = require('async')
import * as helpers from '../../helpers';
import CONSTANTS from '../../const';
import uuid from 'uuid/v4';

export default {
  getNearest() {
    return router.post('/api/v1.0/shipper/get-nearest', (req, res) => {
      const region = req.body.regionName;
      const location = req.body.location;
      const service = req.body.serviceId;

      if(!_.isPlainObject(location) || !_.isFinite(location.lat) || !_.isFinite(location.lng)) {
        return res.json({
          code: 200,
          data: []
        })
      }

      let radius = 1000;
      if(req.body.distance && req.body.distance > 1000) {
        radius = req.body.distance
      }

      const limit = 10
      let condition = {
        receivePushOrder: 1,
        training: 1,
        blockOrderUtil: {
          $lt: Date.now()
        }
      }
      const fields = '_id';
      const excepts = [];

      let shippers;
      const getShippersNearBy = (next) => {
        helpers.getNearest(location, radius, service, limit, (err, data) => {
          if(err) {
            return next(err);
          }

          shippers = data;

          next();
        })
      }

      const filterShipper = (next) => {
        let mapInf = {};
        let ids = [];
        shippers = shippers.filter((shipper) => {
          let valid = false;
          if(excepts.indexOf(shipper.userId) === -1) {
            mapInf[shipper.userId] = shipper
            ids.push(shipper.userId);

            valid = true
          }

          return valid;
        })

        const query = condition;
        query._id = {
          $in: ids
        }

        Members
          .find(query, fields)
          .lean()
          .exec((err, results) => {
            if(err) {
              return next(err)
            }

            shippers = results;

            shippers.forEach((shipper) => {
              shipper.location = {
                coordinates: [mapInf[shipper._id.toHexString()].location.lng, mapInf[shipper._id.toHexString()].location.lat]
              }
              shipper.bearing = mapInf[shipper._id.toHexString()].location.bearing;
            })
            shippers.forEach((shipper) => {
              shipper._id = uuid();
            })
            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: shippers
            });
          })
      }

      async.waterfall([
        getShippersNearBy,
        filterShipper
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      })
    })
  }
}
