import { Router } from 'express';
const router = new Router();
import cache from 'memory-cache';

export default {

    notify() {

        return router.post('/api/v1.0/app/notify' ,(req, res) => {

            req.checkBody('platform', 'platform is required').notEmpty();
            req.checkBody('notify_token', 'token is required').notEmpty();
            req.checkBody('memberToken', 'memberToken is required').notEmpty();

            let errors = req.validationErrors();

            if (errors) {
                return res.json({
                    code : 400,
                    error : util.inspect(errors)
                })
            }

            Members.findOne({memberToken: req.body.memberToken}, (err, member) => {

                if(err) {
                    return res.json({
                        code : 500,
                        error : 'Server Error',
                        login: cache.get('member_login_state')
                    })
                }

                if(member) {

                    Notifications.findOne({member: member._id, platform: req.body.platform}, (err, notify) => {

                        if(notify) {
                            notify.notify_token = req.body.notify_token;
                            notify.save();
                        } else {
                            let body = {
                                platform : req.body.platform,
                                notify_token: req.body.notify_token,
                                member : member._id,
                            }
                            Notifications.create(body);
                        }

                        return res.json({
                            code : 200
                        })

                    })

                } else {
                    return res.json({
                        code : 404,
                        error : 'User not found',
                        login: cache.get('member_login_state'),
                    })
                }

            })

        });

    },

}
