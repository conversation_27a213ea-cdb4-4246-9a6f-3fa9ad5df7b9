import _ from 'lodash';
import async from 'async';
import uuid from 'uuid/v4';
import util from 'util';
import rp from 'request-promise';
import CONSTANTS from '../../const';
import MESSAGE from '../../message';

export default {
  getConfig(req, res) {
    const newApprove = req.body.newApprove;

    return res.json({
      code: 200,
      data: {
        isOpenCard: newApprove ? 1 : 0,
        messageCard: "Hỗ trợ thẻ Viettel",
        isOpenBanking: 1,
        messageBanking: "Nạp qua ngân hàng nội địa",
        isOpenVimo: 1,
        messageVimo: "Miễn phí - nhận tiền ngay",
        isOpenMomo: 1,
        messageMomo: "Miễn phí - nhận tiền ngay",
        isOpenSMS: newApprove ? 0 : 1,
        messageSMS: "15k/1 ngày sử dụng",
        messageDirect: ""
      }
    })
  }
}
