import _ from 'lodash';
import async from 'async';
import util from 'util';
import CONSTANTS from '../../const';
import MESSAGE from '../../message';
import Joi from 'joi'
import fs from 'fs.extra'
import uuid from 'uuid/v4'
import rp from 'request-promise'
import { currentServerAddr, amazonS3Addr, cdnServerAddr } from '../../config'

export default {
  authen(req, res) {
    const userId = _.get(req, 'user.id', '');
    const avatar = _.get(req, 'body.avatar', '');
    const frontCer = _.get(req, 'body.frontCer', '');
    const backCer = _.get(req, 'body.backCer', '');
    const phone = _.get(req, 'body.phone', '');
    let oldData;
    let newData;

    const checkParams = (next) => {
      if (!avatar || !frontCer || !backCer || !phone) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGE.SYSTEM.ERROR
        })
      }

      next();
    }

    const getOldData = (next) => {
      PotentialPhone
        .findOne({
          phone
        })
        .lean()
        .exec((err, result) => {
          if (err) {
            return next(err);
          }

          oldData = result;

          next();
        })
    }

    const updateDb = (next) => {
      PotentialPhone
        .findOneAndUpdate({
          phone
        }, {
          phone,
          supplementImgs: [
            avatar,
            frontCer,
            backCer
          ],
          updatedAt: Date.now()
        }, {
          'new': true,
          upsert: true,
          setDefaultsOnInsert: true
        })
        .exec((err, result) => {
          if (err) {
            return next(err);
          }

          newData = result;

          next();
        })
    }

    const updateJob = (next) => {
      next({
        code: CONSTANTS.CODE.SUCCESS
      })

      AuthenticationJob
        .findOneAndUpdate({
          phone
        }, {
          potentialPhoneStatus: 2,
          updatedAt: Date.now()
        })
        .lean()
        .exec((err, result) => {
          if (result) {
            AuthenticationJobLog
              .create({
                job: result._id,
                action: 'SUPPLEMENT_POTENTIAL_PHONE',
                oldData,
                newData
              }, (err, result) => { })
          }
        })
    }

    async.waterfall([
      checkParams,
      getOldData,
      updateDb,
      updateJob
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGE.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  },

  getAuthenInf(req, res) {
    const userId = _.get(req, 'user.id', '');

    AuthenShopInf
      .findOne({ member: userId }, "-member")
      .lean()
      .exec((err, result) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        if (err) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGE.SYSTEM.ERROR
          })
        }

        res.json({
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        });
      })
  },

  modifyAuthenInf(req, res) {
    const userId = _.get(req, 'user.id', '');
    const id = _.get(req, 'body.id', '');
    const avatar = _.get(req, 'body.avatar', '');
    const frontCer = _.get(req, 'body.frontCer', '');
    const backCer = _.get(req, 'body.backCer', '');

    const checkParams = (next) => {
      if (!id) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGE.SYSTEM.ERROR
        })
      }

      next();
    }

    const updateAuthenInf = (next) => {
      const objUpdate = {
        status: 0,
        updatedAt: Date.now()
      }

      if (avatar) {
        objUpdate.avatar = avatar
      }

      if (frontCer) {
        objUpdate.frontCer = frontCer
      }

      if (backCer) {
        objUpdate.backCer = backCer
      }

      AuthenShopInf
        .findOneAndUpdate({
          _id: id,
          member: userId
        }, objUpdate, { 'new': false })
        .lean()
        .exec((err, result) => {
          if (err) {
            return next(err);
          }

          if (!result) {
            return next(new Error(`Not found authen inf`));
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS
          });
        })
    }

    async.waterfall([
      // checkParams,
      updateAuthenInf
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGE.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  },

  configAuthenShop(req, res) {
    const userId = _.get(req, 'user.id', '');

    AuthenticationJob
      .findOne({
        member: userId,
        potentialPhoneStatus: 0
      })
      .lean()
      .exec((err, result) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        if (err) {
          res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          })
        }

        if (result) {
          return res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              isOpen: 1,
              interest: []
            }
          });
        }

        res.json({
          code: CONSTANTS.CODE.SUCCESS,
          data: {
            isOpen: 0,
            interest: []
          }
        });
      })
  },
}
