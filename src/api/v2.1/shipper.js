import _ from 'lodash';
import async from 'async';
import util from 'util';
import CONSTANTS from '../../const';
import MESSAGE from '../../message';
import Joi from 'joi'
import fs from 'fs.extra'
import uuid from 'uuid/v4'
import {currentServerAddr, cmsServerAddr, mediaServerAddr, notifyServiceAddr} from '../../config'

export default {
  authen(req, res) {
    const region = req.body.regionName || 'hn';
    let id;

    const schemaInput = {
      name: Joi.string().allow(''),
      address: Joi.string().allow(''),
      accommodation: Joi.string().allow(''),
      phoneRelative1: Joi.string().allow(''),
      phoneRelative2: Joi.string().allow(''),
      avatar: Joi.string().allow(''),
      identityFacadeImg: Joi.string().allow(''),
      identityBacksideImg: Joi.string().allow(''),
      identityNum: Joi.string().allow(''),
      identityDate: Joi.string().allow(''),
      identityPlace: Joi.string().allow(''),
      dob: Joi.string().allow(''),
      drivingLicenseFacadeImg: Joi.string().allow(''),
      drivingLicenseNum: Joi.string().allow(''),
      registrationLicenseFacadeImg: Joi.string().allow(''),
      motobikeBacksideImg: Joi.string().allow(''),
      avatarWithIdentity: Joi.string().allow(''),
      licensePlate: Joi.string().allow(''),
      brand: Joi.string().allow(''),
      color: Joi.string().allow(''),
      note: Joi.string().allow('')
    }

    const checkParams = (next) => {
      const result = Joi.validate(req.body, schemaInput, {allowUnknown: true, convert: true});
      if(result.error) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGE.SYSTEM.ERROR
        })
      }

      next(null);
    }

    const generateArray = (type) => {
      if(req.body[type] && req.body[type].startsWith(mediaServerAddr)) {
        req.body[type] = req.body[type].replace(mediaServerAddr,"");
      }
      return [{
        value: req.body[type],
        valid: false,
        uploadedAt: Date.now()
      }]
    }

    const checkHasAuthen = (next) => {
      Members
      .count({ _id: req.user.id, 'ship.isAuthen':1 })
      .exec((err, count) => {
        if(err) {
          return next(err);
        }
        if(count) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Gửi thông tin thất bại do tài khoản tài xế của bạn đã được xác thực'
            }
          })
        }

        next();
      })
    }

    const createInDb = (next) => {
      const obj = {
        member: req.user.id,
        name: generateArray('name'),
        address: generateArray('address'),
        accommodation: generateArray('accommodation'),
        phoneRelative1: generateArray('phoneRelative1'),
        phoneRelative2: generateArray('phoneRelative2'),
        avatar: generateArray('avatar'),
        identityFacadeImg: generateArray('identityFacadeImg'),
        identityBacksideImg: generateArray('identityBacksideImg'),
        identityNum: generateArray('identityNum'),
        identityDate: generateArray('identityDate'),
        identityPlace: generateArray('identityPlace'),
        dob: generateArray('dob'),
        drivingLicenseFacadeImg: generateArray('drivingLicenseFacadeImg'),
        drivingLicenseNum: generateArray('drivingLicenseNum'),
        registrationLicenseFacadeImg: generateArray('registrationLicenseFacadeImg'),
        motobikeBacksideImg: generateArray('motobikeBacksideImg'),
        avatarWithIdentity: generateArray('avatarWithIdentity'),
        licensePlate: generateArray('licensePlate'),
        brand: generateArray('brand'),
        color: generateArray('color'),
        isTaxi: req.body.isTaxi || 0,
        hasDrivingLicense: req.body.hasDrivingLicense ? 1 : 0,
        hasRegistrationLicense: req.body.hasRegistrationLicense ? 1 : 0,
        hasIdentity: req.body.hasIdentity ? 1 : 0
      }

      if(req.body.note) {
        obj.note = generateArray('note');
      }

      obj.region = region

      AuthenShipInf.create(obj, (err, result) => {
        if(err) {
          return next(err);
        }

        id = result._id;

        return next();
      });
    }

    const pushToAdmin = (next) => {
      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: { _id: id }
      })

      Members
        .findOne({ _id: req.user.id }, 'phone')
        .lean()
        .exec((err, result) => {
          if (result && result.phone) {
            Config
              .get(CONSTANTS.CONFIG_TYPE.ADMIN_AUTHEN_ONLINE, region, (err, data) => {
                if (data && data.config && data.config.listAdmin) {
                  data.config.listAdmin.map((admin) => {
                    const options = {
                      method: 'POST',
                      uri: `${notifyServiceAddr}/api/v1.0/push-notification/member`,
                      body: {
                        userId: admin,
                        title: 'Thông tin',
                        message: `Tài xế ${result.phone} vừa gửi thông tin xác thực.`,
                      },
                      json: true // Automatically stringifies the body to JSON
                    };

                    request(options, (err, response) => { });
                  })
                }
              })
          }
        })
    }

    async.waterfall([
      checkParams,
      checkHasAuthen,
      createInDb,
      pushToAdmin
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGE.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  },
  sendContract(req, res) {
    // const userId = req.user.id;
    const id = req.body.id;

    const schemaInput = {
      contract: Joi.string(),
    }

    let currentAuthenInf;
    let objUpdate = {
      updatedAt: Date.now(),
      $push: {}
    }

    const options = {
      method: 'POST',
      uri: `${notifyServiceAddr}/api/v1.0/push-notification/member`,
      json: true // Automatically stringifies the body to JSON
    };

    const checkParams = (next) => {
      const result = Joi.validate(req.body, schemaInput, {allowUnknown: true, convert: true});

      if(result.error) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGE.SYSTEM.ERROR
        })
      }

      next(null);
    }

    const getCurrentAuthenInf = (next) => {
      AuthenShipInf
        .findOne({
          _id: req.body.id
        })
        .populate('member', 'phone')
        .lean()
        .exec((err, result) => {
          if(err || !result) {
            return next(err || new Error(`Not found authen inf`));
          }

          currentAuthenInf = result;
          if(currentAuthenInf.currentStep < CONSTANTS.STEP_AUTHEN.CONTRACT) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: {
                head: 'Thông báo',
                body: 'Bạn chưa được phép gửi hợp đồng khi chưa hoàn thành các bước trước'
              }
            })
          }
          if(currentAuthenInf.statusContract === 1) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: {
                head: 'Thông báo',
                body: 'Hợp đồng hiện tại đã được duyệt, bạn không cần gửi hợp đồng nữa.'
              }
            })
          }
          next();
        });
    }

    const generateObjectUpdate = (type) => {

      if(req.body[type] && req.body[type].startsWith(mediaServerAddr)) {
        req.body[type] = req.body[type].replace(mediaServerAddr,"");
      }

      objUpdate['$push'][type] = {
        value: req.body[type],
        valid: false,
        uploadedAt: Date.now()
      }
    }

    const updateAuthenInf = (next) => {
      if(req.body.contract) {
        generateObjectUpdate('contract');
      }

      if(currentAuthenInf.statusContract < 0) {
        objUpdate.statusContract = 0;
      }

      if (currentAuthenInf.shipperRejected) {
        objUpdate.shipperRejected = 0;
      }

      AuthenShipInf
        .findOneAndUpdate({
          _id: req.body.id,
          statusContract: {$lt: 0}
        }, objUpdate)
        .lean()
        .exec((err, result) => {
          if(err) {
            return next(err);
          }

          if(!result) {
            return next(new Error(`Not found authen inf`));
          }

          next();
        })
    }

    const pushToAdmin = (next) => {
      next(null, {
        code: CONSTANTS.CODE.SUCCESS
      });

      Config
        .get(CONSTANTS.CONFIG_TYPE.ADMIN_AUTHEN_ONLINE, currentAuthenInf.region, (err, data) => {
          if (data && data.config && data.config.listAdmin) {
            data.config.listAdmin.map((admin) => {
              options.body = {
                userId: admin,
                title: 'Hợp đồng',
                message: `Tài xế ${currentAuthenInf.member.phone} vừa gửi hợp đồng.`,
              }

              request(options, (err, response) => {});
            })
          }
        })
    }

    async.waterfall([
      checkParams,
      getCurrentAuthenInf,
      updateAuthenInf,
      pushToAdmin
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGE.SYSTEM.ERROR
      });

      options.body = {
        userId: currentAuthenInf.member.toString(),
        title: '',
        appName: 'driver',
        message: '',
        eventName: 'online_authen_update'
      }

      request(options, (err, response) => {});

      res.json(data || err);
    })
  },

  sendBill(req, res) {
    const userId = req.user.id;

    const schemaInput = {
      bill: Joi.string(),
      receiveUniformAddress: Joi.string().empty('').default(''),
      size: Joi.string().empty('').default(''),
    }

    let currentAuthenInf;
    let objUpdate = {
      sizeUniform: req.body.size,
      updatedAt: Date.now(),
      $push: {}
    }

    const checkParams = (next) => {
      const result = Joi.validate(req.body, schemaInput, {allowUnknown: true, convert: true});

      if(result.error) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGE.SYSTEM.ERROR
        })
      }

      next(null);
    }

    const getCurrentAuthenInf = (next) => {
      let objQuery = {
        member: userId
      }
      if(!req.body.receiveUniformAddress && req.body.regionName !== 'vietnam:hatinh') {
        objQuery.isTaxi = 1
      } else {
        objQuery.isTaxi = {
          $ne: 1
        }
      }
      AuthenShipInf
        .findOne(objQuery)
        .populate('member', 'phone')
        .lean()
        .exec((err, result) => {
          if(err || !result) {
            return next(err || new Error(`Not found authen inf`));
          }

          currentAuthenInf = result;
          if(currentAuthenInf.currentStep < CONSTANTS.STEP_AUTHEN.PAYMENT) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: {
                head: 'Thông báo',
                body: 'Bạn chưa được phép gửi thông tin thanh toán khi chưa hoàn thành các bước trước'
              }
            })
          }
          // if(currentAuthenInf.statusPayment === 1) {
          //   return next({
          //     code: CONSTANTS.CODE.FAIL,
          //     message: {
          //       head: 'Thông báo',
          //       body: 'Hóa đơn thanh toán đồng phục đã được duyệt, bạn không cần gửi thêm thông tin nữa.'
          //     }
          //   })
          // }
          next();
        });
    }

    const generateObjectUpdate = (type) => {

      if(req.body[type] && req.body[type].startsWith(mediaServerAddr)) {
        req.body[type] = req.body[type].replace(mediaServerAddr,"");
      }

      objUpdate['$push'][type] = {
        value: req.body[type],
        valid: false,
        uploadedAt: Date.now()
      }
    }

    const updateAuthenInf = (next) => {

      if(req.body.receiveUniformAddress) {
        generateObjectUpdate('receiveUniformAddress');
      }

      if(req.body.bill) {
        generateObjectUpdate('bill');
      }

      let query = {
        _id: currentAuthenInf._id
      }

      if (currentAuthenInf.statusPayment !== 1) {
        if(currentAuthenInf.statusPayment < 0) {
          objUpdate.statusPayment = 0;
        }

        if (currentAuthenInf.shipperRejected) {
          objUpdate.shipperRejected = 0;
        }

        query.statusPayment = {$lt: 0};
      }

      AuthenShipInf
        .findOneAndUpdate(query, objUpdate)
        .lean()
        .exec((err, result) => {
          if(err) {
            return next(err);
          }

          if(!result) {
            return next(new Error(`Not found authen inf`));
          }

          next();
        })
    }

    const pushToAdmin = (next) => {
      next(null, {
        code: CONSTANTS.CODE.SUCCESS
      });

      Config
        .get(CONSTANTS.CONFIG_TYPE.ADMIN_AUTHEN_ONLINE, currentAuthenInf.region, (err, data) => {
          if (data && data.config && data.config.listAdmin) {
            data.config.listAdmin.map((admin) => {
              const options = {
                method: 'POST',
                uri: `${notifyServiceAddr}/api/v1.0/push-notification/member`,
                body: {
                  userId: admin,
                  title: 'Chuyển tiền đp',
                  message: `Tài xế ${currentAuthenInf.member.phone} vừa chuyển khoản tiền đồng phục.`,
                },
                json: true // Automatically stringifies the body to JSON
              };

              request(options, (err, response) => {});
            })
          }
        })
    }

    async.waterfall([
      checkParams,
      getCurrentAuthenInf,
      updateAuthenInf,
      pushToAdmin
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGE.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  },

  sendUniform(req, res) {
    const userId = req.user.id;

    const schemaInput = {
      uniform: Joi.string()
    }

    let currentAuthenInf;
    let objUpdate = {
      requireUniform: 0,
      updatedAt: Date.now(),
      $push: {}
    }

    const checkParams = (next) => {
      const result = Joi.validate(req.body, schemaInput, {allowUnknown: true, convert: true});

      if(result.error) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGE.SYSTEM.ERROR
        })
      }

      next(null);
    }

    const getCurrentAuthenInf = (next) => {
      AuthenShipInf
        .findOne({
          // _id: req.body.id,
          member: userId
        })
        .populate('member', 'phone')
        .lean()
        .exec((err, result) => {
          if(err || !result) {
            return next(err || new Error(`Not found authen inf`));
          }

          currentAuthenInf = result;
          if(currentAuthenInf.currentStep < CONSTANTS.STEP_AUTHEN.FINISH) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: {
                head: 'Thông báo',
                body: 'Bạn chưa được phép gửi đồng phục khi chưa hoàn thành các bước trước'
              }
            })
          }
          if(currentAuthenInf.statusUniform === 1) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: {
                head: 'Thông báo',
                body: 'Ảnh đồng phục đã được duyệt, bạn không cần gửi thêm thông tin nữa.'
              }
            })
          }
          next();
        });
    }

    const generateObjectUpdate = (type) => {

      if(req.body[type] && req.body[type].startsWith(mediaServerAddr)) {
        req.body[type] = req.body[type].replace(mediaServerAddr,"");
      }

      objUpdate['$push'][type] = {
        value: req.body[type],
        valid: false,
        uploadedAt: Date.now()
      }
    }

    const updateAuthenInf = (next) => {

      if(req.body.uniform) {
        generateObjectUpdate('uniform');
      }

      if(currentAuthenInf.statusUniform < 0) {
        objUpdate.statusUniform = 0;
      }

      if (currentAuthenInf.shipperRejected) {
        objUpdate.shipperRejected = 0;
      }

      AuthenShipInf
        .findOneAndUpdate({
          // _id: req.body.id,
          member: req.user.id,
          statusUniform: {$lt: 0}
        }, objUpdate)
        .lean()
        .exec((err, result) => {
          if(err) {
            return next(err);
          }

          if(!result) {
            return next(new Error(`Not found authen inf`));
          }

          next();
        })
    }

    const pushToAdmin = (next) => {
      next(null, {
        code: CONSTANTS.CODE.SUCCESS
      });

      Config
        .get(CONSTANTS.CONFIG_TYPE.ADMIN_AUTHEN_ONLINE, currentAuthenInf.region, (err, data) => {
          if (data && data.config && data.config.listAdmin) {
            data.config.listAdmin.map((admin) => {
              const options = {
                method: 'POST',
                uri: `${notifyServiceAddr}/api/v1.0/push-notification/member`,
                body: {
                  userId: admin,
                  title: 'Đồng phục',
                  message: `Tài xế ${currentAuthenInf.member.phone} vừa gửi ảnh đồng phục.`,
                },
                json: true // Automatically stringifies the body to JSON
              };

              request(options, (err, response) => {});
            })
          }
        })
    }

    async.waterfall([
      checkParams,
      getCurrentAuthenInf,
      updateAuthenInf,
      pushToAdmin
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGE.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  },
  getAuthenInfNew(req, res) {
    const userId = req.user.id;
    const isTaxi = req.body.isTaxi;
    let authenInf;
    const getAuthenInf = (next) => {
      let query = {
        member: userId
      }
      if(isTaxi) {
        query.isTaxi = 1;
      } else {
        query.isTaxi = {
          $ne: 1
        }
      }
      AuthenShipInf
        .findOne(query)
        .lean()
        .exec((err, result) => {
          if(err) {
            return next(err);
          }

          authenInf = result;

          next();
        });
    }

    const transferData = (next) => {
      if(!authenInf) {
        return next(null, {
          code: CONSTANTS.CODE.SUCCESS
        })
      }

      const keysText = ['name', 'address', 'email', 'facebookUrl', 'identityNum', 'identityDate', 'licensePlate', 'drivingLicenseNum', 'note', 'message', 'accommodation', 'phoneRelative1', 'phoneRelative2', 'receiveUniformAddress', 'brand', 'color', 'identityPlace', 'dob'];
      const keysImage = ['avatar', 'facebookImg', 'identityFacadeImg','identityBacksideImg', 'drivingLicenseFacadeImg', 'drivingLicenseBacksideImg', 'motobikeFacadeImg', 'motobikeBacksideImg', 'registrationLicenseFacadeImg', 'registrationLicenseBacksideImg', 'avatarWithIdentity', 'bill', 'contract', 'uniform'];

      keysText.forEach((key) => {
        if(authenInf && authenInf[key] && authenInf[key].length) {
          const value = authenInf[key];

          if(key === 'message') {
            authenInf[key] = `${value[value.length-1]}`;
          } else {
            authenInf[key] = `${value[value.length-1].value}`;
          }
        } else {
          authenInf[key] = "";
        }
      });

      keysImage.forEach((key) => {
        if(authenInf && authenInf[key] && authenInf[key].length) {
          const value = authenInf[key];
          if(value[value.length-1].value.startsWith("http") || value[value.length-1].value.startsWith("https")) {
            authenInf[key] = value[value.length-1].value;
          } else {
            if(value[value.length-1].value.includes('shipInf')) {
              authenInf[key] = `${currentServerAddr}${value[value.length-1].value}`
            } else {
              authenInf[key] = `${mediaServerAddr}${value[value.length-1].value}`
            }
          }
        } else {
          authenInf[key] = "";
        }
      })

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: authenInf
      });
    }

    async.waterfall([
      getAuthenInf,
      transferData
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGE.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  },
  updateAuthenInf(req, res) {
    const userId = req.user.id;
    const region = req.body.regionName || 'hn';

    const schemaInput = {
      id: Joi.string().required(),
      name: Joi.string().allow(''),
      address: Joi.string().allow(''),
      accommodation: Joi.string().allow(''),
      phoneRelative1: Joi.string().allow(''),
      phoneRelative2: Joi.string().allow(''),
      avatar: Joi.string().allow(''),
      identityFacadeImg: Joi.string().allow(''),
      identityBacksideImg: Joi.string().allow(''),
      identityNum: Joi.string().allow(''),
      identityDate: Joi.string().allow(''),
      identityPlace: Joi.string().allow(''),
      dob: Joi.string().allow(''),
      drivingLicenseFacadeImg: Joi.string().allow(''),
      drivingLicenseNum: Joi.string().allow(''),
      registrationLicenseFacadeImg: Joi.string().allow(''),
      motobikeBacksideImg: Joi.string().allow(''),
      avatarWithIdentity: Joi.string().allow(''),
      licensePlate: Joi.string().allow(''),
      brand: Joi.string().allow(''),
      color: Joi.string().allow(''),
      note: Joi.string().allow('')
    }

    let currentAuthenInf;
    let objUpdate = {
      updatedAt: Date.now(),
      $push: {}
    }

    const checkParams = (next) => {
      const result = Joi.validate(req.body, schemaInput, {allowUnknown: true, convert: true});

      if(result.error) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGE.SYSTEM.ERROR
        })
      }

      next(null);
    }

    const getCurrentAuthenInf = (next) => {
      AuthenShipInf
        .findOne({
          _id: req.body.id,
          member: userId
        })
        .lean()
        .exec((err, result) => {
          if(err || !result) {
            return next(err || new Error(`Not found authen inf`));
          }

          currentAuthenInf = result;

          next();
        });
    }

    const generateObjectUpdate = (type) => {

      if(req.body[type] && req.body[type].startsWith(mediaServerAddr)) {
        req.body[type] = req.body[type].replace(mediaServerAddr,"");
      }

      objUpdate['$push'][type] = {
        value: req.body[type],
        valid: false,
        uploadedAt: Date.now()
      }
    }

    const updateAuthenInf = (next) => {

      if(req.body.name) {
        generateObjectUpdate('name');
      }

      if(req.body.address) {
        generateObjectUpdate('address');
      }
      if(req.body.avatar) {
        generateObjectUpdate('avatar');
      }
      if(req.body.identityFacadeImg) {
        generateObjectUpdate('identityFacadeImg');
      }
      if(req.body.identityBacksideImg) {
        generateObjectUpdate('identityBacksideImg');
      }
      if(req.body.identityNum) {
        generateObjectUpdate('identityNum');
      }
      if(req.body.identityDate) {
        generateObjectUpdate('identityDate');
      }
      if(req.body.identityPlace) {
        generateObjectUpdate('identityPlace');
      }
      if(req.body.dob) {
        generateObjectUpdate('dob');
      }
      if(req.body.drivingLicenseFacadeImg) {
        generateObjectUpdate('drivingLicenseFacadeImg');
      }
      if(req.body.drivingLicenseNum) {
        generateObjectUpdate('drivingLicenseNum');
      }
      if(req.body.registrationLicenseFacadeImg) {
        generateObjectUpdate('registrationLicenseFacadeImg');
      }
      if(req.body.motobikeBacksideImg) {
        generateObjectUpdate('motobikeBacksideImg');
      }
      if(req.body.avatarWithIdentity) {
        generateObjectUpdate('avatarWithIdentity');
      }
      if(req.body.licensePlate) {
        generateObjectUpdate('licensePlate');
      }
      if(req.body.brand) {
        generateObjectUpdate('brand');
      }
      if(req.body.color) {
        generateObjectUpdate('color');
      }
      if(req.body.note) {
        generateObjectUpdate('note');
      }
      if(req.body.accommodation) {
        generateObjectUpdate('accommodation');
      }
      if(req.body.phoneRelative1) {
        generateObjectUpdate('phoneRelative1');
      }
      if(req.body.phoneRelative2) {
        generateObjectUpdate('phoneRelative2');
      }

      if(currentAuthenInf.status === -1) {
        objUpdate.status = 0;
      } else if(currentAuthenInf.status === 1) {
        objUpdate.status = 2;
      } else if(currentAuthenInf.status === -2) {
        objUpdate.status = 2;
      }

      objUpdate.region = region;

      objUpdate.hasDrivingLicense = req.body.hasDrivingLicense ? 1 : 0;
      objUpdate.hasRegistrationLicense = req.body.hasRegistrationLicense ? 1 : 0;
      objUpdate.hasIdentity = req.body.hasIdentity ? 1 : 0;

      AuthenShipInf
        .findOneAndUpdate({
          _id: req.body.id,
          member: req.user.id,
          status: currentAuthenInf.status
        }, objUpdate, {'new': false})
        .lean()
        .exec((err, result) => {
          if(err) {
            return next(err);
          }

          if(!result) {
            return next(new Error(`Not found authen inf`));
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS
          });
        })
    }

    async.waterfall([
      checkParams,
      getCurrentAuthenInf,
      updateAuthenInf
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGE.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  },
  getConfigAuthen(req,res) {
    const region = req.body.regionName || '';

    if(!region) {
      return res.json({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    Config
      .get(CONSTANTS.CONFIG_TYPE.SHIPPER_AUTHEN_GUIDE, region, (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        if(err || !data) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGE.SYSTEM.ERROR
          })
        }

        let jsonRes = {
          code: CONSTANTS.CODE.SUCCESS,
          data: data.config
        }

        res.json(jsonRes)
      })
  },
  getStatusUniform(req, res) {
    const userId = req.user.id;

    const getStatusUniform = (next) => {
      AuthenShipInf
        .findOne({
          member: userId
        }, 'requireUniform uniform statusUniform messageUniform')
        .lean()
        .exec((err, result) => {
          if (err) {
            return next(err);
          }

          if (!result || !result.requireUniform) {
            return next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                status: 0
              }
            });
          }

          if (result.uniform && result.uniform.length) {
            const value = result.uniform;
            if (value[value.length - 1].value.startsWith('http') || value[value.length - 1].value.startsWith('https')) {
              result.uniform = value[value.length - 1].value;
            } else {
              if (value[value.length - 1].value.includes('shipInf')) {
                result.uniform = `${currentServerAddr}${value[value.length - 1].value}`
              } else {
                result.uniform = `${mediaServerAddr}${value[value.length - 1].value}`
              }
            }
          } else {
            result.uniform = '';
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              status: result.requireUniform,
              uniform: result.uniform,
              statusUniform: result.statusUniform,
              messageUniform: result.messageUniform
            }
          });
        });
    }

    async.waterfall([
      getStatusUniform
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGE.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  },
  getStatusBuyUniform(req, res) {
    const userId = req.user.id;
    let status = 0

    const getStatusBuyUniform = (next) => {
      AuthenShipInf
        .findOne({
          member: userId
        }, 'requireBuyUniform')
        .lean()
        .exec((err, result) => {
          if (err) {
            return next(err);
          }

          if (result && result.requireBuyUniform) {
            status = result.requireBuyUniform
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              status
            }
          });
        });
    }

    async.waterfall([
      getStatusBuyUniform
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGE.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  },
}
