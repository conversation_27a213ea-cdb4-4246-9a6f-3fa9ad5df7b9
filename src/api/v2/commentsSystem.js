/**
 * Created by <PERSON><PERSON> on 2/4/2017.
 */
import CONSTANTS from '../../const';
import MESSAGE from '../../message';
import { Router } from 'express';
import uuid from 'uuid/v4';
import moment from 'moment';
import * as config from '../../config';
import cache from 'memory-cache';

const router = new Router();
var Util = require('../../Util');

export default {

    postComment(req, res) {

        console.log("Starttttttttttt");
        req.checkBody('memberToken', 'memberToken is required').notEmpty();
        req.checkBody('message', 'message is required').notEmpty();
        req.checkBody('feed_id', 'feed_id is required').notEmpty();

        let errors = req.validationErrors();

        if (errors) {
            return res.json({
                code: 400,
                error: util.inspect(errors),
                login: cache.get('member_login_state')
            })
        }

        console.log("Starttttttttttt");
        const message = _.get(req, 'body.message', '');
        const feed_id = req.body.feed_id;
        const parent_comment_id = req.body.parent_comment_id;

        if(!message) {
            return res.json({
                code: CONSTANTS.CODE.WRONG_PARAMS,
                message: MESSAGE.COMMENT.MESSAGE_EMPTY
            });
        }

        if(!feed_id) {
            return res.json({
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: MESSAGE.SYSTEM.ERROR
            });
        }

        Members.findOne({memberToken: req.body.memberToken}, (err, member) => {
            if(err) {
                return res.json({
                    code : 500,
                    error : 'Server Error',
                    login: cache.get('member_login_state')
                })
            }
            console.log("Found member");
            if(member) {

                const newCmt_id = uuid();
                var key = CONSTANTS.COMMENTS.KEY + feed_id +
                    ((parent_comment_id == null || parent_comment_id == "") ? "":":cmt"+parent_comment_id)
                    + ":cmt" +newCmt_id;
                console.log("keyyyyyyyy: "+key);
                let postObj = {
                    comment_id: newCmt_id,
                    feed_id:  feed_id,
                    parent_comment_id: parent_comment_id,
                    member: member._id,
                    message: message,
                    salary : 0,
                    phone : "",
                    created_time: moment().utcOffset(420).unix(),
                }

                // //Insert into MongoDb
                // console.log("Started insert into MongoDB, id"+postObj.member);
                // NewFeedSystem.create(postObj, (err, feed) => {
                //
                //     return res.json({
                //         code: 200,
                //         newfeed: feed,
                //         login: cache.get('member_login_state'),
                //     })
                // });
                // console.log("Inserted into MongoDB, id"+postObj.member);
                // Insert into Redis Cache
                console.log("Started insert into Redis, id"+postObj.member);
                const data =  JSON.stringify(postObj);
                redisConnection.set(key, data, (err, res) => {
                    console.log(`redis:set`, err, res);
                });

                return res.json({
                    code: 200,
                    newfeed: postObj,
                    login: cache.get('member_login_state'),
                })

            } else {

                return res.json({
                    code : 404,
                    error : 'User not found',
                    login: cache.get('member_login_state'),
                })

            }
        });
    },

    getCommentsByFeedID(req, res) {
        const feed_id = _.get(req, 'body.feed_id', '');
        if(!feed_id) {
            return res.json({
                code : CONSTANTS.CODE.WRONG_PARAMS,
                // message: message.SYSTEM.ERROR
            });
        }

        getCommentOfFeed(feed_id, (err, comments) => {
            if(err != null || err != '') {
                console.log("error: '" + err + "'");
                return res.json({
                    code : CONSTANTS.CODE.SYSTEM_ERROR
                });
            }

            res.json({
                code: CONSTANTS.CODE.SUCCESS,
                comments: comments
            });
        });
    }
}

function getCommentOfFeed(feed_id, callback) {
    var allComments = [];
    const key = CONSTANTS.COMMENTS.KEY+feed_id+"*";
    console.log("key: "+key);
    redisConnection.multi()
        .keys(key, function (err, feedsSys) {
            // NOTE: code in this callback is NOT atomic
            // this only happens after the the .exec call finishes.

            console.log("Total: " + feedsSys.length + " Comments");

            feedsSys.forEach(function (feed, index) {
                console.log("Feed " + index + ": " + feed.toString());
            });
        })
        .exec(function (err, feedsSys) {
            callback(allComments);
            if(err)
                console.log("ERROR: ", err, feedsSys);
        });
}
