import _ from 'lodash';
import CONSTANTS from '../../const';
import MESSAGE from '../../message';
import { Router } from 'express';
import uuid from 'uuid/v4';
import moment from 'moment';
import * as config from '../../config';
import cache from 'memory-cache';

const router = new Router();
var Util = require('../../Util');

export default {
    getInitFeeds (req, res) {
      let location = _.get(req, 'body.regionName', '');
      let key = `feeds:${location}`;
    
      redisConnection.lrange(key, 0, 8, function (err, posts) {
        let newFeeds = [];

        if(!err) {
          posts.forEach((post) => {
            post = JSON.parse(post);
            if(!post.from.totalPost) {
              post.from.totalPost = _.get(post, 'from.memberInfo.shop.totalPost', 1);
              if(!post.location && post.origin_place) {
                post.location = {
                  "lat": post.origin_place.geometry[0].geometry[1],
                  "lng": post.origin_place.geometry[0].geometry[0]
                }
              }
            }

            newFeeds.push(post);
          })
        }

        res.json({
          code: CONSTANTS.CODE.SUCCESS,
          feeds: newFeeds
        })
      })
    },

    getNewFeeds(req, res) {
        let limit = req.body.limit ? parseInt(req.body.limit) - 1 : 79;
        getFeedsSystem(function (allFeeds) {
            getFeedsFB(limit, function (feedsFB) {
                var result = allFeeds.concat(feedsFB);
                res.json({
                    code: CONSTANTS.CODE.SUCCESS,
                    feeds: result || []
                });
            })
        })
    },

    checkBlackPhone(req, res) {
      const phones = _.get(req, 'body.phones', []);
      BlackPhone
        .find({
          phone: {
            $in: phones
          }
        })
        .lean()
        .exec((err, results) => {
          const resObj = {
            code: 200,
            data: []
          }

          if(!err) {
            results.forEach((result) => {
              resObj.data.push(result.phone);
            });
          }

          res.json(resObj);
        })
    },

    getHistoryOrder(req, res) {
      const id = _.get(req, 'body.id', '');
      const phone = _.get(req, 'body.phone', '');

      const checkPhot = (next) => {
        if(!phone) {
          return next(null);
        }

        BlackPhone
          .findOne({
            phone: phone
          })
          .lean()
          .exec(next)
      }

      const getOrders = (next) => {
        let query = {"$or": []};

        if(phone) {
          query["$or"].push({
            phone
          })
        }

        if(id) {
          query["$or"].push({
            "from.id": id
          });

          query["$or"].push({
            "from.realId": id
          });
        }

        const options = {
          limit: 50,
          sort: {
            created_time: -1
          }
        }

        NewFeeds
          .find(query, null, options)
          .lean()
          .exec(next)
      }

      async.parallel([
        checkPhot,
        getOrders
      ], (err, results) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        if(err) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGE.SYSTEM.ERROR
          })
        }

        res.json({
          code: CONSTANTS.CODE.SUCCESS,
          feeds: results[1],
          isPhot: results[0] ? true : false
        })
      })
    },

    getStatistic(req, res) {
      const id = _.get(req, 'body.id', '');
      const phone = _.get(req, 'body.phone', '');
      let totalFollowId = 0;
      let totalFollowPhone = 0;

      const getFollowId = (next) => {
        if(!id) {
          return next(null);
        }

        StatisticFeedFacebook
          .find({id})
          .lean()
          .exec((err, results) => {
            if(err) {
              return next(err);
            }

            results.forEach((result) => {
              totalFollowId += result.count;
            });

            next();
          })
      }

      const getFollowPhone = (next) => {
        if(!phone) {
          return next(null);
        }

        StatisticFeedFacebook
          .find({phone})
          .lean()
          .exec((err, results) => {
            if(err) {
              return next(err);
            }

            results.forEach((result) => {
              totalFollowPhone += result.count;
            });

            next();
          })
      }

      async.parallel([
        getFollowId,
        getFollowPhone
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        if(err) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGE.SYSTEM.ERROR
          });
        }

        res.json({
          code: CONSTANTS.CODE.SUCCESS,
          data: {
            totalFollowId,
            totalFollowPhone
          }
        });
      });
    },

    createNewFeed() {

        return router.post('/api/v2.0/shop/create', (req, res) => {

            // req.checkBody('memberToken', 'memberToken is required').notEmpty();
            req.checkBody('phone', 'phone is required').notEmpty();
            req.checkBody('salary', 'salary is required').notEmpty();
            req.checkBody('deposit', 'deposit is required').notEmpty();
            req.checkBody('origin_place', 'origin_place is required').notEmpty();
            req.checkBody('destination_places', 'origin_place is required').notEmpty();
            req.checkBody('distance', 'distance is required').notEmpty();

            let errors = req.validationErrors();

            if (errors) {
                return res.json({
                    code : 400,
                    error : util.inspect(errors),
                    login: cache.get('member_login_state')
                })
            }

            Members.findOne({memberToken: req.body.memberToken}, (err, member) => {

                if(err) {
                    return res.json({
                        code : 500,
                        error : 'Server Error',
                        login: cache.get('member_login_state')
                    })
                }

                if(member) {

                    let postObj = {
                        feed_id: uuid(),
                        feed_facebook_id: 0,
                        group_id: 0,
                        member: member._id,
                        origin_place : req.body.origin_place,
                        destination_places : req.body.destination_places,
                        distance: req.body.distance,
                        deposit: req.body.deposit,
                        salary: req.body.salary,
                        note: req.body.note ? req.body.note : null,
                        phone: req.body.phone,
                        created_time: moment().utcOffset(420).unix(),
                        lasttime_push: Util.getCurrentTime(),
                        system_push_count: 0,
                        shop_push_count: 0,
                        total_comment: 0,
                        lasttime_comment: 0,
                        type: 1,
                        status: 0,
                        error: req.body.error ? req.body.error : null,
                    }

                    // //Insert into MongoDb

                    NewFeedSystem.create(postObj, (err, feed) => {
                        if(err) {
                            console.log("Error: "+err);
                            return res.json({
                                code : 500,
                                error : 'Server Error',
                                login: cache.get('member_login_state')
                            })
                        }
                        const data =  JSON.stringify(postObj);
                        redisConnection.set(`feedsSystem:${postObj.feed_id}`, data, (err, res) => {

                        });

                        return res.json({
                            code: 200,
                            message: "Tạo đơn thành công",
                            newfeed: postObj,
                            login: cache.get('member_login_state'),
                        })
                    });


                } else {

                    return res.json({
                        code : 404,
                        error : 'User not found',
                        login: cache.get('member_login_state'),
                    })

                }
            });
        });
    },

    pushFeed(req, res) {
        return router.post('/api/v2.0/shop/push', (req, res) => {
            req.checkBody('feed_id', 'feed_id is required').notEmpty();

            let errors = req.validationErrors();

            if (errors) {
                return res.json({
                    code: 400,
                    error: util.inspect(errors),
                    login: cache.get('member_login_state')
                })
            }
            // Feed_id
            const feed_id = req.body.feed_id;

            redisConnection.get(CONSTANTS.FEEDS.KEYS.FEEDS_DONE + feed_id, (err, feed) => {
                if (err) {
                    return res.json({
                        code: CONSTANTS.CODE.FEED_NOT_FOUND,
                        message: MESSAGE.FEEDS.NOT_FOUND
                    });
                }
                // Reset System_pust, Incre Shop_push
                const post = JSON.parse(feed);
                if(post) {
                    post.system_push_count = 0; // Descre system push count
                    post.shop_push_count++; // Incre shop_push_count
                    post.lasttime_push = Util.getCurrentTime();
                    post.status = CONSTANTS.FEEDS.STATUS.PROCESSING;
                    const data = JSON.stringify(post);
                    redisConnection.del(CONSTANTS.FEEDS.KEYS.FEEDS_DONE + feed_id); // Remove from Done cache
                    redisConnection.set(CONSTANTS.FEEDS.KEYS.FEEDS_PROCESSING + feed_id, data, function (err, res) {

                    });
                    // Update mongoDB
                    NewFeedSystem.findOneAndUpdate(
                        { feed_id: post.feed_id },
                        {
                            system_push_count: post.system_push_count,
                            shop_push_count: post.shop_push_count,
                            lasttime_push: post.lasttime_push,
                            status: post.status
                        },
                        function(err, user) {
                        if (err) throw err;
                            if (err){
                                return res.json({
                                    code: CONSTANTS.CODE.SYSTEM_ERROR,
                                    message: message.SYSTEM.ERROR
                                });
                            }else{
                                return res.json({
                                    code: 200,
                                    message: "Đẩy đơn thành công"
                                });
                            }
                    });
                }
                else{
                    // Feed still live or done already
                    return res.json({
                        code: CONSTANTS.CODE.FEED_NOT_FOUND,
                        message: MESSAGE.FEEDS.NOT_FOUND
                    });
                }
            });
        });
    },

    doneFeed(req, res) {
        return router.post('/api/v2.0/shop/done', (req, res) => {
            req.checkBody('feed_id', 'feed_id is required').notEmpty();

            let errors = req.validationErrors();

            if (errors) {
                return res.json({
                    code: 400,
                    error: util.inspect(errors),
                    login: cache.get('member_login_state')
                })
            }
            // Feed_id
            const feed_id = req.body.feed_id;

            redisConnection.get(CONSTANTS.FEEDS.KEYS.FEEDS_PROCESSING + feed_id, (err, feed) => {
                if (err) {
                    return res.json({
                        code: CONSTANTS.CODE.FEED_NOT_FOUND,
                        message: MESSAGE.FEEDS.DONE_ALREADY
                    });
                }
                // Reset System_pust, Incre Shop_push
                const post = JSON.parse(feed);
                if(post) {
                    post.lasttime_push = Util.getCurrentTime();
                    post.status = CONSTANTS.FEEDS.STATUS.DONE;
                    const data = JSON.stringify(post);
                    redisConnection.del(CONSTANTS.FEEDS.KEYS.FEEDS_PROCESSING + feed_id); // Remove from Processing cache
                    redisConnection.set(CONSTANTS.FEEDS.KEYS.FEEDS_DONE + feed_id, data, function (err, res) {
                        console.log(`redis:lpush`, err, res);
                    });
                    // Update mongoDB
                    NewFeedSystem.findOneAndUpdate(
                        { feed_id: post.feed_id },
                        {
                            lasttime_push: post.lasttime_push,
                            status: post.status
                        },
                        function(err, user) {
                            if (err) throw err;
                            if (err){
                                return res.json({
                                    code: CONSTANTS.CODE.SYSTEM_ERROR,
                                    message: message.SYSTEM.ERROR
                                });
                            }else{
                                return res.json({
                                    code: 200,
                                    message: "Done đơn thành công"
                                });
                            }
                        });
                }
                else{
                    // Feed still live or done already
                    return res.json({
                        code: CONSTANTS.CODE.FEED_NOT_FOUND,
                        message: MESSAGE.FEEDS.DONE_ALREADY
                    });
                }
            });
        });
    },

    getFeedsByShop() {

        return router.post('/api/v2.0/shop/get-feeds', (req, res) => {

            req.checkBody('memberToken', 'memberToken is required').notEmpty();
            req.checkBody('status', 'status is required').notEmpty();

            let errors = req.validationErrors();

            if (errors) {
                return res.json({
                    code : 400,
                    error : util.inspect(errors),
                    login: cache.get('member_login_state')
                })
            }

            Members.findOne({memberToken : req.body.memberToken}, (err, member) => {

                if(member) {

                    NewFeedSystem.find({member: member._id, status: req.body.status }, (err, feedsSystem) => {

                        return res.json({
                            code : 200,
                            feedsSystem : feedsSystem,
                            login: cache.get('member_login_state'),
                        })
                    });
                } else {

                    return res.json({
                        code : 404,
                        error : 'User not found',
                        login: cache.get('member_login_state'),
                    })
                }
            });
        });
    },

    deleteAFeed(req, res) {
        return router.post('/api/v2.0/shop/delete-feed', (req, res) => {
            req.checkBody('memberToken', 'memberToken is required').notEmpty();
            req.checkBody('feed_id', 'feed_id is required').notEmpty();

            let errors = req.validationErrors();
            if (errors) {
                return res.json({
                    code : 400,
                    error : util.inspect(errors),
                    login: cache.get('member_login_state')
                })
            }

            const feed_id = req.body.feed_id;
            // Update mongoDB
            NewFeedSystem.findOneAndUpdate(
                { feed_id: feed_id },
                {
                    status: CONSTANTS.FEEDS.STATUS.DELETED
                },
                function(err, user) {
                    if (err) throw err;
                    if (err){
                        return res.json({
                            code: CONSTANTS.CODE.SYSTEM_ERROR,
                            message: message.SYSTEM.ERROR
                        });
                    }
                    else{
                        redisConnection.del(CONSTANTS.FEEDS.KEYS.FEEDS_PROCESSING + feed_id); // Remove from Processing cache
                        redisConnection.del(CONSTANTS.FEEDS.KEYS.FEEDS_DONE + feed_id); // Remove from Processing cache
                        return res.json({
                            code: 200,
                        });
                    }
                });
        });
    },

    deleteAllFeeds(req, res) {
        return router.post('/api/v2.0/shop/delete-all', (req, res) => {
            req.checkBody('memberToken', 'memberToken is required').notEmpty();

            let errors = req.validationErrors();

            if (errors) {
                return res.json({
                    code : 400,
                    error : util.inspect(errors),
                    login: cache.get('member_login_state')
                })
            }

            Members.findOne({ memberToken: req.body.memberToken }, (err, member) => {

                if(err) {
                    return res.json({
                        code : 500,
                        error : 'Server Error',
                        login: cache.get('member_login_state')
                    })
                };

                if(member) {

                    NewFeedSystem.find(
                        {
                            member: member._id,
                            $or:[ {status:CONSTANTS.FEEDS.STATUS.PROCESSING}, {status: CONSTANTS.FEEDS.STATUS.DONE} ]
                        },
                        function(err, feeds) {
                        feeds.forEach(function(feed) {
                            feed.status = CONSTANTS.FEEDS.STATUS.DELETED;
                            feed.save();
                            //Remove from Redis
                            redisConnection.del(CONSTANTS.FEEDS.KEYS.FEEDS_PROCESSING + feed.feed_id); // Remove from Processing cache
                            redisConnection.del(CONSTANTS.FEEDS.KEYS.FEEDS_DONE + feed.feed_id); // Remove from Processing cache

                        });
                            return res.json({
                                code: 200,
                            });
                    });
                } else {
                    return res.json({
                        code : 404,
                        error : 'User not found',
                        login: cache.get('member_login_state'),
                    })
                }

            });
        });
    },
    getConfigFacebookFeed(req, res) {
      const regionName = req.body.regionName;

      const checkParams = (next) => {
        if(!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.FEED_FACEBOOK, regionName, (err, data) => {
          if(err) {
            return next(err);
          }

          if(!data) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data.config
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig
      ], (err, data) => {
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    }
}

function getFeedsSystem (callback) {
    var allFeeds = [];
    redisConnection.multi()
        .keys('feedsSystem*', function (err, feedsSys) {
            // NOTE: code in this callback is NOT atomic
            // this only happens after the the .exec call finishes.
            feedsSys.forEach(function (feed, index) {
                redisConnection.get(feed, function(err, data){
                    var post = JSON.parse( data);
                    if(post.status == 0){
                        if(post.system_push_count == 0
                            || Util.getCurrentTime() - post.lasttime_push >= CONSTANTS.FEEDS.INTERVAL_PUSH_TIME) {
                            // Live Feeds need to put to client
                            post.system_push_count++; // Tang bien count len 1
                            post.lasttime_push = Util.getCurrentTime();
                            if (post.system_push_count < CONSTANTS.FEEDS.MAX_SYSTEM_PUSH) {
                                redisConnection.set(feed.toString(), JSON.stringify(post), (err, res) => {
                                    console.log(`redis:set`, err, res);
                                });
                            }
                            else {
                                // Feed push Expired
                                redisConnection.del(feed.toString()); // Remove from Processing cache
                                // Move to Done
                                post.status = CONSTANTS.FEEDS.STATUS.DONE;
                                redisConnection.set(CONSTANTS.FEEDS.KEYS.FEEDS_DONE+post.feed_id, JSON.stringify(post), (err, res) => {
                                    console.log(`redis:set`, err, res);
                                });
                            }
                            // Update mongoDB
                            NewFeedSystem.findOneAndUpdate(
                                { feed_id: post.feed_id },
                                {
                                    system_push_count: post.system_push_count,
                                    shop_push_count: post.shop_push_count,
                                    lasttime_push: post.lasttime_push,
                                    status: post.status
                                },
                                function(err, user) {
                                    if (err) throw err;
                                    if (err){
                                        return res.json({
                                            code: CONSTANTS.CODE.SYSTEM_ERROR,
                                            message: message.SYSTEM.ERROR
                                        });
                                    }
                                });
                            // Attach to allFeeds
                            allFeeds.push(post);
                        }
                    }
                    else{
                        redisConnection.del(feed.toString()); // Remove from Redis cache
                        // Move to Done
                        post.status = CONSTANTS.FEEDS.STATUS.DONE;
                        redisConnection.set(CONSTANTS.FEEDS.KEYS.FEEDS_DONE+post.feed_id, JSON.stringify(post), (err, res) => {
                            console.log(`redis:set`, err, res);
                        });
                        // Update mongoDB
                        NewFeedSystem.findOneAndUpdate(
                            { feed_id: post.feed_id },
                            {
                                system_push_count: post.system_push_count,
                                shop_push_count: post.shop_push_count,
                                lasttime_push: post.lasttime_push,
                                status: post.status
                            },
                            function(err, user) {
                                if (err) throw err;
                                if (err){
                                    console.warn(err.message);  // returns error if no matching object found
                                    return res.json({
                                        code: CONSTANTS.CODE.SYSTEM_ERROR,
                                        message: message.SYSTEM.ERROR
                                    });
                                }
                            });
                    }
                });
            });
        })
        .exec(function (err, feedsSys) {
            callback(allFeeds);
            if(err)
                console.log("ERROR: ", err, feedsSys);
        });
}

function getFeedsFB (limit, callback) {
    redisConnection.lrange("feedsFacebook", 0, limit, function (err, posts) {
        if(err) {
            callback([]);
        }

        posts = posts.map(function (post) {
            return JSON.parse(post);
        });

        callback(posts);
    });
}
