import _ from 'lodash';
import async from 'async';
import util from 'util';
import fs from 'fs.extra'
import {amazonS3Addr, notifyServiceAddr, locationServerAddr, service, orderType, orderTypeGroup} from '../../config';
import CONSTANTS from '../../const';
import MESSAGE from '../../message';
import uuid from 'uuid/v4';
import rp from 'request-promise'
var Util = require('../../Util');
import * as mailUtil from '../../mailUtil';
const ms = require('ms')
import crypto from 'crypto';
// import geoip from 'geoip-lite';

export default {
    getConfigAnalytics(req, res) {
      res.json({
        code: 200,
        data: {
          isOpen: 1,
          trackId: "UA-87788343-1"
        }
      })
    },
    sendCrashReport(req, res) {
      res.json({
        code: CONSTANTS.CODE.SUCCESS
      })

      let appName = '';
      if(req.body && req.body.appName) {
        appName = req.body.appName
      }
      if(req.body.memberToken) {
        const memberToken = req.body.memberToken
        let stringToken = `user:${memberToken}`
        if(appName) {
          stringToken = `${appName}:${memberToken}`
        }
        redisConnection.get(stringToken, (err, result) => {
          if(result) {
            const objSign = JSON.parse(result);
            Crashlog
              .create({
                error: req.body.error,
                device: req.body.device,
                appState: req.body.appState,
                appName: req.body.appName || '',
                versionCodePush: req.body.versionCodePush || '',
                member: objSign.id || ''
              })
          } else {
            Crashlog
            .create({
              error: req.body.error,
              device: req.body.device,
              appState: req.body.appState,
              appName: req.body.appName || '',
              versionCodePush: req.body.versionCodePush || ''
            })
          }
        })
      } else {
        Crashlog
          .create({
            error: req.body.error,
            device: req.body.device,
            appState: req.body.appState,
            appName: req.body.appName || '',
            versionCodePush: req.body.versionCodePush || ''
          })
      }
    },
    getAPIKeyGoogleMap(req, res) {
      return res.json({
        code: CONSTANTS.CODE.SUCCESS,
        data: {
          apiKey: 'AIzaSyBIidO3qxiwdjdX_GT1fLRvfGM8E8D4WIc'
        }
      })
    },
    listServices(req, res) {
      const userId = req.user.id;
      let region = req.body.region;

      const getRegion = (next) => {
        if(region) {
          return next();
        }

        Members
          .findById(userId, "region")
          .lean()
          .exec((err, result) => {
            if(err || !result) {
              return next(err || new Error(`Not found user info`))
            }

            region = result.region;
            next();
          });
      }

      const getServices = (next) => {
        Service
          .list(region, (err, services) => {
            console.log(err, services);
            if(err) {
              return next(err);
            }

            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: services
            });
          })
      }

      async.waterfall([
        getRegion,
        getServices
      ], (err, data) => {
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    reportLocation(req, res) {
        const message = _.get(req, 'body.message', '');
        const feed_id = _.get(req, 'body.feed_id', '');
        const nameLocation = _.get(req, 'body.nameLocation', '');
        if(message && feed_id && nameLocation) {
            const obj = {
                message,
                feed_id,
                nameLocation,
                createdAt: Date.now()
            }

            ReportLocation.create(obj, (err, result) => {
                res.json({
                    code: 200,
                    message: {
                        'head': 'Thank you',
                        'body': (err && (err.code !== 11000)) ? 'Lỗi mất rồi gửi lại đi' : 'Được rồi bạn ơi'
                    }
                });
            });
        }
    },
    tracking(req, res) {
      const type = _.get(req, 'body.type', '');
      const member = req.user.id;
      const otherInf = _.get(req, 'body.otherInf', {});
      otherInf.ipForward = req.headers['x-forwarded-for'];
      otherInf.ipRemote = req.connection.remoteAddress;

      const obj = {
        type,
        member,
        otherInf
      }

      TrackingAction.create(obj, (err, result)=>{});
      if(type === 0 || type === 2 || type === 3 || type === 4) {
        BlackDevice
          .count({
              uniqueId: otherInf.uniqueId
          })
          .exec((err, count) => {
            if(err) {
              return res.json({
                code: 300
              });
            }
            if(count > 0) {
              // Push notification to admin
              Members
                .findById(member, "phone")
                .lean()
                .exec((err, result) => {
                  const phone = result ? result.phone : "";
                  const admins = global.app.get('listUsers')

                  const message = `Phone: ${phone}\nUserId: ${member}\nDeviceId: ${otherInf.uniqueId}`
                  let objCreate = {
                    message,
                    data: {
                      phone,
                      member
                    },
                    resultPush: []
                  }

                  let resultPush = []
                  async.eachSeries(admins, (user, done) => {

                    const options = {
                      method: 'POST',
                      uri: `${notifyServiceAddr}/api/v1.0/push-notification/member`,
                      body: {
                          userId: user,
                          title: "Đăng nhập thiết bị bị khóa",
                          message: `Đăng nhập thiết bị bị khóa\n Phone: ${phone}\nUserId: ${member}\nDeviceId: ${otherInf.uniqueId}`,
                          data: {}
                      },
                      json: true // Automatically stringifies the body to JSON
                    };

                    rp(options)
                      .then((result) => {
                        resultPush.push({
                          user,
                          result: result
                        })
                        done()
                      })
                      .catch((err) => {
                        resultPush.push({
                          user,
                          result: err
                        })
                        done()
                      })
                  },() => {
                    objCreate.resultPush = resultPush
                    RiskTeamLog.create(objCreate,() => {})
                  })

                  admins.forEach((adminId) => {
                    const options = {
                      method: 'POST',
                      uri: `${notifyServiceAddr}/api/v1.0/push-notification/member`,
                      body: {
                          userId: adminId,
                          title: "Đăng nhập thiết bị bị khóa",
                          message: `Phone: ${phone}\nUserId: ${member}\nDeviceId: ${otherInf.uniqueId}`,
                          data: {}
                      },
                      json: true // Automatically stringifies the body to JSON
                    };

                    request(options, (err, response, result) => {

                    });
                  })
                })


              // Block accounts
              Members
                .update({
                  _id: member
                }, {
                  blockUtil: *************
                })
                .exec();
              ReasonBlock
                .create({
                  member,
                  message: 'Hệ thống đã khoá tài khoản của bạn. Xin liên hệ hotline, gặp TĐV: 55822 để được hỗ trợ!'
                },()=>{})

              return res.json({
                code: 1993,
                message: MESSAGE.USER.BLACK_DEVICE
              });
            }

            return res.json({
              code: 200
            });
          });
      } else {
        res.json({
          code: 200
        });
      }
    },
    feedback(req, res) {
      const member = req.user.id;
      const message = _.get(req, 'body.message', '');
      let region = req.body.regionName;
      let hasImage = false;
      let filePath = '';
      let linkDB = '';

      const checkParams = (next) => {
        if(typeof message !== 'string' || message.trim().length === 0) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGE.SYSTEM.ERROR
          });
        }

        next(null);
      }

      const checkSendImageAndSave = (next) => {
        if(req.file) {
          const fileName = uuid();
          hasImage = true;
          filePath = `feedback/${fileName}.png`;
          linkDB = `/feedback/${fileName}.png`;

          const options = {
            method: 'POST',
            uri: `${amazonS3Addr}/api/v1.0/upload`,
            formData: {
              bucket: 'sanship',
              key: filePath,
              fileUpload: {
                value: req.file.buffer,
                options: {
                  filename: "image.png"
                }
              }
            },
            json: true
          }

          rp(options)
            .then((result) => {
              if(result.code !== 200) {
                return next({
                  code: CONSTANTS.CODE.FAIL,
                  message: MESSAGE.SYSTEM.ERROR
                })
              }

              next();
            })
            .catch((err) => {
              return next(err);
            })
        } else {
          next(null);
        }
      }

      const getRegion = (next) => {
        if(region) {
          return next();
        }

        Members
          .findById(req.user.id, "region")
          .lean()
          .exec((err, result) => {
            if(err || !result) {
              return next(err || new Error(`Not found user inf`));
            }

            region = result.region || 'hn';

            next();
          })
      }

      const saveToDb = (next) => {
        const obj = {
          member,
          message,
          region: region
        }

        if(hasImage) {
          obj.image = linkDB;
        }

        Feedback.create(obj, (err, result) => {
          if(err) {
            return next(err);
          }

          next({
            code: CONSTANTS.CODE.SUCCESS,
            message: MESSAGE.USER.FEEDBACK
          });
        })
      }

      async.waterfall([
        checkParams,
        checkSendImageAndSave,
        getRegion,
        saveToDb
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGE.SYSTEM.ERROR
        });

        res.json(data || err);
      });
    },
    getConfigForUpdateLocation(req, res) {
      return res.json({
        code: CONSTANTS.CODE.SUCCESS,
        data: {
          interval: 60000,
          options: {
            enableHighAccuracy: true,
            timeout: 20000
          },
          url: `${locationServerAddr}/api/v2.0/member/update-latest-location`
        }
      })
    },
    getConfigForPhoneAthentication(req, res) {
      res.json({
        code: CONSTANTS.CODE.SUCCESS,
        data: {
          resetPassword:'SSService',
          register:'SSService',
          changePhone:'SSService'
        }
      })
    },
    getConfigForMessageAuthen(req,res) {
      const regionName = req.body.regionName;

      const checkParams = (next) => {
        if(!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getMessage = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.WARNING_AUTHEN, regionName, (err, data) => {
          if(err) {
            return next(err);
          }

          if(!data) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data.config
          })
        })
      }

      async.waterfall([
        checkParams,
        getMessage,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getDetailNews(req, res) {
      const newsId = _.get(req, 'body._id', '');
      const region = _.get(req, 'body.regionName', '');

      const checkParams = (next) => {
        if(!region || !newsId) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGE.SYSTEM.ERROR
          })
        }

        next();
      }

      const getDetailNews = (next) => {
        New.findOne({_id: newsId}, 'data')
          .lean()
          .exec((err, result) => {
            if (err || !result || !result.data) {
              return next(err || new Error('Not found'));
            }

            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: result.data
            })
          });
      }

      async.waterfall([
        checkParams,
        getDetailNews,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGE.SYSTEM.ERROR
        });

        res.json(data || err);
      });
    },
    listServiceRegister(req,res) {
      const {regionName} = req.body;
      const id = req.user.id;
      const platform = _.get(req, 'body.platform' , '');
      const nativeVersion = _.get(req, 'body.nativeVersion', '');

      let services;

      const checkParams = (next) => {
        if(!regionName || !id) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }
        next();
      }

      const listServiceAvailable = (next) => {
        Service
          .find({
            active:1,
            open:1,
            showListShipper: 1,
            $or: [
              {
                'region.allow': regionName
              },
              {
                'region.allow': 'all',
                'region.deny': {
                  $ne: regionName
                }
              }
            ]
          })
          .select({
            name:1,
            icon:1,
            editable:1,
            messageForRider:1,
            canRegister:1,
            nameTabShipper: 1
          })
          .sort('-order')
          .lean()
          .exec((err, results) => {
            if(err) {
              return next(err)
            }

            services = results;

            services.map((item, index) => {
              if (service.order.includes(item._id.toHexString()) && ((platform === 'ios' && nativeVersion < 1500091) || (platform === 'android' && nativeVersion < 90072))) {
                item.editable = 0;
              }
              if (service.car.includes(item._id.toHexString()) && ((platform === 'ios' && nativeVersion < 1500091) || (platform === 'android' && nativeVersion < 90072))) {
                services.splice(index,1)
              }
            })

            next();
          })
      }

      const findRunningService = (next) => {
        Rider
          .findOne({
            member: id
          })
          .lean()
          .select({
            serviceRunning:1,
            serviceRegisted:1
          })
          .exec((err, result) => {
            if(err) {
              return next(err);
            }
            if(!result || !result.serviceRunning) {
              return next(null,{
                code: CONSTANTS.CODE.SUCCESS,
                data:{
                  services,
                  count:0
                }
              })
            }
            let count = 0;
            const serviceRunning = result.serviceRunning.map(String)
            services.some((service) => {
              if(serviceRunning.includes(service._id.toString())) {
                service.active = 1;
                count ++;
              }
            })

            next(null,{
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                services,
                count
              }
            })
          })
      }

      async.waterfall([
        checkParams,
        listServiceAvailable,
        findRunningService
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    handleSyncData(req,res) {
      let members = []
      const findMember = (next) => {
        ShipperAuthentication
          .find({ member: req.body.id})
          .select('member')
          .lean()
          .exec((err, results) => {
            members = results.map(mem => {
              return mem.member;
            })
            console.log("ahihi total members: " + members.length)
            next();
          })
      }


      const updateService = (next) => {
        let i = 0
        async.mapLimit(members, 100, (member, done) => {
          i++
          let objUpdate = {
              serviceRunning: [mongoose.Types.ObjectId("5eec325eed3b2a35431dee17"),
              mongoose.Types.ObjectId("5f07d2e7e1baaf2b3f691246"),
              mongoose.Types.ObjectId("611253fb20dde52d9edf400d"),
              mongoose.Types.ObjectId("616d122881b14b396afe56b1"),
              mongoose.Types.ObjectId("6178d42681b14b396afe5a9d"),
              mongoose.Types.ObjectId("6201d01259efe71bfe1d9e80"),
              mongoose.Types.ObjectId("62ce2e316df1da1c078fc8b8"),
              mongoose.Types.ObjectId("6112543020dde52d9edf400e")
              ]
          }
          Rider
            .findOneAndUpdate({
              member: member
            },objUpdate,{
              new:true
            })
            .select('serviceRunning')
            .lean()
            .exec((err, result) => {
              if(err) {
                return done(err)
              }
              if(!result) {
                return done();
              }
              const dataRedis =  JSON.stringify(result)
              redisConnection.set(`cacheman:cachegoose-cache:rider:${member}`, dataRedis, (err, res) => {
                if(err) {
                  console.log("ahihi errrrrrrrrrr: ", i, member)
                  return done(err);
                }
                console.log("ahihi done: ",i)
                done();
              });

            })
        },(err) => {
          if(err) {
            return next(err);
          }
          next();
        })
      }

      async.waterfall([
        findMember,
        updateService
      ], (err, data) => {
          if(err) {
            console.error(err)
          }
          res.json({
            code: 200
          })
          console.log('sucessfully updated')
      });
    },
    switchService(req,res) {
      const id = req.user.id;
      const serviceId = req.body.service;
      let servicesUpdate = []
      let groupService = [serviceId]
      let method;
      let isRiderAuthen = 0

      const checkParams = (next) => {
        if(!id || !serviceId) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }
        if(orderTypeGroup) {
          Object.keys(orderTypeGroup).forEach((orderType, i) => {
            if(orderTypeGroup[orderType].includes(serviceId)) {
              groupService = orderTypeGroup[orderType]
            }
          });
        }

        next();
      }

      const findMemberInf = (next) => {
        Members
          .findById(id)
          .select('ship.isAuthen')
          .lean()
          .exec((err, result) => {
            if(err) {
              return next(err)
            }
            if(result && result.ship && result.ship.isAuthen){
              isRiderAuthen = result.ship.isAuthen
            }
            next();
          })
      }

      const findRiderInfo = (next) => {
        Rider
          .findOne({
            member: id
          })
          .lean()
          .select({
            serviceRunning:1
          })
          .exec((err, result) => {
            if(err) {
              return next(err)
            }
            if(!result || !result.serviceRunning) {
              return next({
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: MESSAGE.SYSTEM.ERROR
              })
            }
            const serviceRunning = result.serviceRunning.map(String)

            const serviceCar = orderType.carFour.concat(orderType.carSeven);
            if(!serviceCar.includes(serviceId) && _.intersection(serviceCar,serviceRunning).length > 0 && isRiderAuthen) {
              return next({
                code: CONSTANTS.CODE.FAIL,
                message: {
                  head: 'Thông báo',
                  body: 'Rất tiếc, bạn không thể đăng ký dịch vụ này do đã đăng ký dịch vụ Taxi. Vui lòng liên hệ Hotline: 1900.633.689 để được hỗ trợ.'
                }
              })
            }

            // if(!orderType.hireDriver.includes(serviceId) && _.intersection(orderType.hireDriver,serviceRunning).length > 0 && isRiderAuthen) {
            //   return next({
            //     code: CONSTANTS.CODE.FAIL,
            //     message: {
            //       head: 'Thông báo',
            //       body: 'Rất tiếc, bạn không thể đăng ký dịch vụ này do đã đăng ký dịch vụ Lái xe hộ. Vui lòng liên hệ Hotline: 1900.633.689 để được hỗ trợ.'
            //     }
            //   })
            // }

            const index = serviceRunning.indexOf(serviceId)
            if(index > -1) {
              for (let i = 0; i < serviceRunning.length; i++) {
                if(groupService.includes(serviceRunning[i])) {
                  serviceRunning.splice(i,1);
                  i--;
                }
              }
              servicesUpdate = serviceRunning
            } else {
              groupService.forEach((item, i) => {
                if(!serviceRunning.includes(item)) {
                  serviceRunning.push(item)
                }
              });
              servicesUpdate = serviceRunning;
              if(service.car.concat(orderType.carFour).concat(orderType.carSeven).includes(serviceId)) {
                servicesUpdate = groupService
              } else {
                for (let i = 0; i < serviceRunning.length; i++) {
                  if(service.car.concat(orderType.carFour).concat(orderType.carSeven).includes(serviceRunning[i])) {
                    serviceRunning.splice(i,1);
                    i--;
                  }
                }
                servicesUpdate = serviceRunning;
              }
            }
            next();
          })
      }
      const switchService = (next) => {
        let objUpdate = {};
        objUpdate = {
          serviceRunning: servicesUpdate
        }
        Rider
          .findOneAndUpdate({
            member: id
          },objUpdate,{
            new:true
          })
          .select('serviceRunning')
          .lean()
          .exec((err, result) => {
            if(err) {
              return next(err)
            }
            const dataRedis =  JSON.stringify(result)
            redisConnection.set(`cacheman:cachegoose-cache:rider:${id}`, dataRedis, (err, res) => {
            });
            const serviceRunning = result.serviceRunning.map(String)
            const options = {
              method: 'POST',
              uri: `${locationServerAddr}/api/v2.0/shipper/update-service`,
              body: {
                id,
                services: serviceRunning
              },
              json: true
            }

            rp(options)
              .then((result) => {
                if(result.code !== 200) {
                  return next({
                    code: CONSTANTS.CODE.FAIL,
                    message: MESSAGE.SYSTEM.ERROR
                  })
                }

                next(null,{
                  code: CONSTANTS.CODE.SUCCESS,
                  data: serviceRunning
                })
              })
              .catch((err) => {
                mailUtil
                  .sendMail(` --- ERR switchService ${locationServerAddr}/api/v2.0/shipper/update-service --- ${err}`);
                return next(err);
              })
          })
      }
      async.waterfall([
        checkParams,
        findMemberInf,
        findRiderInfo,
        switchService
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigTag(req,res) {
      Config
        .get(CONSTANTS.CONFIG_TYPE.TAG, req.body.regionName, (err, data) => {
          if(err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR
            })
          }
          res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: data && data.config || {}
          })
        })
    },
    getConfigContact(req,res) {
      let timeSync = 1;
      const checkParams = (next) => {
        if(!req.body.regionName || !req.user.id) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }
        next();
      }

      const getTimeSync = (next) => {
        Contact
          .findOne({member: req.user.id})
          .select({updatedAt:1})
          .lean()
          .exec((err,result) => {
            if(err) {
              return next(err)
            }
            if(!result || !result.updatedAt) {
              return next();
            }
            timeSync = result.updatedAt;
            next();
          })
      }

      const getConfig = (next) => {
        Config
          .get(CONSTANTS.CONFIG_TYPE.ZIPPO, req.body.regionName, (err, result) => {
            if(err) {
              return next({
                code: CONSTANTS.CODE.SYSTEM_ERROR
              })
            }

            let dataRes = {};
            if(result && result.config) {
              dataRes = result.config;
              dataRes.timeSync = timeSync;
            }
            next(null,{
              code: CONSTANTS.CODE.SUCCESS,
              data: dataRes
            })
          })
      }

      async.waterfall([
        checkParams,
        getTimeSync,
        getConfig
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    syncContact(req,res) {
      const userId = req.user.id || '';

      const checkParams = (next) => {
        if(!req.body.data || !req.body.data.length || !userId) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }
        next();
      }

      const syncContact = (next) => {

        let dataContact = []
        req.body.data.forEach((contact, index) => {
          if(contact.phoneNumbers && contact.phoneNumbers.length) {
            dataContact.push(contact)
          }
        });
        Contact
          .findOneAndUpdate({
            member: userId
          },{
            $addToSet: {
              data: { $each: dataContact }
            },
            updatedAt: Date.now()
          },{
            new: true,
            upsert: true,
            setDefaultsOnInsert: true
          })
          .lean()
          .exec((err, result) => {
            if(err) {
              return next(err)
            }
            next({
              code: CONSTANTS.CODE.SUCCESS
            })
          })
      }

      async.waterfall([
        checkParams,
        syncContact
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigHistoryFacebook(req, res) {
      const regionName = req.body.regionName;

      const checkParams = (next) => {
        if(!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.HISTORY_FACEBOOK, regionName, (err, data) => {
          if(err) {
            return next(err);
          }

          if(!data) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data.config
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    listTopupAddress(req, res) {
      const location = req.body.location;
      const hasUniform = _.has(req.body, 'hasUniform');
      let listTopupAddress;
      let region;

      const getRegion = (next) => {
        const options = {
          method: 'POST',
          uri: `${locationServerAddr}/api/v2.0/region/lat-lng`,
          body: {
            location,
            level: 2
          },
          json: true // Automatically stringifies the body to JSON
        }

        rp(options)
          .then((result) => {
            if(result.code !== CONSTANTS.CODE.SUCCESS) {
              return next(result);
            }

            region = result.data;
            next();
          })
          .catch((err) => {
            mailUtil
              .sendMail(` --- ERR getRegionByLatLng ${locationServerAddr}/api/v2.0/region/lat-lng --- ${err}`);
            return next(err);
          });
      }

      const listTopup = (next) => {
        let query = {active: 1, status: 1, region};
        if (hasUniform) {
          query.hasUniform = hasUniform;
        }

        PartnerBase
          .find(query, 'phone name address location workingTime allowCalling')
          .lean()
          .exec((err, result) => {
            if(err || !result) {
              return next(err || new Error(`Not found partner info`))
            }

            listTopupAddress = result;

            next();
          });
      }

      const sortFollowDistance = (next) => {
        if (!location) {
          return next();
        }

        listTopupAddress.map(topupAddr => {
          topupAddr.distance = Util.getDistanceFromLatLonInKm(location.lat, location.lng, topupAddr.location.lat, topupAddr.location.lng);
        });

        listTopupAddress.sort((a, b) => a.distance - b.distance);

        next();
      }

      const handleWorkingTime = (next) => {
        const now = new Date();
        const day = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];

        listTopupAddress.map(topupAddr => {
          const startDay = topupAddr.workingTime.weektime.from;
          const endDay = topupAddr.workingTime.weektime.to;
          const startTime = topupAddr.workingTime.daytime.from.trim();
          const endTime = topupAddr.workingTime.daytime.to.trim();
          const startHour = Number(startTime.split(':')[0].trim());
          const endHour = Number(endTime.split(':')[0].trim());
          const startMinutes = Number(startTime.split(':')[1].trim());
          const endMinutes = Number(endTime.split(':')[1].trim());
          let closed = false;

          if ((now.getHours() === startHour && now.getMinutes() < startMinutes)
            || (now.getHours() === endHour && now.getMinutes() > endMinutes)
            || now.getHours() < startHour
            || now.getHours() > endHour
            || (startDay < endDay && (now.getDay() < startDay || now.getDay() > endDay))
            || (startDay > endDay && now.getDay() < startDay && now.getDay() > endDay)) {
              closed = true;
          }

          topupAddr.workingTime = {
            closed,
            message: `${startTime} -> ${endTime} | ${day[startDay]} -> ${day[endDay]}`
          };
        })

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: listTopupAddress
        });
      }

      async.waterfall([
        getRegion,
        listTopup,
        sortFollowDistance,
        handleWorkingTime
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigPoint(req, res) {
      const regionName = req.body.regionName;

      const checkParams = (next) => {
        if(!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.POINT, regionName, (err, data) => {
          if(err) {
            return next(err);
          }

          if(!data) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data.config
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigLogin(req, res) {
      const regionName = req.body.regionName || "unknown";

      const checkParams = (next) => {
        if(!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.LOGIN, regionName, (err, data) => {
          if(err) {
            return next(err);
          }

          if(!data) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data.config
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getHotLineBookCar(req, res) {
      Config
        .getData({
          type: CONSTANTS.CONFIG_TYPE.HOTLINE_CAR,
          region: req.body.regionName,
          regionDistrict: req.user.regionDistrict
        }, (err, data) => {
          if(_.isError(err)) {
            logger.logError([err], req.originalUrl, req.body);
          }
          if(err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR
            })
          }

          res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: data ? data.config : null
          })
        })
    },
    getStateMachineRider(req, res) {
      const userId = _.get(req, 'user.id', '');

      OrderBike
        .find({
          shipper: userId,
          status: {$in: [1, 2, 7, 8]},
          acceptedAt: {$gt: Date.now() - ms('1h')}
        }, '_id nonDes')
        .sort({acceptedAt: -1})
        .limit(1)
        .lean()
        .exec((err, data) => {
          if(_.isError(err)) {
            logger.logError([err], req.originalUrl, req.body);
          }

          if (err || !data) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR
            })
          }

          if (!data.length) {
            return res.json({
              code: CONSTANTS.CODE.SUCCESS
            })
          }

          res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              link: data[0].nonDes ? 'DetailNoDesBikeScreen' : 'DetailOrderBikeScreen',
              extras: {
                id: data[0]._id,
                message: 'Bạn đang có chuyến đi chưa hoàn thành. Bạn vui lòng hoàn thành chuyến đi và cập nhật trạng thái để có thể nhận được chuyến đi khác. Xin cảm ơn!'
              }
            }
          })
        })
    },
    getConfigNecessary(req, res) {
      Config
        .get(CONSTANTS.CONFIG_TYPE.CONFIG_NECESSARY, req.body.regionName, (err, data) => {
          if(_.isError(err)) {
            logger.logError([err], req.originalUrl, req.body);
          }
          if (err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR
            })
          }

          res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: data && data.config || []
          })
        })
    },
    getConfigChangePhone(req, res) {
      Config
        .get(CONSTANTS.CONFIG_TYPE.CHANGE_PHONE, req.body.regionName, (err, data) => {
          if(_.isError(err)) {
            logger.logError([err], req.originalUrl, req.body);
          }
          if (err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR
            })
          }

          res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: data && data.config || {}
          })
        })
    },
    checkCanTurnOnPushOrder(req, res) {
      const userId = req.user.id;
      const regionName = req.body.regionName;
      let userInf;

      const checkParams = (next) => {
        if (!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getUserInf = (next) => {
        Members
          .findById(userId)
          .lean()
          .exec((err, result) => {
            if(err || !result) {
              return next(err || new Error(`Not found user info`))
            }

            userInf = result;
            next();
          });
      }

      const checkCoint = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.COINT, regionName, (err, data) => {
          if (err) {
            return next(err);
          }

          if (!data) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          if (userInf.coints <= data.config.minCoints) {
            return next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                canTurnOnPushOrder: 0
              },
              message: {
                head: 'Thông báo',
                body: 'Tài khoản của bạn hiện tại nhỏ hơn số dư tối thiểu. Bạn vui lòng nạp thêm để có thể bật chức năng nhận đơn hệ thống. Xin cảm ơn!'
              }
            })
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              canTurnOnPushOrder: 1
            }
          })
        })
      }

      async.waterfall([
        checkParams,
        getUserInf,
        checkCoint
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigShowMaxServiceCharge(req, res) {
      const regionName = req.body.regionName || 'unknown';

      const checkParams = (next) => {
        if (!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.MAX_SERVICE_CHARGE, regionName, (err, data) => {
          if (err) {
            return next(err);
          }

          if (!data) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data.config
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigReferralCode(req, res) {
      const regionName = req.body.regionName || 'unknown';
      const userId = _.get(req,'body.userId','')
      const mode = _.get(req,'body.modeApp','')
      let isOpenProfile = 0
      const checkParams = (next) => {
        if (!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getMember = (next) => {
        if(!userId) {
          return next()
        }
        Members
          .findOne({
            _id: userId
          })
          .lean()
          .exec((err,result) => {
            if(err) {
              return next(err)
            }
            if(result && (Date.now() - result.createdAt) < ms('15m')) {
              isOpenProfile = 1
            }
            next();
          })
      }

      const getConfig = (next) => {
        Config
          .findOne({
            type: CONSTANTS.CONFIG_TYPE.REFERRAL_CODE,
            $or: [{
              'region.allow': 'all',
              'region.deny': {$ne: regionName}
            },{
              'region.allow': regionName
            }],
            mode
          })
          .lean()
          .exec((err, data) => {
            if (err) {
              return next(err);
            }

            if (!data) {
              return next({
                code: CONSTANTS.CODE.FAIL
              })
            }
            let dataResponse = data.config
            if(data.config.isOpenProfile) {
              dataResponse.isOpenProfile = isOpenProfile
            } else {
              dataResponse.isOpenProfile = 0
            }
            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: dataResponse
            })
          })
      }

      async.waterfall([
        checkParams,
        getMember,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getPaymentMethodConfig(req, res) {
      const regionName = req.body.regionName;
      const platform = req.body.platform;
      const nativeVersion = req.body.nativeVersion;

      const checkParams = (next) => {
        if(!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.PAYMENT_METHOD, regionName, (err, data) => {
          if(err) {
            return next(err);
          }

          if(!data || !data.config) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          for (let i = 0; i < data.config.length; i++) {
            if (nativeVersion <= data.config[i].nativeVersion[platform]) {
              data.config[i].open = 0;
            }

            if(!data.config[i].open) {
              data.config.splice(i,1);
              i--;
            }
          }

          for (let i = 0; i < data.configOrder.length; i++) {
            if (nativeVersion <= data.configOrder[i].nativeVersion[platform]) {
              data.configOrder[i].open = 0;
            }

            if(!data.configOrder[i].open) {
              data.configOrder.splice(i,1);
              i--;
            }
          }

          for (let i = 0; i < data.configToken.length; i++) {
            if (nativeVersion <= data.configToken[i].nativeVersion[platform]) {
              data.configToken[i].open = 0;
            }

            if (!data.configToken[i].open) {
              data.configToken.splice(i, 1);
              i--;
            }
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data.config,
            dataOrder: data.configOrder,
            dataWeb: data.configOrderWeb || [],
            dataMB: data.configMB || [],
            dataFastGo: data.configFastGo || [],
            configToken: data.configToken || []
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },

    getPaymentMethodTickbox(req, res) {
      const regionName = req.body.regionName;
      const platform = req.body.platform;
      const nativeVersion = req.body.nativeVersion;
      const storeId = req.body.store
      let methodLimited = []
      const checkParams = (next) => {
        if(!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getLimitMethod = (next) => {
        if(!storeId) {
          return next();
        }

        Store
          .findOne({
            _id: storeId
          })
          .lean()
          .exec((err, data) => {
            if(err) {
              return next(err)
            }

            if(data && data.paymentMethod) {
              methodLimited = data.paymentMethod
            }

            next();
          })
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.PAYMENT_METHOD_TICKBOX, regionName, (err, data) => {
          if(err) {
            return next(err);
          }

          if(!data || !data.config) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          for (let i = 0; i < data.config.length; i++) {
            if (nativeVersion <= data.config[i].nativeVersion[platform]) {
              data.config[i].open = 0;
            }

            if(!data.config[i].open) {
              data.config.splice(i,1);
              i--;
            }
          }
          if(methodLimited.length) {
            for (let i = 0; i < data.config.length; i++) {
              if(!methodLimited.includes(data.config[i].variable)) {
                data.config.splice(i,1);
                i--;
              }
            }
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data.config
          })
        })
      }

      async.waterfall([
        checkParams,
        getLimitMethod,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });
        res.json(data || err);
      });
    },
    getConfigPaymentTickBox(req,res) {
      let regionName = req.body.regionName;
      const platform = req.body.platform;
      const nativeVersion = req.body.nativeVersion;
      const newVersion = req.body.newVersion || 0;
      const userId = req.user.id;
      let isOpenCODTransfer = 0;

      const checkParams = (next) => {
        if(!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getRegionStore = (next) => {
        Store
          .findOne({
            member: userId
          })
          .select('region')
          .lean()
          .exec((err, result) => {
            if(err) {
              return next(err);
            }
            if(result && result.region) {
              regionName = result.region
            }
            next();
          })
      }

      const checkRegionHasServiceCharge = (next) => {
        Config
          .findOne({
            type: 75
          })
          .lean()
          .exec((err, result) => {
            if(err) {
              return next(err)
            }

            if(!result) {
              return next({
                code: CONSTANTS.CODE.FAIL
              })
            }

            if(result.config && result.config[regionName] && result.config[regionName].serviceCharge) {
              isOpenCODTransfer = 1
            }

            next();
          })
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.PAYMENT_TICKBOX, regionName, (err, data) => {
          if(err) {
            return next(err);
          }

          if(!data || !data.config) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          let dataReturn = {
            moneyArr: [],
            chargeArr: [],
            minMoney: 0,
          }

          for (let i = 0; i < data.config.chargeArr.length; i++) {
            if (nativeVersion <= data.config.chargeArr[i].nativeVersion[platform]) {
              data.config.chargeArr[i].open = 0;
            }

            if(!data.config.chargeArr[i].open) {
              data.config.chargeArr.splice(i,1);
              i--;
            }
          }

          for (let i = 0; i < data.config.withdrawArr.length; i++) {
            if (nativeVersion <= data.config.withdrawArr[i].nativeVersion[platform] || (data.config.withdrawArr[i].variable === 'zalo-withdraw' && !newVersion)) {
              data.config.withdrawArr[i].open = 0;
            }

            if (!data.config.withdrawArr[i].open) {
              data.config.withdrawArr.splice(i, 1);
              i--;
            }
          }

          dataReturn.moneyArr = data.config.moneyArr || [],
          dataReturn.chargeArr = data.config.chargeArr  || []
          dataReturn.minMoney = data.config.minMoney || 0
          dataReturn.withdrawArr = data.config.withdrawArr  || []
          dataReturn.minMoneyWithdraw = data.config.minMoneyWithdraw || 0
          if(data.config.forcePhoneWithdraw) {
            dataReturn.forcePhoneWithdraw = 1
          } else {
            dataReturn.forcePhoneWithdraw = 0
          }
          if(data.config.isOpenCODTransfer && isOpenCODTransfer) {
            dataReturn.isOpenCODTransfer = 1
          } else {
            dataReturn.isOpenCODTransfer = 0
          }
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: dataReturn
          })
        })
      }

      async.waterfall([
        checkParams,
        getRegionStore,
        checkRegionHasServiceCharge,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigShowPaymentTickBox(req,res) {
      const regionName = req.body.regionName;

      const checkParams = (next) => {
        if(!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.SHOW_PAYMENT_TICKBOX, regionName, (err, data) => {
          if(err) {
            return next(err);
          }

          if(!data || !data.config) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }


          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data.config
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigPaymentHeywow(req,res) {
      let regionName = req.body.regionName;
      const platform = req.body.platform;
      const nativeVersion = req.body.nativeVersion;
      const userId = req.user.id;

      const checkParams = (next) => {
        if(!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.PAYMENT_HEYWOW, regionName, (err, data) => {
          if(err) {
            return next(err);
          }

          if(!data || !data.config) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          let dataReturn = {
            moneyArr: [],
            withdrawArr: [],
            minMoneyWithdraw: 0,
          }

          dataReturn.moneyArr = data.config.moneyArr  || []
          dataReturn.withdrawArr = data.config.withdrawArr  || []
          dataReturn.minMoneyWithdraw = data.config.minMoneyWithdraw || 0
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: dataReturn
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigPaymentInApp(req, res) {
      const regionName = req.body.regionName;
      const platform = req.body.platform;
      const nativeVersion = req.body.nativeVersion;

      const checkParams = (next) => {
        if(!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.PAYMENT_INAPP, regionName, (err, data) => {
          if(err) {
            return next(err);
          }

          if(!data || !data.config) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          let dataReturn = {
            moneyArr: [],
            chargeArr: [],
            minMoney: 0,
          }

          for (let i = 0; i < data.config.chargeArr.length; i++) {
            if (nativeVersion <= data.config.chargeArr[i].nativeVersion[platform]) {
              data.config.chargeArr[i].open = 0;
            }

            if(!data.config.chargeArr[i].open) {
              data.config.chargeArr.splice(i,1);
              i--;
            }
          }
          dataReturn.moneyArr = data.config.moneyArr || [],
          dataReturn.chargeArr = data.config.chargeArr  || []
          dataReturn.minMoney = data.config.minMoney || 0
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: dataReturn
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigShowPaymentInapp(req, res) {
      const regionName = req.body.regionName;

      const checkParams = (next) => {
        if(!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.SHOW_PAYMENT_INAPP, regionName, (err, data) => {
          if(err) {
            return next(err);
          }

          if(!data || !data.config) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }


          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data.config
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getLinkYoutube(req, res) {
      const appName = req.body.appName || 'customer';

      Config
        .get(CONSTANTS.CONFIG_TYPE.LINK_YOUTUBE, req.body.regionName, (err, data) => {
          if(_.isError(err)) {
            logger.logError([err], req.originalUrl, req.body);
          }

          if (err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR
            })
          }

          if (data && data.config && data.config.linkGuide && data.config.linkGuide[appName]) {
            data.config.linkGuide = data.config.linkGuide[appName];
          }

          res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: data && data.config || {}
          })
        })
    },
    getLinkYoutubeShipper(req, res) {
      Config
        .get(CONSTANTS.CONFIG_TYPE.LINK_YOUTUBE_SHIPPER, req.body.regionName, req.body.provider, (err, data) => {
          if (err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR
            })
          }

          res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: data && data.config || {}
          })
        })
    },
    getConfigShipperAuthenOnline(req, res) {
      Config
        .get(CONSTANTS.CONFIG_TYPE.SHIPPER_AUTHEN_ONLINE, req.body.regionName, (err, data) => {
          if(_.isError(err)) {
            logger.logError([err], req.originalUrl, req.body);
          }

          if (err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR
            })
          }

          res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: data && data.config || {}
          })
        })
    },
    getConfigCovidPassport(req, res) {
      const regionName = req.body.regionName || 'unknown';
      const memberCode = req.body.code;
      const id = req.user.id;
      const platform = req.body.platform;
      const nativeVersion = req.body.nativeVersion;
      let count = 0
      const checkParams = (next) => {
        if (!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getSpecialAuthenHn = (next) => {
        if(regionName !== 'hn' && regionName !== 'vietnam:danang') {
          return next();
        }
        ShipperAuthentication
          .count({
            member: id,
            createdAt: {
              $gt:1748883600000
            },
            uniformPromote: 1
          })
          .exec((err,countShipper) =>{
            if(err) {
              return next(err);
            }
            count = countShipper
            next();
          })
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.COVID_PASSPORT, regionName, (err, data) => {
          if (err) {
            return next(err);
          }

          if (!data) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          const token = crypto.createHash('md5').update(id).digest("hex");
          if (data.config.extras.link) {
            data.config.extras.link = `${data.config.extras.link}?id=${memberCode}&&token=${token}`;
          }
          if(data && data.config && count) {
            data.config.isOpen = 1
          }
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data.config
          })
        })
      }

      async.waterfall([
        checkParams,
        getSpecialAuthenHn,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigShortcutService(req, res) {
      const regionName = req.body.regionName || 'unknown';
      const platform = req.body.platform;
      const nativeVersion = req.body.nativeVersion;
      const userId = req.user.id;
      let expirationDate;

      const checkParams = (next) => {
        if (!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const calculateExpirationDate = (next) => {
        PointTransactionLog
          .find({
            member: userId,
            'data.type': { $in: [0, 2] }
          }, 'createdAt')
          .sort('-createdAt')
          .limit(1)
          .lean()
          .exec((err, results) => {
            if (err) {
              return next(err);
            }

            if (results && results[0]) {
              expirationDate = results[0].createdAt + ms('40d');
            }

            next();
          })
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.SHORTCUT_SERVICE, regionName, (err, data) => {
          if (err) {
            return next(err);
          }

          if (!data) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          if (expirationDate) {
            data.config.expirationDate = expirationDate;
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data.config
          })
        })
      }

      async.waterfall([
        checkParams,
        calculateExpirationDate,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigForceAppDriver(req, res) {
      const regionName = req.body.regionName || 'unknown';
      const platform = req.body.platform;
      const nativeVersion = req.body.nativeVersion;

      const checkParams = (next) => {
        if (!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.FORCE_DRIVER, regionName, (err, data) => {
          if (err) {
            return next(err);
          }

          if (!data) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data.config
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigRoutingShop(req, res) {
      const regionName = req.body.regionName || 'unknown';
      const platform = req.body.platform;
      const nativeVersion = req.body.nativeVersion;

      const checkParams = (next) => {
        if (!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.ROUTING_SHOP, regionName, (err, data) => {
          if (err) {
            return next(err);
          }

          if (!data) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data.config.isOpen || 0
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigTimeAcceptOrder(req, res) {
      const regionName = req.body.regionName || 'unknown';
      const platform = req.body.platform;
      const nativeVersion = req.body.nativeVersion;

      const checkParams = (next) => {
        if (!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.TIME_ACCEPT_ORDER, regionName, (err, data) => {
          if (err) {
            return next(err);
          }

          if (!data) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          let config = {
            expiredTime: 20,
            acceptanceTime: 5,
            expiredTimeBike: 20,
            acceptanceTimeBike: 5,
            maxDistance: 2
          }

          if(data.config) {
            config = data.config
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: config
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigImageFrameBanner(req, res) {
      const regionName = req.body.regionName || 'unknown';

      const checkParams = (next) => {
        if (!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.IMAGE_FRAME, regionName, (err, data) => {
          if (err) {
            return next(err);
          }

          if (!data) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data.config
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigCodePush(req, res) {
      const platform = req.body.platform || '';
      const nativeVersion = req.body.nativeVersion || '';
      const appName = req.body.appName || '';

      const checkParams = (next) => {
        if (!platform || !nativeVersion || !appName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.CODE_PUSH, '', (err, data) => {
          if (err) {
            return next(err);
          }

          if (!data) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          const versionCodePush = data.config[appName][platform][nativeVersion];
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              versionCodePush
            }
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigHotline(req, res) {
      const platform = req.body.platform || '';
      const nativeVersion = req.body.nativeVersion || '';
      const regionName = req.body.regionName || '';

      const checkParams = (next) => {
        if (!platform || !nativeVersion || !regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.HOT_LINE, regionName, (err, data) => {
          if (err) {
            return next(err);
          }

          if (!data) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigGuide(req, res) {
      const nativeVersion = req.body.nativeVersion || '';
      const appName = req.body.appName || 'customer';
      const regionName = req.body.regionName || 'unknown';

      const checkParams = (next) => {
        if (!nativeVersion || !appName || !regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.GUIDE, regionName, (err, data) => {
          if (err) {
            return next(err);
          }

          let isOpen = 0;

          if (data) {
            isOpen = data.config[appName] != nativeVersion;
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: isOpen
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigAuthen(req, res) {
      const platform = req.body.platform || '';
      const nativeVersion = req.body.nativeVersion || '';
      const appName = req.body.appName || 'customer';
      const regionName = req.body.regionName || 'unknown';
      const userId = req.user.id;
      let shipperAuthenInf;

      const checkParams = (next) => {
        if (!nativeVersion || !appName || !regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const checkMember = (next) => {
        ShipperAuthentication
          .findOne({member: userId}, 'onlineAuthen')
          .lean()
          .exec((err, result) => {
            if (result && !result.onlineAuthen) {
              return next({
                code: CONSTANTS.CODE.SUCCESS,
                data: {
                  online: 0,
                  offline: 1
                }
              })
            }

            shipperAuthenInf = result;

            next();
          })
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.AUTHEN, regionName, (err, data) => {
          if (err) {
            return next(err);
          }

          if (!data || !data.config) {
            return next(new Error('Invalid config'));
          }

          let result = {
            code: CONSTANTS.CODE.SUCCESS,
            data: data.config
          }


          if(regionName === 'vietnam:nghean' && ((platform === 'ios' && nativeVersion < 600041) || (platform === 'android' && nativeVersion < 60036))) {
            if(result && result.data && result.data.online) {
              result.data.online = 0
            }
          }

          next(null, result);
        })
      }

      async.waterfall([
        checkParams,
        checkMember,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getStateAuthen(req, res) {
      const userId = req.user.id;
      let isTaxi = req.body.isTaxi;
      let status = 0;

      const checkAuthenShipInf = (next) => {

        let query = {
          member: userId
        }
        if(isTaxi) {
          query.isTaxi = 1;
        } else {
          query.isTaxi = {
            $ne: 1
          }
        }

        AuthenShipInf
          .findOne(query)
          .lean()
          .exec((err, result) => {
            if (err) {
              return next(err);
            }

            if (result) {
              status = 1;
            }

            next();
          });
      }

      const checkTraining = (next) => {
        Members
          .findOne({
            _id: userId
          }, 'training')
          .lean()
          .exec((err, result) => {
            if (err) {
              return next(err);
            }

            if (result && result.training) {
              if (status === 1) {
                status = 3;
              } else {
                status = 2;
              }
            }

            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                status,
                message: ''
              }
            });
          });
      }

      async.waterfall([
        checkAuthenShipInf,
        checkTraining,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    removeAccount(req, res) {
      const userId = req.user.id || '';
      const deviceId = req.body.deviceId;
      let phone;

      const removeAccount = (next) => {
        Members
          .findOneAndUpdate({
            _id: userId,
            'ship.isAuthen': 0
          }, {
            name: '',
            'facebook.name': ''
          })
          .lean()
          .exec((err, result) => {
            if (err) {
              return next(err);
            }

            if (!result) {
              return next({
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: {
                  head: 'Thông báo',
                  body: 'Hiện tại chúng tôi chưa hỗ trợ xoá tài khoản đối với đối tác tài xế. Bạn vui lòng liên hệ hotline 1900.633.689 để yêu cầu xoá tài khoản. Xin cảm ơn.'
                }
              })
            }

            phone = result.phone;

            next();
          });
      }

      const saveOTP = (next) => {
        if (phone && deviceId) {
          redisConnection.setex(`otp:${phone}`, ms('3h')/1000, deviceId);
        }

        setTimeout(() => {
          redisConnection.del(`user:${req.body.memberToken}`);

          next(null, {
            code: 1993,
            message: {
              head: 'Thông báo',
              body: 'Xoá tài khoản thành công'
            }
          });
        }, 300);
      }

      async.waterfall([
        removeAccount,
        saveOTP
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: {
            head: 'Thông báo',
            body: 'Xoá tài khoản thất bại'
          }
        });

        res.json(data || err);
      });
    },
    getConfigRemoveAccount(req, res) {
      const platform = req.body.platform || '';
      const regionName = req.body.regionName || '';
      const modeApp = req.body.modeApp || '';
      let data = {
        isOpen: 0
      }

      const checkParams = (next) => {
        if (!platform || !regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config
          .findOne({
            status: 1,
            $or:[{
              'region.allow':'all',
              'region.deny':{
                $ne: regionName
              }
            },{
              'region.allow': regionName
            }],
            platform,
            modeApp
          })
          .lean()
          .exec((err, result) => {
            if(err) {
              return next(err)
            }
            if(result && result.config) {
              data = result.config

              if (modeApp === 'shipper') {
                data.actions.map(action => {
                  if (action.data.link === 'ProfileShopScreen') {
                    action.data.link = 'ProfileScreen';
                  }
                })
              }
            }
            next(null,{
              code: CONSTANTS.CODE.SUCCESS,
              data
            });
          })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigLocationShipper(req, res) {
      const regionName = req.body.regionName || 'unknown';
      const userId = req.user.id || '';
      let data = {
        require: 0,
        permissions: 0, // AUTHORIZED: 1, AUTHORIZED_FOREGROUND: 2, NOT_AUTHORIZED: 0
        member: []
      }

      const checkParams = (next) => {
        if (!regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config
          .get(CONSTANTS.CONFIG_TYPE.LOCATION_SHIPPER, regionName, (err, result) => {
            if (err) {
              return next(err);
            }

            if (result && result.config) {
              data = result.config
            }

            next();
          })
      }

      const getMember = (next) => {
        if (data.member.includes(userId)) {
          return next({
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              require: 0,
              permissions: 0
            }
          });
        }

        Members
          .findOne({
            _id: userId
          })
          .lean()
          .exec((err, result) => {
            if (!err && (!result || (!result.ship.isAuthen && (!result.staff || !result.staff.isAuthen) ))) {
              return next({
                code: CONSTANTS.CODE.SUCCESS,
                data: {
                  require: 0,
                  permissions: 0
                }
              });
            }

            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data
            })
          })
      }

      async.waterfall([
        checkParams,
        getConfig,
        getMember
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigAmountDeposit(req, res) {
      const platform = req.body.platform || '';
      const regionName = req.body.regionName || '';
      let data = {
        isOpen: 0
      }

      const checkParams = (next) => {
        if (!platform || !regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.AMOUNT_DEPOSIT, regionName, (err, data) => {
          if (err) {
            return next(err);
          }

          if (!data) {
            return next({
              code: CONSTANTS.CODE.SUCCESS
            })
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data.config
          })
        })
      }

      async.waterfall([
        checkParams,
        getConfig,
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getConfigBlockShipper(req, res) {
      const regionName = req.body.regionName || '';
      const userId = req.user.id || '';

      let member
      let configData;

      const checkParams = (next) => {
        if (!userId || !regionName) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.BLOCK_SHIPPER_ONLINE, regionName, (err, dataConfig) => {
          if (err || !dataConfig) {
            return next(err || new Error('Config not found'));
          }
          let data = dataConfig.config || ''
          if (!data || (data && !data.isOpen)) {
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                isOpen: 0
              }
            })
          }

          // if (data.isOpen && !data.online) {
          //   return next({
          //     code: CONSTANTS.CODE.SUCCESS,
          //     data
          //   })
          // }

          configData = data

          next(null)
        })
      }


      const checkAccountBlock = (next) => {
        Members
          .findOne({
            _id: userId
          })
          .select('blockOrderUtil training')
          .lean()
          .exec((err, result) => {
            if(err){
              return next(err);
            }
            if(!result) {
              return next({
                code: CONSTANTS.CODE.SYSTEM_ERROR,
              })
            }
            member = result
            if(member.blockOrderUtil < Date.now()) {
              return next({
                code: CONSTANTS.CODE.SUCCESS,
                data: {
                  isOpen: 0
                }
              })
            }
            next();
          })
      }

      const showInfo = (next) => {
        BlockLog
          .find({
            member: userId,
            blockType: 'blockOrderUtil',
            status: -1,
            'data.timeBlockUtil':member.blockOrderUtil
          })
          .sort('-createdAt')
          .limit(1)
          .lean()
          .exec((err, result) => {
            if(err) {
              return next(err)
            }

            if(!result.length) {
              return next({
                code: CONSTANTS.CODE.SUCCESS,
                data: {
                  isOpen: 0
                }
              })
            }
            if(!configData.online) {
              return next({
                code: CONSTANTS.CODE.SUCCESS,
                data: {
                  isOpen: 1,
                  online: 0,
                  message: result[0].message,
                  description: result[0].description,
                  blockInf: {
                    _id: result[0]._id,
                    blockOrderUtil: member.blockOrderUtil
                  }
                }
              })
            }
            if(!result[0].reTraining && result[0].type !== 'REST_BLOCK') {
              return next({
                code: CONSTANTS.CODE.SUCCESS,
                data: {
                  isOpen: 0
                }
              })
            }
            if(member.training && result[0].reTraining) {

              Members
                .update({
                  _id: userId
                },{
                  training: 0
                }, () => {
                })
            }
            next(null,{
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                isOpen: 1,
                online: 1,
                description: result[0].description,
                message: result[0].message,
                blockInf: {
                  _id: result[0]._id,
                  blockOrderUtil: member.blockOrderUtil,
                  amount: result[0].amount,
                  reTraining: result[0].reTraining,
                  statusTraining: result[0].statusTraining,
                  statusPayment: result[0].statusPayment,
                  statusInfo: result[0].statusInfo,
                  identityFont: result[0].identityFont,
                  identityBack: result[0].identityBack,
                  avatarWithUniform: result[0].avatarWithUniform,
                  reasonReject: result[0].reasonReject,
                  lock90d: result[0].type === 'REST_BLOCK' && regionName === 'hn'
                }
              }
            })
          })
      }

      async.waterfall([
        checkParams,
        getConfig,
        checkAccountBlock,
        showInfo
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    saveHtmlZalo(req, res) {
      const content = req.body.content || '';
      // const userId = req.user.id || '';

      const insertToDb = (next) => {
        console.log('haha:content', content)

        next(null, {
          code: CONSTANTS.CODE.SUCCESS
        });
      }

      async.waterfall([
        insertToDb,
      ], (err, data) => {
        if (_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }

        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
}
