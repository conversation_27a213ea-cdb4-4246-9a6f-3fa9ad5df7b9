import { Router } from 'express';
import moment from 'moment';
import _ from 'lodash';
import * as FB from 'fb';
import * as async from 'async';
import uuid from 'uuid/v4';
import CONSTANTS from '../../const';
import message from '../../message';
import rp from 'request-promise'
import ms from 'ms';
import * as config from '../../config';
import LocationManager from '../../task/updateLocation';
import {locationServerAddr, proxyFacebookServerAddr, SANSHIP_FB_APPID, authenServiceAddr, notifyServiceAddr, SANSHIP_HEYU_FB_APPID, codePhoneAddr, service, shopTransactionType, tickTransactionType, productAddr, mediaServerAddr} from '../../config';
import request from 'request';
import bcrypt from 'bcrypt';
import isMobilePhone from 'validator/lib/isMobilePhone';
import * as helpers from '../../helpers';
import * as mailUtil from '../../mailUtil';

export default {
    getRegionByLatLng(req, res) {
      const location = req.body.location;
      const level = req.body.level;
      const currentRegion = req.body.regionName;
      const appName = _.get(req,'body.appName', '')

      let region,regionDistrict;
      const getRegionFromLocationService = (next) => {
        const options = {
          method: 'POST',
          uri: `${locationServerAddr}/api/v2.0/region/lat-lng`,
          body: {
            location,
            level
          },
          json: true // Automatically stringifies the body to JSON
        }

        rp(options)
          .then((result) => {
            if(result.code !== CONSTANTS.CODE.SUCCESS) {
              return next(result);
            }

            region = result.data;
            const objPublish = {
              userId: req.user.id,
              region
            }
            redisConnectionForPubSub.publish("update_region", JSON.stringify(objPublish));
            return next();
          })
          .catch((err) => {
            mailUtil
              .sendMail(` --- ERR getRegionByLatLng ${locationServerAddr}/api/v2.0/region/lat-lng --- ${err}`);
            return next(err);
          });
      }

      const getRegionDistrict = (next) => {
        const options = {
          method: 'POST',
          uri: `${locationServerAddr}/api/v2.0/region/lat-lng`,
          body: {
            location,
            level: 3,
          },
          json: true // Automatically stringifies the body to JSON
        }

        rp(options)
          .then((result) => {
            if(result.code !== CONSTANTS.CODE.SUCCESS) {
              return next(result);
            }

            regionDistrict = result.data;

            next();
          })
          .catch((err) => {
            mailUtil
              .sendMail(` --- ERR getRegionByLatLng ${locationServerAddr}/api/v2.0/region/lat-lng --- ${err}`);
            return next(err);
          });
      }

      const setRedisRegion = (next) => {
        let memberData = req.user;
        memberData.regionDistrict = regionDistrict

        let stringToken = `user:${req.user.memberToken}`
        if(appName) {
          stringToken = `${appName}:${req.user.memberToken}`
        }

        redisConnection.set(stringToken, JSON.stringify(memberData), (err, res) => {
            if(err) {
                return next(err);
            }
            next()
        });
      }

      const updateToUserInf = (next) => {
        // if(currentRegion === region) {
        //   return next(null, {
        //     code: CONSTANTS.CODE.SUCCESS,
        //     data: region
        //   })
        // }

        const objUpdate = {
          region,
          regionDistrict
        };

        // if (region === 'vietnam:quangninh') {
        //   objUpdate.training = 1;
        //   objUpdate.receivePushOrder = 1;
        // }

        Members
          .findOneAndUpdate({
            _id: req.user.id
          }, objUpdate, (err, result) => {
            if(err) {
              return next(err);
            }
            if(result && result.phone) {
              CodePhone
                .find({
                  phone: result.phone
                })
                .sort('-createdAt')
                .limit(1)
                .lean()
                .exec((err, results) => {
                  if(results && results.length && !results[0].region) {
                    CodePhone
                      .update({
                        _id: results[0]._id
                      },{
                        region,
                        updatedAt: Date.now()
                      },() =>{})
                  }
                })
            } else {
              console.log("ahihi",req.user.id, req.body)
            }

            //   if(result && !result.region && result.phone && result.type === 2 && region === 'vietnam:khanhhoa') {
            //     const options = {
            //       method: 'POST',
            //       uri: `https://push.heyu.asia/api/v1.0/push-notification/all`,
            //       body: {
            //           title: "Shop mới Nha Trang",
            //           message: `Shop ${result.phone} vừa mới đăng ký tài khoản tại Nha Trang. Admin vui lòng chăm sóc shop!`,
            //           "query":{
            //               "phone":{
            //                   "$in":["0913970025","0358964930","0355291155","0387289950","0913525381","0971444698","0973626496","0983888421","0968349421","0357948523"]
            //               }
            //           }
            //       },
            //       json: true // Automatically stringifies the body to JSON
            //     };
            //
            //     rp(options)
            //       .then((result) => {
            //       })
            //       .catch((err) => {
            //       });
            //
            // }

            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: region
            })
          })
      }

      async.waterfall([
        getRegionFromLocationService,
        getRegionDistrict,
        setRedisRegion,
        updateToUserInf
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: message.SYSTEM.ERROR
        })

        res.json(data || err)
      })
    },
    checkCode(req, res) {
      const options = {
        method: 'POST',
        uri: `${codePhoneAddr}/api/v1.0/check-code`,
        body: {
            phone: req.body.phone,
            code: req.body.code,
            token: req.body.token
        },
        json: true // Automatically stringifies the body to JSON
      };

      rp(options)
        .then((result) => {
          res.json(result);
        })
        .catch((err) => {
          if(_.isError(err)) {
            logger.logError([err], req.originalUrl, req.body);
          }
          mailUtil
            .sendMail(` --- ERR ${codePhoneAddr}/api/v1.0/check-code --- ${err}`);
          res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: message.SYSTEM.ERROR
          })
        });
    },
    getPhoneCode(req, res) {
      const phone = req.body.phone;

      let users;
      const checkMultipleAccount = (next) => {
        Members
          .find({
            phone
          }, "blockUtil")
          .lean()
          .exec((err, results) => {
            if(err) {
              return next(err);
            }

            if(results.length > 1) {
              return next({
                code: CONSTANTS.CODE.FAIL,
                message: message.USER.MULTIPLE_ACCOUNT
              })
            }

            users = results;

            next();
          })
      }

      const checkBlock = (next) => {
        let isBlock = false;
        for(let i=0; i < users.length; i++) {
          if(users[i].blockUtil - Date.now() >= 0) {
            isBlock = true;
            break;
          }
        }

        if(!isBlock) {
          return next();
        }

        let userInfDB = users[0];

        ReasonBlock
          .find({member: userInfDB._id})
          .sort({"createdAt": -1})
          .limit(1)
          .lean()
          .exec((err, resultBlock) => {
            if(err) {
              return next({
                  code: CONSTANTS.CODE.SYSTEM_ERROR,
                  message: message.SYSTEM.ERROR
              });
            }

            let messageBlock;
            if(resultBlock.length === 1 && resultBlock[0].message) {
              messageBlock = {
                head: 'Thông báo',
                body: `Tài khoản của bạn đã bị khoá tới ${moment(userInfDB.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')} (${resultBlock[0].message}). Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
              }
            } else {
              messageBlock = {
                head: 'Thông báo',
                body: `Tài khoản của bạn đã bị khoá tới ${moment(userInfDB.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')}. Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
              }
            }

            next({
              code: CONSTANTS.CODE.FAIL,
              message: messageBlock
            })
          })
      }

      const sendCode = (next) => {
        const options = {
          method: 'POST',
          uri: `${codePhoneAddr}/api/v1.0/send-code`,
          body: {
              phone: req.body.phone,
              ip: req.headers['x-forwarded-for'],
              deviceId: req.body.deviceId,
              platform: req.body.platform
          },
          json: true // Automatically stringifies the body to JSON
        };

        rp(options)
          .then((result) => {
            next(null, result);
          })
          .catch((err) => {
            mailUtil
              .sendMail(` --- ERR ${codePhoneAddr}/api/v1.0/send-code --- ${err}`);

            next(err);
          });
      }

      async.waterfall([
        checkMultipleAccount,
        checkBlock,
        sendCode
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: message.SYSTEM.ERROR
        })

        res.json(data || err)
      })
    },
    historyTransaction(req, res) {
      const from = req.body.from || Date.now();
      const limit = req.body.limit || 10;
      const userId = req.user.id;
      const restTransaction = tickTransactionType.concat(shopTransactionType)
      TransactionLog
        .find({
          member: userId,
          createdAt: {
            $lt: from
          },
          'data.type':{$nin: restTransaction}
        })
        .sort("-createdAt")
        .limit(limit)
        .lean()
        .exec((err, results) => {
          if(err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: message.SYSTEM.ERROR
            })
          }

          results.forEach((transaction) => {
            if(transaction.data && transaction.data.back && [4,10,15,16].includes(transaction.data.type)) {
              transaction.message = "Trả lại phí";
            }

            if(transaction.data && !transaction.data.back && [22, 23].includes(transaction.data.type)) {
              transaction.message = "Thanh toán qua ứng dụng";
            }
          });

          return res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: results
          })
        })
    },

    transferCODToMoney(req, res) {
      const amount = Math.round(Math.abs(req.body.amount || 0));
      let region = req.body.region || ''
      if(!region) {
        region = req.body.regionName
      }
      const userId = req.user.id;
      let amountShop = 0
      const checkAmount = (next) => {
        if(typeof amount !== 'number' || amount <= 0 || !region) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: message.SYSTEM.ERROR
          });
        }
        next();
      }
      const checkTime = (next) => {
        // Check time
        const currentDate = new Date();
        if(currentDate.getHours() < 22 && currentDate.getHours() >= 9) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: message.USER.TIME_TRANSACTION_SSM
          })
        }
        next();
      }

      const updateMoney = (next) => {
        Members
          .findOneAndUpdate({
            _id: userId,
            moneyCOD: {
              $gte: amount
            }
          }, {
            $inc: {
              moneyCOD: -amount,
              money: amount
            }
          }, {'new': true}, (err, result) => {
            if(err || !result) {
              return next({
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: message.SYSTEM.ERROR
              });
            }
            if(result && result.isBlockStore && result.money > 0) {
              const options = {
                method: 'POST',
                uri: `${productAddr}/api/v1.0/admin/store/unblock`,
                body: {
                    id: userId
                },
                json: true // Automatically stringifies the body to JSON
              };

              rp(options)
                .then((result) => {
                })
                .catch((err) => {
                });
            }

            TransactionLog
              .create({
                member: userId,
                region,
                data: {
                  amount: -amount,
                  initialCoints: result.coints,
                  finalCoints: result.coints,
                  initialRealMoney: result.realMoney,
                  finalRealMoney: result.realMoney,
                  initialRealMoneyShop: result.realMoneyShop,
                  finalRealMoneyShop: result.realMoneyShop,
                  initialMoney: result.money - amount,
                  finalMoney: result.money,
                  initialCOD: result.moneyCOD + amount,
                  finalCOD: result.moneyCOD,
                  type: 35
                },
                message: "Chuyển tiền COD sang tài khoản TickBox"
              }, (err) => {
                if(err) {
                  return next(err);
                }

                return next({
                  code: CONSTANTS.CODE.SUCCESS,
                  message: {
              			head: 'Thông báo',
              			body: `Bạn đã chuyển thành công ${amount} đ từ tài khoản COD sang tài khoản TickBox`
              		}
                })
              })
          });
      }

      async.waterfall([
        checkAmount,
        // checkTime,
        updateMoney
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: message.SYSTEM.ERROR
        })

        res.json(data || err)
      })
    },
    transferSSMToCoint(req, res) {
      const amount = Math.round(Math.abs(req.body.amount || 0));
      const userId = req.user.id;
      let amountShop = 0
      let regionTran;

      const checkAmount = (next) => {
        if(typeof amount !== 'number' || amount <= 0) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: message.SYSTEM.ERROR
          });
        }
        if(amount < 50000) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: message.USER.MIN_MONEY_TRANSFER
          });
        }
        next();
      }
      const checkTime = (next) => {
        // Check time
        const currentDate = new Date();
        if(currentDate.getHours() < 22 && currentDate.getHours() >= 9) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: message.USER.TIME_TRANSACTION_SSM
          })
        }
        next();
      }

      const checkRegionTransaction = (next) => {
        Members
          .findById(userId)
          .select('regionTransaction realMoney realMoneyShop')
          .lean()
          .exec((err, result) => {
            if(err) {
              return next(err);
            }
            if(!result || !result.regionTransaction) {
              return next({
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: message.SYSTEM.ERROR
              })
            }
            regionTran = result.regionTransaction
            if(result.realMoneyShop) {
              if((result.realMoney - result.realMoneyShop) < amount) {
                amountShop = amount - (result.realMoney - result.realMoneyShop)
              }
            }
            next();
          })
      }

      const updateMoney = (next) => {
        Members
          .findOneAndUpdate({
            _id: userId,
            realMoney: {
              $gte: amount
            }
          }, {
            $inc: {
              realMoney: -amount,
              coints: amount,
              realMoneyShop: -amountShop
            }
          }, {'new': true}, (err, result) => {
            if(err || !result) {
              return next({
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: message.SYSTEM.ERROR
              });
            }

            TransactionLog
              .create({
                member: userId,
                region: regionTran,
                data: {
                  amount: -amount,
                  discount: 0,
                  bonus: 0,
                  initialCoints: result.coints - amount,
                  finalCoints: result.coints,
                  initialRealMoney: result.realMoney + amount,
                  finalRealMoney: result.realMoney,
                  initialRealMoneyShop: result.realMoneyShop + amountShop,
                  finalRealMoneyShop: result.realMoneyShop,
                  type: 0
                },
                message: "Chuyển SSM sang Coints"
              }, (err) => {
                if(err) {
                  return next(err);
                }

                return next({
                  code: CONSTANTS.CODE.SUCCESS,
                  data: {
                    coints: result.coints,
                    realMoney: result.realMoney
                  },
                  message: message.USER.TRANSFER_SSM_TO_COINT
                })
              })
          });
      }

      async.waterfall([
        checkAmount,
        checkTime,
        checkRegionTransaction,
        updateMoney
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: message.SYSTEM.ERROR
        })

        res.json(data || err)
      })
    },

    getInfoFromToken_v2(req, res) {
      const access_token = req.body.access_token;
      let userInf;
      const checkParams = (next) => {
          if(!_.isString(access_token) || !access_token.length) {
              return next({
                  code: CONSTANTS.CODE.WRONG_PARAMS,
                  message: message.SYSTEM.ERROR
              });
          }

          next();
      }

      const checkAppId = (next) => {
        const options = {
          method: 'GET',
          uri: `${proxyFacebookServerAddr}/api/v1.0/facebook/get-app-info`,
          qs: {
            access_token
          },
          json: true
        };

        request(options, (err, response, result) => {
          if(err || !response || response.statusCode !== 200 || !result || result.code !== CONSTANTS.CODE.SUCCESS) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: message.SYSTEM.ERROR
            })
          }

          if(result.data.id !== SANSHIP_FB_APPID && result.data.id !== SANSHIP_HEYU_FB_APPID) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: 'Internal Server Error'
            });
          }

          return next(null);
        });
      }

      const getUserInf = (next) => {
        const options = {
          method: 'POST',
          uri: `${proxyFacebookServerAddr}/api/v1.0/facebook/get-profile`,
          body: {
              access_token
          },
          json: true // Automatically stringifies the body to JSON
        };

        request(options, (err, response, result) => {
          if(err || !response || response.statusCode !== 200 || !result) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: message.SYSTEM.ERROR
            })
          }

          if(result.code !== CONSTANTS.CODE.SUCCESS) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: message.SYSTEM.ERROR
            })
          }

          userInf = result.userInfo;
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: userInf
          });
        })
      }

      async.waterfall([
        checkParams,
        checkAppId,
        getUserInf
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        if(err) {
          return res.json(err);
        }

        res.json(data);
      })
    },
    updateProfile(req, res) {
      const userId = req.user.id;
      const name = req.body.name || '';
      const email = req.body.email || '';
      const avatar = req.body.avatar || '';
      const access_token = req.body.access_token || '';
      const idFB = req.body.id || '';

      let shouldMergeFb = false;
      let userInf;

      const checkParams = (next) => {
        if(!name) {
          return res.json({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: message.SYSTEM.ERROR
          })
        }

        next();
      }

      const getUserInfo = (next) => {
        Members
          .findById(userId)
          .lean()
          .exec((err, result) => {
            if(err || !result) {
              return next(err || new Error(`Not found user info`));
            }

            userInf = result;

            next();
          });
      }

      const checkCanMergeFB = (next) => {
        if(!idFB || userInf.facebook.id) {
          return next();
        }

        return next();

        Members
          .count({
            'facebook.id': idFB
          })
          .exec((err, count) => {
            if(!err && count === 0) {
              shouldMergeFb = true;
            }

            next();
          })
      }

      const updateUserInf = (next) => {
        const objUpdate = {
          name,
          email
        }

        if(avatar) {
          objUpdate['facebook.picture'] = avatar;
        }

        if(access_token) {
          objUpdate['facebook.token'] = access_token;
        }

        if(shouldMergeFb) {
          objUpdate['facebook.id'] = idFB;
        }

        if(!userInf.facebook.picture) {
          objUpdate['facebook.name'] = name;
          objUpdate['facebook.email'] = email;
        }

        Members
          .update({
            _id: req.user.id
          }, objUpdate)
          .exec((err, result) => {
            if(err) {
              return next(err);
            }

            return next(null, {
              code: CONSTANTS.CODE.SUCCESS
            })
          })
      }

      async.waterfall([
        checkParams,
        getUserInfo,
        checkCanMergeFB,
        updateUserInf
      ], (err, data) => {
        err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: message.SYSTEM.ERROR
        });

        res.json(data || err);
      })
    },
    getInfoFromToken(req, res) {
      const access_token = req.body.access_token;
      let userInf;
      const checkParams = (next) => {
          if(!_.isString(access_token) || !access_token.length) {
              return next({
                  code: CONSTANTS.CODE.WRONG_PARAMS,
                  message: message.SYSTEM.ERROR
              });
          }

          next();
      }

      const checkAppId = (next) => {
        const options = {
          method: 'GET',
          uri: `${proxyFacebookServerAddr}/api/v1.0/facebook/get-app-info`,
          qs: {
            access_token
          },
          json: true
        };

        request(options, (err, response, result) => {
          if(err || !response || response.statusCode !== 200 || !result || result.code !== CONSTANTS.CODE.SUCCESS) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: message.SYSTEM.ERROR
            })
          }

          if(result.data.id !== SANSHIP_FB_APPID && result.data.id !== SANSHIP_HEYU_FB_APPID) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: 'Internal Server Error'
            });
          }

          return next(null);
        });
      }

      const getUserInf = (next) => {
        const options = {
          method: 'POST',
          uri: `${proxyFacebookServerAddr}/api/v1.0/facebook/get-profile`,
          body: {
              access_token
          },
          json: true // Automatically stringifies the body to JSON
        };

        request(options, (err, response, result) => {
          if(err || !response || response.statusCode !== 200 || !result) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: message.SYSTEM.ERROR
            })
          }

          if(result.code !== CONSTANTS.CODE.SUCCESS) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: message.SYSTEM.ERROR
            })
          }

          userInf = result.userInfo;
          next(null);
        })
      }

      const checkDuplicateFacebook = (next) => {
        Members
          .count({
            'facebook.id': userInf.id
          })
          .exec((err, count) => {
            if(err) {
              return next({
                code: CONSTANTS.CODE.WRONG_PARAMS,
                message: message.SYSTEM.ERROR
              })
            }

            if(count > 0) {
              return next({
                code: CONSTANTS.CODE.FAIL,
                message: message.USER.MAXIMUM_FACEBOOK
              })
            }

            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: userInf
            })
          })
      }

      async.waterfall([
        checkParams,
        checkAppId,
        getUserInf,
        checkDuplicateFacebook
      ], (err, data) => {
        if(err) {
          return res.json(err);
        }

        res.json(data);
      })
    },
    addPresenterCode(req, res) {
      res.json({
        code: CONSTANTS.CODE.SUCCESS
      });

      const presenterCode = req.body.presenterCode;
      const modeApp = req.body.modeApp;
      const userId = req.user.id;

      if(presenterCode) {
        Members
          .findOne({$or: [{
            phone: presenterCode
          }, {
            code: presenterCode
          }]})
          .sort({createdAt: -1})
          .lean()
          .exec((err, result) => {
            if(_.isError(err)) {
              logger.logError([err], req.originalUrl, req.body);
            }
            if(!err) {

              Presenter
                .create({
                  presenter: result ? result._id : null,
                  member: userId,
                  presenterCode: presenterCode
                }, (err, res) => {
                  const objPublish = {
                    userId,
                    type: 2
                  }

                  if (modeApp === 'shipper') {
                    objPublish.type = 0;
                  } else if (modeApp === 'merchant') {
                    objPublish.type = 3;
                  }

                  redisConnectionForPubSub.publish("user_choose_mode", JSON.stringify(objPublish));

                  if(result && result._id && modeApp === 'shipper') {
                    if(result.region === 'hcm') {
                      Members
                        .findOne({
                          _id: result._id,
                          'ship.isAuthen': 1
                        },(err, resultData) => {
                          if(resultData) {

                            Presenter
                              .update({
                                _id: res._id
                              },{
                                status: 0,
                                type: 1,
                                region: 'hcm'
                              },() => {})

                            const options = {
                              method: 'POST',
                              uri: `${notifyServiceAddr}/api/v1.0/push-notification/member`,
                              body: {
                                  userId: result._id.toString(),
                                  title: "Giới thiệu thành công!",
                                  appName: 'driver',
                                  message: `Hệ thống ghi nhận bạn đã giới thiệu tài khoản với số điện thoại ${resultData.phone}.Để nhận được phần thưởng hấp dẫn, vui lòng nhắc người được giới thiệu qua văn phòng HeyU để đăng ký xác thực tài khoản. Xin cảm ơn!`,
                              },
                              json: true // Automatically stringifies the body to JSON
                            };

                            request(options, (err, response) => {
                            });
                          }
                        })
                    }
                  }
                });
            }
          })
      }
    },
    loginByPhone(req, res) {
      const phone = req.body.phone;
      const password = req.body.password;
      let userInfDB;
      let memberToken;

      const checkParams = (next) => {
        // TODO:
        if(!phone || password.length < 6) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: message.SYSTEM.ERROR
          });
        }

        next(null);
      }

      const getUserInf = (next) => {
        const aliasPhone = helpers.getAliasPhone(phone);
        const aliasPhone1 = helpers.convertToNewPhone(phone);

        let query = {
          phone
        }

        if(aliasPhone || aliasPhone1) {
          query = {
            '$or': [
              {
                phone
              },
              {
                phone: aliasPhone || aliasPhone1
              }
            ]
          }
        }

        memberToken = uuid();
        Members
          .findOne(query)
          .lean()
          .exec((err, result) => {
            if(err) {
              return next(err);
            }

            if(!result) {
              return next({
                code: CONSTANTS.CODE.FAIL,
                message: {
                  'head': 'Thông báo',
                  'body': 'Không tồn tại tài khoản nào với số điện thoại này, vui lòng thử lại. Xin cảm ơn.'
                }
              })
            }

            userInfDB = result;
            next();
          })
        }

        const checkPassword = (next) => {
          if(!userInfDB.password) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: {
                'head': 'Thông báo',
                'body': 'Bạn chưa có mật khẩu, vui lòng nhấn vào Quên mật khẩu Hoặc chưa có mật khẩu phía dưới để tạo mật khẩu. Cảm ơn bạn.'
              }
            })
          }

          bcrypt.compare(password, userInfDB.password, function(err, res) {
            if(err) {
              return next(err);
            }

            if(!res) {
              return next({
                code: CONSTANTS.CODE.FAIL,
                message: {
                  'head': 'Thông báo',
                  'body': 'Mật khẩu không chính xác, vui lòng thử lại. Xin cảm ơn.'
                }
              })
            }

            if(userInfDB.blockUtil > Date.now()) {
              ReasonBlock
                .find({member: userInfDB._id})
                .sort({"createdAt": -1})
                .limit(1)
                .lean()
                .exec((err, resultBlock) => {
                  if(err) {
                    return next({
                        code: CONSTANTS.CODE.SYSTEM_ERROR,
                        message: message.SYSTEM.ERROR
                    });
                  }

                  let messageBlock;
                  if(resultBlock.length === 1 && resultBlock[0].message) {
                    messageBlock = {
                      head: 'Thông báo',
                      body: `Tài khoản của bạn đã bị khoá tới ${moment(userInfDB.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')} (${resultBlock[0].message}). Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                    }
                  } else {
                    messageBlock = {
                      head: 'Thông báo',
                      body: `Tài khoản của bạn đã bị khoá tới ${moment(userInfDB.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')}. Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                    }
                  }

                  return next({
                    code: CONSTANTS.CODE.FAIL,
                    message: messageBlock
                  })
                })

              return;
            }

            next();
          });
      }

      const makeSingleLogin = (next) => {
          const userId = userInfDB._id.toHexString();
          redisConnection.get(`user:${userId}`, (err, token) => {
              if(err) {
                  return next(err);
              }

              if(token) {
                  redisConnection.del(`user:${token}`, (err, result) => {
                      if(err) {
                          return next(err);
                      }

                      next();
                  });
              } else {
                  next();
              }
          });
      }

      const generateToken = (next) => {
          const userId = userInfDB._id.toHexString();
          const objSign = {
              id: userId,
              accessToken: _.get(req, 'body.access_token', '')
          }

          redisConnection
              .multi()
              .set(`user:${userId}`, memberToken)
              .set(`user:${memberToken}`, JSON.stringify(objSign))
              .exec((err, result) => {
                  if(err) {
                      return next(err);
                  }

                  redisConnection.del(`otp:${phone}`);

                  const data = _.merge({}, userInfDB, {memberToken});
                  _.unset(data, 'facebook.token');
                  _.unset(data, 'password');
                  data.isExpire = (data.expireTime < Date.now());
                  next(null, {
                    code: CONSTANTS.CODE.SUCCESS,
                    member: data
                  });
              });
      }

      async.waterfall([
        checkParams,
        getUserInf,
        checkPassword,
        makeSingleLogin,
        generateToken
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: message.SYSTEM.ERROR
        });

        res.json(data || err);
      });
    },
    resetPassword(req, res) {
      const phone = req.body.phone;
      const newPassword = req.body.newPassword;
      const token = req.body.token;

      let passwordHashed = '';
      let memberToken = '';
      let userInfDB;

      const checkParams = (next) => {
        if(!phone || newPassword.length < 6 || !token) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: message.SYSTEM.ERROR
          });
        }
        next(null);
      }

      const getPhoneFromToken = (next) => {
        const options = {
          method: 'POST',
          uri: `${proxyFacebookServerAddr}/api/v1.0/facebook/get-phone-account-kit`,
          body: {
              token
          },
          json: true // Automatically stringifies the body to JSON
        };

        request(options, (err, response, result) => {
          if(err || !response || response.statusCode !== 200 || !result || result.code !== 200) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: message.SYSTEM.ERROR
            })
          }

          if(result.data.application.id !== SANSHIP_FB_APPID && result.data.application.id !== SANSHIP_HEYU_FB_APPID) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: 'Internal Server Error'
            });
          }

          const phoneFromToken = `0${_.get(result, 'data.phone.national_number', '')}`

          if(phoneFromToken !== phone) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: 'Internal Server Error'
            });
          }

          next();
        })
      }

      const hashPassword = (next) => {
        bcrypt.hash(newPassword, 10, function(err, hash) {
          if(err) {
            return next(err);
          }

          passwordHashed = hash;
          next();
        });
      }

      const updatePassword = (next) => {
        memberToken = uuid();
        const options = {
          'new': true,
          upsert: true,
          setDefaultsOnInsert: true
        };

        Members
          .findOneAndUpdate({
              phone: phone
          }, {
            password: passwordHashed,
            status: 1
          }, options)
          .lean()
          .exec((err, result) => {
              if(err || !result) {
                return next(err || new Error(`Not found user with that phone`));
              }

              if(result.blockUtil > Date.now()) {
                ReasonBlock
                  .find({member: result._id})
                  .sort({"createdAt": -1})
                  .limit(1)
                  .lean()
                  .exec((err, resultBlock) => {
                    if(err) {
                      return next({
                          code: CONSTANTS.CODE.SYSTEM_ERROR,
                          message: message.SYSTEM.ERROR
                      });
                    }

                    let messageBlock;
                    if(resultBlock.length === 1 && resultBlock[0].message) {
                      messageBlock = {
                        head: 'Thông báo',
                        body: `Tài khoản của bạn đã bị khoá tới ${moment(result.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')} (${resultBlock[0].message}). Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                      }
                    } else {
                      messageBlock = {
                        head: 'Thông báo',
                        body: `Tài khoản của bạn đã bị khoá tới ${moment(result.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')}. Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                      }
                    }

                    return next({
                      code: CONSTANTS.CODE.FAIL,
                      message: messageBlock
                    })
                  })

                return;
              }

              userInfDB = result;
              next();
          });
      }

      const handleRegisterService = (next) => {
        OrderType
          .find({move: {$ne: 1}, hireDriver: {$ne:1}}, '_id')
          .lean()
          .exec((err, result) => {
            if (err || !result) {
              return next(err || new Error(`Not found order type info`));
            }

            const orderTypeId = result.map(orderType => orderType._id.toHexString());
            Rider.count({member: userInfDB._id}, (err, count) => {
              if (count > 0) {
                return next();
              }

              Rider.create({
                member: userInfDB._id,
                serviceRunning: service.order.concat(orderTypeId)
              }, (err, result) => {
                if(err) {
                  return next(err);
                }
                next();
              })
            })
          })
      }

      const makeSingleLogin = (next) => {
          const userId = userInfDB._id.toHexString();
          redisConnection.get(`user:${userId}`, (err, token) => {
              if(err) {
                  return next(err);
              }

              if(token) {
                  redisConnection.del(`user:${token}`, (err, result) => {
                      if(err) {
                          return next(err);
                      }

                      next();
                  });
              } else {
                  next();
              }
          });
      }

      const generateToken = (next) => {
          const userId = userInfDB._id.toHexString();
          const objSign = {
              id: userId,
              accessToken: _.get(req, 'body.access_token', '')
          }

          redisConnection
              .multi()
              .set(`user:${userId}`, memberToken)
              .set(`user:${memberToken}`, JSON.stringify(objSign))
              .exec((err, result) => {
                  if(err) {
                      return next(err);
                  }

                  redisConnection.del(`otp:${phone}`);

                  const data = _.merge({}, userInfDB, {memberToken});
                  _.unset(data, 'facebook.token');
                  _.unset(data, 'password');
                  data.isExpire = (data.expireTime < Date.now());

                  // publish event
                  if(Date.now() - data.createdAt <= 30000) {
                    redisConnectionForPubSub.publish("new_user", data._id.toHexString());
                  }

                  next(null, {
                    code: CONSTANTS.CODE.SUCCESS,
                    member: data
                  });
              });
      }

      async.waterfall([
        checkParams,
        getPhoneFromToken,
        hashPassword,
        updatePassword,
        handleRegisterService,
        makeSingleLogin,
        generateToken
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: message.SYSTEM.ERROR
        });

        res.json(data || err);
      });
    },
    login(req, res) {
      const access_token = _.get(req, 'body.access_token', '');
      let userInfFB;
      let userInfDB;
      let memberToken;
      let messageBonus;

      const checkParams = (next) => {
          if(!_.isString(access_token) || !access_token.length) {
              return next({
                  code: CONSTANTS.CODE.WRONG_PARAMS,
                  message: message.SYSTEM.ERROR,
              });
          }

          next();
      }

      const checkAppId = (next) => {
        const options = {
          method: 'GET',
          uri: `${proxyFacebookServerAddr}/api/v1.0/facebook/get-app-info`,
          qs: {
            access_token
          },
          json: true
        };

        request(options, (err, response, result) => {
          if(err || !response || response.statusCode !== 200 || !result || result.code !== CONSTANTS.CODE.SUCCESS) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: message.SYSTEM.ERROR
            })
          }

          if(result.data.id !== SANSHIP_FB_APPID && result.data.id !== SANSHIP_HEYU_FB_APPID) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: 'Internal Server Error'
            });
          }

          return next(null);
        });
      }

      const getUserInf = (next) => {
        const options = {
          method: 'POST',
          uri: `${proxyFacebookServerAddr}/api/v1.0/facebook/get-profile`,
          body: {
              access_token
          },
          json: true // Automatically stringifies the body to JSON
        };

        request(options, (err, response, result) => {
          if(err || !response || response.statusCode !== 200 || !result) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: message.SYSTEM.ERROR
            })
          }

          if(result.code !== CONSTANTS.CODE.SUCCESS) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: message.SYSTEM.ERROR
            })
          }

          userInfFB = result.userInfo;
          userInfFB.token = access_token;
          next();
        })
      }

      const updateUser = (next) => {
        memberToken = uuid();
        const options = {
          'new': true,
          upsert: true,
          setDefaultsOnInsert: true
        };

        Members
          .findOneAndUpdate({
              'facebook.id': userInfFB.id
          }, {
             'facebook': userInfFB,
             memberToken
          }, options)
          .lean()
          .exec((err, result) => {
              if(err) {
                  return next({
                      code: CONSTANTS.CODE.SYSTEM_ERROR,
                      message: message.SYSTEM.ERROR
                  });
              }

              if(result.blockUtil > Date.now()) {
                ReasonBlock
                  .find({member: result._id})
                  .sort({"createdAt": -1})
                  .limit(1)
                  .lean()
                  .exec((err, resultBlock) => {
                    if(err) {
                      return next({
                          code: CONSTANTS.CODE.SYSTEM_ERROR,
                          message: message.SYSTEM.ERROR
                      });
                    }

                    let messageBlock;
                    if(resultBlock.length === 1 && resultBlock[0].message) {
                      messageBlock = {
                        head: 'Thông báo',
                        body: `Tài khoản của bạn đã bị khoá tới ${moment(result.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')} (${resultBlock[0].message}). Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                      }
                    } else {
                      messageBlock = {
                        head: 'Thông báo',
                        body: `Tài khoản của bạn đã bị khoá tới ${moment(result.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')}. Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                      }
                    }

                    return next({
                      code: CONSTANTS.CODE.FAIL,
                      message: messageBlock
                    })
                  })

                return;
              }

              // if(!result.expireTime) {
              //     const bonusFirstTime = Date.now() + ms(config.bonusFirstTime);
              //     result.expireTime = bonusFirstTime;
              //     messageBonus = message.USER.BONUS_TIME
              //     Members.update({_id: result._id}, {expireTime: bonusFirstTime}).exec((err, result) => {});
              // }

              userInfDB = result;
              next();
          });
      }

      const makeSingleLogin = (next) => {
          const userId = userInfDB._id.toHexString();
          redisConnection.get(`user:${userId}`, (err, token) => {
              if(err) {
                  return next({
                      code: CONSTANTS.CODE.SYSTEM_ERROR,
                      message: message.SYSTEM.ERROR
                  });
              }

              if(token) {
                  redisConnection.del(`user:${token}`, (err, result) => {
                      if(err) {
                          return next({
                              code: CONSTANTS.CODE.SYSTEM_ERROR,
                              message: message.SYSTEM.ERROR
                          });
                      }

                      next();
                  });
              } else {
                  next();
              }
          });
      }

      const generateToken = (next) => {
          const userId = userInfDB._id.toHexString();
          const objSign = {
              id: userId,
              accessToken: _.get(req, 'body.access_token', '')
          }

          redisConnection
              .multi()
              .set(`user:${userId}`, memberToken)
              .set(`user:${memberToken}`, JSON.stringify(objSign))
              .exec((err, result) => {
                  if(err) {
                      return next({
                          code: CONSTANTS.CODE.SYSTEM_ERROR,
                          message: message.SYSTEM.ERROR
                      });
                  }


                  const data = _.merge({}, userInfDB, {memberToken});
                  _.unset(data, 'facebook.token');
                  data.isExpire = (data.expireTime < Date.now());
                  next(null, data);
              });
      }

      async.waterfall([
          checkParams,
          checkAppId,
          getUserInf,
          updateUser,
          makeSingleLogin,
          generateToken
      ], (err, data) => {
          if(err) {
            return res.json(err);
          }

          res.json({
            code: CONSTANTS.CODE.SUCCESS,
            member: data,
            message: messageBonus
          });
      });
    },
    logout(req, res) {
      res.json({
        code: 200
      });

      const userId = req.user.id;
      const phone = req.body.phone;
      const deviceId = req.body.deviceId;

      if (phone && deviceId) {
        redisConnection.setex(`otp:${phone}`, ms('3h')/1000, deviceId);
      }

      VoipToken
        .update({member: userId}, {$unset: {member: ""}})
        .exec();
    },
    login1(req, res) {
      const access_token = _.get(req, 'body.access_token', '');
      const deviceId = _.get(req, 'body.deviceId', '');
        let userInfFB;
        let userInfDB;
        let memberToken;
        let data2Merge;
        let messageBonus;

        const checkParams = (next) => {
            if(!_.isString(access_token) || !access_token.length || !deviceId) {
                return next({
                    code: CONSTANTS.CODE.WRONG_PARAMS,
                    message: message.SYSTEM.ERROR,
                });
            }

            next();
        }

        const checkAppId = (next) => {
          const options = {
            method: 'GET',
            uri: `https://graph.facebook.com/app/?access_token=${access_token}`,
            headers: {
              'Accept': 'application/json'
            },
            json: true
          };

          // rp(options)
            // .then((result) => {
          request(options, (err, response, result) => {
            if(err || !response || response.statusCode !== 200 || !result || result.error) {
              return next({
                code: CONSTANTS.CODE.WRONG_PARAMS,
                message: message.SYSTEM.ERROR
              })
            }

            if(result.id !== SANSHIP_FB_APPID && result.id !== SANSHIP_HEYU_FB_APPID) {
              return next({
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: 'Internal Server Error'
              });
            }

            return next(null);
          });
        }

        const getUserInf = (next) => {
          const options = {
            method: 'POST',
            uri: `${proxyFacebookServerAddr}/api/v1.0/facebook/get-profile`,
            body: {
                access_token
            },
            json: true // Automatically stringifies the body to JSON
          };

          // rp(options)
            // .then((result) => {
          request(options, (err, response, result) => {
            if(err || !response || response.statusCode !== 200 || !result) {
              return next({
                code: CONSTANTS.CODE.WRONG_PARAMS,
                message: message.SYSTEM.ERROR
              })
            }

            if(result.code !== CONSTANTS.CODE.SUCCESS) {
              return next({
                code: CONSTANTS.CODE.WRONG_PARAMS,
                message: message.SYSTEM.ERROR
              })
            }

            userInfFB = result.userInfo;
            userInfFB.token = access_token;
            next();
          })
            // .catch((err) => {
            //   next(err);
            // });
            // const accessToken = _.get(req, 'body.access_token', '');
            // const options = {
            //     accessToken,
            //     version: 'v2.4'
            // }
            // const fields = "id,name,birthday,email,picture{url}";
            //
            // const fb = new FB.Facebook(options);
            // fb.api('/me', {fields}, (res) => {
            //     if(!res || res.error) {
            //         return next({
            //             code: CONSTANTS.CODE.SYSTEM_ERROR,
            //             message: message.SYSTEM.ERROR
            //         });
            //     }
            //
            //     userInfFB = _.defaults({}, res, {
            //         email: '',
            //         birthday: '',
            //         token: accessToken
            //     });
            //     userInfFB.picture = _.get(res, 'picture.data.url', '');
            //
            //     next();
            // });
        }

        const checkRealId = (next) => {
          IdMap
            .findOne({_id: userInfFB.id})
            .lean()
            // .cache(Math.floor(ms('7 days')/1000), `realFbId:${userInfFB.id}`)
            .exec((err, result) => {
              if (err) {
                  return next({
                      code: CONSTANTS.CODE.SYSTEM_ERROR,
                      message: message.SYSTEM.ERROR
                  });
              } else if (result) {
                  const realId = result.realId;
                  userInfFB.realId = realId;
                  // find existed data have this realId and need merge ? (start async)
                  getDataToMerge(realId, (err, data) => {
                    if(err) {
                      return next({
                        code: CONSTANTS.CODE.SYSTEM_ERROR,
                        message: message.SYSTEM.ERROR
                      })
                    }

                    data2Merge = data;
                    next();
                  });
                } else {
                  next();
                }
            })
        }

        const updateUser = (next) => {
            memberToken = uuid();
            const options = {
                'new': true,
                upsert: true,
                setDefaultsOnInsert: true
            };
            let mergeObj ={};
            if (data2Merge) {
              mergeObj={
                $inc: {
                  'likes': data2Merge.likes,
                  'dislikes': data2Merge.dislikes,
                  'shop.totalPost': _.get(data2Merge,'shop.totalPost',0)},
                 $pushAll:{
                    likerList:data2Merge.likerList,
                    dislikerList:data2Merge.dislikerList,
                    'shop.postList': _.get(data2Merge,'shop.postList',[])
                 },
              }
            }
            Members
                .findOneAndUpdate({
                    'facebook.id': userInfFB.id
                }, {
                   'facebook': userInfFB,
                   ...mergeObj,
                   memberToken,
                   deviceId: deviceId
                }, options)
                .lean()
                .exec((err, result) => {
                    if(err) {
                        return next({
                            code: CONSTANTS.CODE.SYSTEM_ERROR,
                            message: message.SYSTEM.ERROR
                        });
                    }

                    if(result.blockUtil > Date.now()) {
                      return next({
                        code: CONSTANTS.CODE.TOKEN_EXPIRE,
                        message: {
                          head: 'Thông báo',
                          body: `Tài khoản của bạn đã bị khoá tới ${moment(result.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')}. Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689`
                        }
                      })
                    }

                    if(!result.expireTime) {
                        // const bonusFirstTime = Date.now() + ms(config.bonusFirstTime);
                        // result.expireTime = bonusFirstTime;
                        messageBonus = message.USER.WELCOME;
                        Members.update({_id: result._id}, {expireTime: 1}).exec((err, result) => {});
                    }

                    userInfDB = result;
                    next();
                });
        }

        const makeSingleLogin = (next) => {
            const userId = userInfDB._id.toHexString();
            redisConnection.get(`user:${userId}`, (err, token) => {
                if(err) {
                    return next({
                        code: CONSTANTS.CODE.SYSTEM_ERROR,
                        message: message.SYSTEM.ERROR
                    });
                }

                if(token) {
                    redisConnection.del(`user:${token}`, (err, result) => {
                        if(err) {
                            return next({
                                code: CONSTANTS.CODE.SYSTEM_ERROR,
                                message: message.SYSTEM.ERROR
                            });
                        }

                        next();
                    });
                } else {
                    next();
                }
            });
        }

        const generateToken = (next) => {
            const userId = userInfDB._id.toHexString();
            const objSign = {
                id: userId,
                accessToken: _.get(req, 'body.access_token', '')
            }

            redisConnection
                .multi()
                .set(`user:${userId}`, memberToken)
                .set(`user:${memberToken}`, JSON.stringify(objSign))
                .exec((err, result) => {
                    if(err) {
                        return next({
                            code: CONSTANTS.CODE.SYSTEM_ERROR,
                            message: message.SYSTEM.ERROR
                        });
                    }


                    const data = _.merge({}, userInfDB, {memberToken});
                    _.unset(data, '_id');
                    _.unset(data, 'facebook.token');
                    data.isExpire = (data.expireTime < Date.now());
                    next(null, data);
                });
        }

        async.waterfall([
            checkParams,
            checkAppId,
            getUserInf,
            checkRealId,
            updateUser,
            makeSingleLogin,
            generateToken
        ], (err, data) => {
            if(err) {
                return res.json(err);
            }
            res.json({
                code: CONSTANTS.CODE.SUCCESS,
                member: data,
                message: messageBonus
            });
        });
    },

    getUserId(req, res) {

      const memberToken = _.get(req,'body.memberToken','')

      const checkParams = (next) => {
        if(!memberToken) {
          return next({
            code: CONSTANTS.CODE.TOKEN_EXPIRE,
            message: message.USER.TOKEN_EXPIRE
          });
        }

        next();
      }

      const getTokenUser = (next) => {
        redisConnection.get(`user:${memberToken}`, (err, result) => {

          if(!result) {
            return next()
          }

          try {
            const objSign = JSON.parse(result);
            if(!_.has(objSign, 'id') || !_.has(objSign, 'accessToken')) {
              return next({
                code: CONSTANTS.CODE.TOKEN_EXPIRE,
                message: message.USER.TOKEN_EXPIRE
              });
            }

            next({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                userId: objSign.id
              }
            });
          } catch(e) {
            return next({
              code: CONSTANTS.CODE.TOKEN_EXPIRE,
              message: message.USER.TOKEN_EXPIRE
            });
          }
        });
      }

      const getTokenTickbox = (next) => {
        redisConnection.get(`tickbox:${memberToken}`, (err, result) => {

          if(!result) {
            return next()
          }

          try {
            const objSign = JSON.parse(result);
            if(!_.has(objSign, 'id') || !_.has(objSign, 'accessToken')) {
              return next({
                code: CONSTANTS.CODE.TOKEN_EXPIRE,
                message: message.USER.TOKEN_EXPIRE
              });
            }

            next({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                userId: objSign.id
              }
            });
          } catch(e) {
            return next({
              code: CONSTANTS.CODE.TOKEN_EXPIRE,
              message: message.USER.TOKEN_EXPIRE
            });
          }
        });
      }

      const getTokenDriver = (next) => {
        redisConnection.get(`driver:${memberToken}`, (err, result) => {

          if(!result) {
            return next()
          }

          try {
            const objSign = JSON.parse(result);
            if(!_.has(objSign, 'id') || !_.has(objSign, 'accessToken')) {
              return next({
                code: CONSTANTS.CODE.TOKEN_EXPIRE,
                message: message.USER.TOKEN_EXPIRE
              });
            }

            next({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                userId: objSign.id
              }
            });
          } catch(e) {
            return next({
              code: CONSTANTS.CODE.TOKEN_EXPIRE,
              message: message.USER.TOKEN_EXPIRE
            });
          }
        });
      }

      const getTokenbookWeb = (next) => {
        redisConnection.get(`bookWeb:${memberToken}`, (err, result) => {

          if(!result) {
            return next()
          }

          try {
            const objSign = JSON.parse(result);
            if(!_.has(objSign, 'id') || !_.has(objSign, 'accessToken')) {
              return next({
                code: CONSTANTS.CODE.TOKEN_EXPIRE,
                message: message.USER.TOKEN_EXPIRE
              });
            }

            next({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                userId: objSign.id
              }
            });
          } catch(e) {
            return next({
              code: CONSTANTS.CODE.TOKEN_EXPIRE,
              message: message.USER.TOKEN_EXPIRE
            });
          }
        });
      }

      const getTokenTickboxWeb = (next) => {
        redisConnection.get(`tickboxWeb:${memberToken}`, (err, result) => {

          if(!result) {
            return next()
          }

          try {
            const objSign = JSON.parse(result);
            if(!_.has(objSign, 'id') || !_.has(objSign, 'accessToken')) {
              return next({
                code: CONSTANTS.CODE.TOKEN_EXPIRE,
                message: message.USER.TOKEN_EXPIRE
              });
            }

            next({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                userId: objSign.id
              }
            });
          } catch(e) {
            return next({
              code: CONSTANTS.CODE.TOKEN_EXPIRE,
              message: message.USER.TOKEN_EXPIRE
            });
          }
        });
      }

      const getTokenHeyCareWeb = (next) => {
        redisConnection.get(`heyCareWeb:${memberToken}`, (err, result) => {

          if (!result) {
            return next()
          }

          try {
            const objSign = JSON.parse(result);
            if (!_.has(objSign, 'id') || !_.has(objSign, 'accessToken')) {
              return next({
                code: CONSTANTS.CODE.TOKEN_EXPIRE,
                message: message.USER.TOKEN_EXPIRE
              });
            }

            next({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                userId: objSign.id
              }
            });
          } catch (e) {
            return next({
              code: CONSTANTS.CODE.TOKEN_EXPIRE,
              message: message.USER.TOKEN_EXPIRE
            });
          }
        });
      }

      const getTokenHeywow = (next) => {
        redisConnection.get(`heywow:${memberToken}`, (err, result) => {

          if(!result) {
            return next()
          }

          try {
            const objSign = JSON.parse(result);
            if(!_.has(objSign, 'id') || !_.has(objSign, 'accessToken')) {
              return next({
                code: CONSTANTS.CODE.TOKEN_EXPIRE,
                message: message.USER.TOKEN_EXPIRE
              });
            }

            next({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                userId: objSign.id
              }
            });
          } catch(e) {
            return next({
              code: CONSTANTS.CODE.TOKEN_EXPIRE,
              message: message.USER.TOKEN_EXPIRE
            });
          }
        });
      }

      const getTokenHeyCareStaff = (next) => {
        redisConnection.get(`staff:${memberToken}`, (err, result) => {

          if (!result) {
            return next();
          }

          try {
            const objSign = JSON.parse(result);
            if (!_.has(objSign, 'id') || !_.has(objSign, 'accessToken')) {
              return next({
                code: CONSTANTS.CODE.TOKEN_EXPIRE,
                message: message.USER.TOKEN_EXPIRE
              });
            }

            next({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                userId: objSign.id
              }
            });
          } catch (e) {
            return next({
              code: CONSTANTS.CODE.TOKEN_EXPIRE,
              message: message.USER.TOKEN_EXPIRE
            });
          }
        });
      }

      const getTokenHeymove = (next) => {
        redisConnection.get(`heymove:${memberToken}`, (err, result) => {

          if(!result) {
            return next()
          }

          try {
            const objSign = JSON.parse(result);
            if(!_.has(objSign, 'id') || !_.has(objSign, 'accessToken')) {
              return next({
                code: CONSTANTS.CODE.TOKEN_EXPIRE,
                message: message.USER.TOKEN_EXPIRE
              });
            }

            next({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                userId: objSign.id
              }
            });
          } catch(e) {
            return next({
              code: CONSTANTS.CODE.TOKEN_EXPIRE,
              message: message.USER.TOKEN_EXPIRE
            });
          }
        });
      }

      const getTokenHeymoveDriver = (next) => {
        redisConnection.get(`heymovedriver:${memberToken}`, (err, result) => {

          if(!result) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }

          try {
            const objSign = JSON.parse(result);
            if(!_.has(objSign, 'id') || !_.has(objSign, 'accessToken')) {
              return next({
                code: CONSTANTS.CODE.TOKEN_EXPIRE,
                message: message.USER.TOKEN_EXPIRE
              });
            }

            next({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                userId: objSign.id
              }
            });
          } catch(e) {
            return next({
              code: CONSTANTS.CODE.TOKEN_EXPIRE,
              message: message.USER.TOKEN_EXPIRE
            });
          }
        });
      }

      async.waterfall([
          checkParams,
          getTokenUser,
          getTokenTickbox,
          getTokenDriver,
          getTokenbookWeb,
          getTokenTickboxWeb,
          getTokenHeyCareWeb,
          getTokenHeywow,
          getTokenHeyCareStaff,
          getTokenHeymove,
          getTokenHeymoveDriver
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: message.SYSTEM.ERROR
        });

        res.json(data || err);
      });
    },
    checkExistsByPhone(req, res) {
      const phone = _.get(req, 'body.phone', '');
      if(!phone) {
        return res.json({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: message.SYSTEM.ERROR
        })
      }

      Members
        .count({phone})
        .lean()
        .exec((err, count) => {
          if(err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: message.SYSTEM.ERROR
            })
          }

          res.json({
            code: CONSTANTS.CODE.SUCCESS,
            count: count,
            message: count > 1 ? {
              'head': 'Thông báo',
              'body': 'Số điện thoại này được đăng ký cho nhiều hơn 1 tài khoản vui lòng liên hệ 1900.633.689 để được hỗ trợ. Xin cảm ơn.'
            } : undefined
          })
        });
    },
    getUsersByPhone(req, res) {

      let phone = _.get(req, 'body.phone', '');
      let userId = _.get(req, 'user.id', '');
      const regionName = _.get(req, 'body.regionName', '')
      let resultMembers;

      const checkParams = (next) => {
        if(!phone) {
          return next({
            code: CONSTANTS.CODE.SUCCESS,
            data: []
          })
        }
        phone = phone.replace(/\D+/g, '');
        next();
      }

      const getUser = (next) => {
        Members
          .find({phone}, "facebook.name facebook.picture")
          .lean()
          .exec((err, results) => {
            if(err) {
              return res.json({
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: message.SYSTEM.ERROR
              })
            }
            resultMembers = results
            next();
          });
      }

      const getConfig = (next) => {
        Config.get(CONSTANTS.CONFIG_TYPE.LIST_ADMIN_VIDEO, regionName, (err, data) => {
          if(err) {
            return next(err);
          }

          if(!data) {
            return next({
              code: CONSTANTS.CODE.FAIL
            })
          }
          let canCallVideo = false
          if(data && data.config && data.config.members && data.config.members.includes(userId)) {
            canCallVideo = true
          }
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: resultMembers,
            canCallVideo
          })
        })
      }

      async.waterfall([
          checkParams,
          getUser,
          getConfig
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: message.SYSTEM.ERROR
        });

        res.json(data || err);
      });

    },
    getById(req, res) {
      const userId = _.get(req, 'body.id', '');
      if(!userId) {
        return res.json({
          code: 300
        })
      }

      Members
        .findById(userId, "facebook.name facebook.picture type")
        .lean()
        .exec((err, result) => {
          if(err) {
            return res.json({
              code: 500
            })
          }

          return res.json({
            code: 200,
            data: result
          })
        })
    },

    get(req, res) {
      Members
        .findById(req.user.id, "-shop.postList -dislikedList -likedList -facebook.token -password -location -likes -dislikes -os_version -memberToken -address -birthday -createdAt -presenterCode -updatedAt -v")
        .lean()
        .exec((err, result) => {
          if(_.isError(err)) {
            logger.logError([err], req.originalUrl, req.body);
          }
            if (err || !result) {
                return res.json({
                  code: CONSTANTS.CODE.SYSTEM_ERROR,
                  message: message.SYSTEM.ERROR
                })
            }

            if(result.blockUtil > Date.now()) {
              ReasonBlock
                .find({member: req.user.id})
                .sort({"createdAt": -1})
                .limit(1)
                .lean()
                .exec((err, resultBlock) => {
                  if(err) {
                    return res.json({
                        code: CONSTANTS.CODE.SYSTEM_ERROR,
                        message: message.SYSTEM.ERROR
                    });
                  }

                  let messageBlock;
                  if(resultBlock.length === 1 && resultBlock[0].message) {
                    messageBlock = {
                      head: 'Thông báo',
                      body: `Tài khoản của bạn đã bị khoá tới ${moment(result.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')} (${resultBlock[0].message}). Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                    }
                  } else {
                    messageBlock = {
                      head: 'Thông báo',
                      body: `Tài khoản của bạn đã bị khoá tới ${moment(result.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')}. Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                    }
                  }

                  return res.json({
                    code: CONSTANTS.CODE.TOKEN_EXPIRE,
                    message: messageBlock
                  })
                })

              return;
            }

            if(result.blockOrderUtil > Date.now()) {
              ReasonBlock
                .find({member: req.user.id})
                .sort({"createdAt": -1})
                .limit(1)
                .lean()
                .exec((err, resultBlock) => {
                  if(err) {
                    return res.json({
                        code: CONSTANTS.CODE.SYSTEM_ERROR,
                        message: message.SYSTEM.ERROR
                    });
                  }

                  let messageBlock;
                  if(resultBlock.length === 1 && resultBlock[0].message) {
                    messageBlock = {
                      head: 'Thông báo',
                      body: `Tài khoản của bạn đã bị tạm ngưng nhận đơn hệ thống tới ${moment(result.blockOrderUtil).format('HH:mm:ss ngày DD/MM/YYYY')} (${resultBlock[0].message}). Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                    }
                  } else {
                    messageBlock = {
                      head: 'Thông báo',
                      body: `Tài khoản của bạn đã bị tạm ngưng nhận đơn hệ thống tới ${moment(result.blockOrderUtil).format('HH:mm:ss ngày DD/MM/YYYY')}. Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                    }
                  }

                  result.isExpire = (result.expireTime < Date.now());
                  result.memberToken = req.user.memberToken;

                  res.json({
                    code: CONSTANTS.CODE.SUCCESS,
                    member: result,
                    message: messageBlock
                  });
                })
            } else {
            result.isExpire = (result.expireTime < Date.now());
            result.memberToken = req.user.memberToken;

            res.json({
              code: CONSTANTS.CODE.SUCCESS,
              member: result
            });
            }
          });
    },

    get1(req, res) {
      let memberInf={}
      let userInfFB ={}
      let data2Merge;
      let messageBonus;

      const getUserInf=(next)=>{
        Members
            .findById(req.user.id)
            .lean()
            .exec((err, result) => {
                if(err) {
                    return next(err)
                }

                if(result.blockUtil > Date.now()) {
                  return next({
                    code: CONSTANTS.CODE.TOKEN_EXPIRE,
                    message: {
                      head: 'Thông báo',
                      body: `Tài khoản của bạn đã bị khoá tới ${moment(result.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')}. Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689`
                    }
                  })
                }

                if(!result.expireTime) {
                  const bonusFirstTime = Date.now() + ms(config.bonusFirstTime);
                  result.expireTime = bonusFirstTime;
                  messageBonus = message.USER.BONUS_TIME;
                  // Members.update({_id: result._id}, {expireTime: bonusFirstTime}).exec((err, result) => {});
                }

                memberInf = result;
                userInfFB = result.facebook;
                return next()
            });
      }

      const checkRealId = (next) => {
        if (_.get(memberInf, 'facebook.realId', '')) {
          return next();
        }
        IdMap
          .findOne({_id: userInfFB.id})
          .lean()
          // .cache(Math.floor(ms('7 days')/1000), `realFbId:${userInfFB.id}`)
          .exec((err, result) => {
            if (err) {
                return next(err);
            } else if (result) {
              const realId = result.realId;
              userInfFB.realId = realId;
              // have realId in map start merge data
              getDataToMerge(realId, (err, data) => {
                if(err) {
                  return next(err);
                }

                data2Merge = data;
                next();
              });
            } else {
              next();
            }
          })
      }

      const updateUser = (next) => {
        const options = {
            'new': true,
            upsert: true,
            setDefaultsOnInsert: true
        };
        let mergeObj ={}
        if (data2Merge) {
          mergeObj={
            $inc: {
              'likes': data2Merge.likes,
              'dislikes': data2Merge.dislikes,
              'shop.totalPost': _.get(data2Merge,'shop.totalPost',0)
             },
             $pushAll:{
                 likerList:data2Merge.likerList,
                 dislikerList:data2Merge.dislikerList,
                 'shop.postList':_.get(data2Merge,'shop.postList',[])
             },
          }
        }

        Members
          .findOneAndUpdate({
              '_id': memberInf._id
          }, {
             'facebook': userInfFB,
             ...mergeObj,
             expireTime: memberInf.expireTime
          }, options)
          .lean()
          .exec((err, result) => {
              if(err) {
                  return next(err);
              }

              const data = result;
              _.unset(data, '_id');
              _.unset(data, 'facebook.token');
              data.isExpire = (data.expireTime < Date.now());

              next(null, {
                code: CONSTANTS.CODE.SUCCESS,
                member: data,
                message: messageBonus
              });
          });
      }

      async.waterfall([
          getUserInf,
          checkRealId,
          updateUser
      ], (err, data) => {
        err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: message.SYSTEM.ERROR
        });

        res.json(data || err);
      });
    },


    updatePhone(req, res) {
      const phone = _.get(req, 'body.phone', '');
      const userId = req.user.id;
      const token = _.get(req, 'body.token', '');

      const checkParams = (next) => {
        if(!phone) {
          return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: message.SYSTEM.ERROR
          });
        }

        next();
      }

      const checkUserAgent = (next) => {
        if(req.headers['user-agent'] === 'okhttp/3.4.1' || (req.headers['user-agent'].indexOf('sanship') !== -1)) {
          return next();
        }

        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: message.SYSTEM.ERROR
        });
      }

      const getPhoneFromToken = (next) => {
        const options = {
          method: 'POST',
          uri: `${proxyFacebookServerAddr}/api/v1.0/facebook/get-phone-account-kit`,
          body: {
              token
          },
          json: true // Automatically stringifies the body to JSON
        };

        request(options, (err, response, result) => {
          console.log('getPhoneFromToken', err, response, result);

          if(err || !response || response.statusCode !== 200 || !result || result.code !== 200) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: message.SYSTEM.ERROR
            })
          }

          if(result.data.application.id !== SANSHIP_FB_APPID && result.data.application.id !== SANSHIP_HEYU_FB_APPID) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: 'Internal Server Error'
            });
          }

          const phoneFromToken = `0${_.get(result, 'data.phone.national_number', '')}`

          if(phoneFromToken !== phone) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: 'Internal Server Error'
            });
          }

          next();
        })
      }

      const checkCondition = (next) => {
        Members
          .count({phone})
          .exec((err, numberApp) => {
            if(err) {
              return next(err);
            }

            if(numberApp >= config.maximumPhone) {
              return next({
                code: CONSTANTS.CODE.FAIL,
                message: message.USER.MAXIMUM_PHONE
              })
            }

            next();
          });
      }

      const updateInf = (next) => {
        Members
          .update({_id: userId}, {phone, status: 1})
          .exec((err, result) => {
              if(err) {
                  return next({
                      code: CONSTANTS.CODE.SYSTEM_ERROR,
                      message: message.SYSTEM.ERROR
                  });
              }

              next(null, {
                  code: CONSTANTS.CODE.SUCCESS,
                  phone,
                  message: message.USER.UPDATE_PHONE
              })
          });
      }

      async.waterfall([
        checkParams,
        getPhoneFromToken,
        checkCondition,
        updateInf
      ], (err, data) => {
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: message.SYSTEM.ERROR
        });

        res.json(data || err);
      })
    },
    changePhoneForUser(req, res) {
      const userId = req.user.id;
      const token = req.body.token || '';
      let newPhone = _.get(req, 'body.newPhone', '');

      let userInf;

      const checkParams = (next) => {
        newPhone = newPhone.replace(/\s/g, '');

        if(!newPhone) {
          return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: message.SYSTEM.ERROR
          });
        }

        next();
      }

      const getPhoneFromToken = (next) => {
        const options = {
          method: 'POST',
          uri: `${proxyFacebookServerAddr}/api/v1.0/facebook/get-phone-account-kit`,
          body: {
              token
          },
          json: true // Automatically stringifies the body to JSON
        };

        request(options, (err, response, result) => {
          if(err || !response || response.statusCode !== 200 || !result || result.code !== 200) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: message.SYSTEM.ERROR
            })
          }

          if(result.data.application.id !== SANSHIP_FB_APPID && result.data.application.id !== SANSHIP_HEYU_FB_APPID) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: 'Internal Server Error'
            });
          }

          const phoneFromToken = `0${_.get(result, 'data.phone.national_number', '')}`

          if(phoneFromToken !== newPhone) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: 'Internal Server Error'
            });
          }

          next();
        })
      }

      const checkExistPhone = (next) => {
        if(helpers.convertToNewPhone(newPhone)) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Cập nhật đầu số',
              body: `Rất xin lỗi bạn số điện thoại bạn cần đổi hiện tại đã được chuyển thành số mới: ${helpers.convertToNewPhone(newPhone)}. Vui lòng thử thay đổi lại với số mới. Xin cảm ơn.`
            }
          })
        }

        Members
          .count({
            phone: newPhone
          })
          .exec((err, count) => {
            if(err) {
              return next(err);
            }

            if(count > 0) {
              return next({
                code: CONSTANTS.CODE.FAIL,
                message: message.USER.EXIST_PHONE
              })
            }

            next();
          });
      }

      const getCurrentPhone = (next) => {
        Members
          .findById(userId, "phone")
          .lean()
          .exec((err, result) => {
            if(err || !result) {
              return next(err || new Error(`Not found user info`));
            }

            userInf = result;

            next();
          })
      }

      const writeLog = (next) => {
        ChangePhoneLog
          .create({
            member: userId,
            oldPhone: userInf.phone,
            newPhone: newPhone
          }, (err, result) => {
            if(err) {
              return next(err);
            }

            next();
          })
      }

      const updateUserInf = (next) => {
        Members
          .update({
            _id: userId
          }, {
            phone: newPhone
          })
          .exec((err, result) => {
            if(err) {
              return next(err);
            }

            next(null , {
              code: CONSTANTS.CODE.SUCCESS,
              phone: newPhone,
              message: message.USER.UPDATE_PHONE
            });
          })
      }

      async.waterfall([
        checkParams,
        getPhoneFromToken,
        checkExistPhone,
        getCurrentPhone,
        writeLog,
        updateUserInf
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: message.SYSTEM.ERROR
        });

        res.json(data || err);
      })
    },
    changePhone(req, res) {
      let oldPhone = _.get(req, 'body.oldPhone', '');
      let newPhone = _.get(req, 'body.newPhone', '');
      const userId = _.get(req, 'body.id', '');

      let accounts = [];
      let oldTokens = [];

      const checkParams = (next) => {
        if(!oldPhone || !newPhone || !userId) {
          return next({
              code: CONSTANTS.CODE.WRONG_PARAMS
          });
        }

        oldPhone = oldPhone.replace(/\s/g, '');
        newPhone = newPhone.replace(/\s/g, '');

        next();
      }

      const getAccountHaveNewPhone = (next) => {
        Members
          .find({phone: newPhone}, "_id")
          .lean()
          .exec((err, results) => {
            if(err) {
              return next(err);
            }

            results.forEach((result) => {
              accounts.push(result._id.toHexString());
            })

            accounts.push(userId);
            next(null);
          })
      }


      const getTokenAccountsHaveNewPhone = (next) => {
        async.map(accounts, (account, done) => {
          redisConnection.get(`user:${account}`, (err, result) => {
            if(err) {
                return done(err);
            }

            done(null, result);
          });
        }, (err, results) => {
          if(err) {
            return next(err);
          }

          oldTokens = results;
          next(null);
        })
      }

      const removeToken = (next) => {
        if(oldTokens.length === 0) {
          return next(null);
        }

        async.map(oldTokens, (token, done) => {
          redisConnection
            .del(`user:${token}`, (err, result) => {
              if(err) {
                  return next(err);
              }

              done(null, result);
            });
        }, (err, results) => {
          if(err) {
            return next(err);
          }

          next(null);
        })
      }

      const removeAccountNewPhone = (next) => {
        Members
          .update({phone: newPhone}, {status: 0, phone: ""}, {multi: true})
          .exec((err, result) => {
            if(err) {
                return next(err);
            }

            next(null);
          })
      }

      const updateNewPhone = (next) => {
        Members
          .update({_id: userId}, {phone: newPhone})
          .exec((err, result) => {
            if(err) {
              return next(err)
            }

            next({
              code: CONSTANTS.CODE.SUCCESS
            });
          });
      }

      async.waterfall([
        checkParams,
        getAccountHaveNewPhone,
        getTokenAccountsHaveNewPhone,
        removeToken,
        removeAccountNewPhone,
        updateNewPhone
      ], (err, data) => {
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: message.SYSTEM.ERROR
        });

        res.json(data || err);
      })
    },

    saveFeed(req, res) {
        const feed_id = _.get(req, 'body.feed_id', '');
        const userId = req.user.id;

        const checkParams = (next) => {
            if(!feed_id) {
                return next({
                    code: CONSTANTS.CODE.SYSTEM_ERROR,
                    message: message.SYSTEM.ERROR
                });
            }

            next();
        }

        const checkCondition = (next) => {
          Members
            .findById(req.user.id, "expireTime")
            .lean()
            .exec((err, result) => {
              if(err) {
                return next(err)
              }

              if(!result) {
                return next({
                  code: CONSTANTS.CODE.TOKEN_EXPIRE,
                  message: message.SYSTEM.ERROR
                })
              }

              if(result.expireTime < Date.now()) {
                return next({
                  code: CONSTANTS.CODE.FAIL,
                  message: message.FEEDS.NOT_ENOUGH_TO_SAVE
                })
              }

              return next();
            })
        }

        const saveToMongo = (next) => {
            const objFeed = {
                feed_id,
                member: userId,
                createdAt: new Date()
            }

            Feeds
                .create(objFeed, (err, result) => {
                    if(err && err.code !== 11000) {
                        return next({
                            code: CONSTANTS.CODE.SYSTEM_ERROR,
                            message: message.SYSTEM.ERROR
                        });
                    }

                    next(null, {
                        code: CONSTANTS.CODE.SUCCESS
                    });
                });
        }

        async.waterfall([
            checkParams,
            // checkCondition,
            saveToMongo
        ], (err, data) => {
            err && _.isError(err) && (data = {
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: message.SYSTEM.ERROR
            });

            res.json(data || err);
        });
    },

    getFeeds(req, res) {
        const userId = req.user.id;
        const from = _.toSafeInteger(req.body.from) || Date.now();
        const limit = _.clamp(_.toSafeInteger(req.body.limit), 5, 100);

        Feeds
            .find({member: userId, createdAt: {$lt: from}}, null, {limit})
            .sort("-createdAt")
            .populate('feedInf', '', NewFeeds)
            .lean()
            .exec((err, feeds) => {
                if(err) {
                    return res.json({
                        code: CONSTANTS.CODE.SYSTEM_ERROR,
                        message: message.SYSTEM.ERROR
                    });
                }

                const feedsStillExist = [];
                feeds.forEach((feed) => {
                  if(feed.feedInf.length !== 0) {
                    const feedInf = feed.feedInf[0];
                    feedInf.feed_id = feedInf.id;
                    feedInf.createdAt = (new Date(feed.createdAt)).getTime();
                    if(req.body.newTemplate) {
                      if(!feedInf.from.totalPost) {
                        // feedInf.from.totalPost = _.get(feedInf, 'from.memberInfo.shop.totalPost', 1);
                        if(!feedInf.location && feedInf.origin_place) {
                          feedInf.location = {
                            "lat": feedInf.origin_place.geometry[0].geometry[1],
                            "lng": feedInf.origin_place.geometry[0].geometry[0]
                          }
                        }
                      }
                    } else {
                      if(feedInf.from.totalPost) {
                        feedInf.from.memberInfo = {
                          shop: {
                            totalPost: feedInf.from.totalPost
                          },
                          facebook: {
                            id: feedInf.from.realId || feedInf.from.id
                          }
                        }

                        if(feedInf.location) {
                          feedInf.origin_place = {
                            address: 'temp',
                            geometry: [
                              {
                                name: 'temp',
                                geometry: [feedInf.location.lng, feedInf.location.lat]
                              }
                            ]
                          }
                        }
                      }
                    }
                    feedsStillExist.push(feedInf);
                  }
                });

                let phones = [];
                let ids = [];

                feedsStillExist.forEach((feed) => {
                  ids.push(feed.from.realId || feed.from.id);
                  if(feed.phone) {
                    phones.push(feed.phone);
                  }
                });

                let step = 0;
                StatisticFeedFacebook
                  .find({
                    $or: [
                      {
                        id: {
                          $in: ids
                        }
                      },
                      {
                        phone: {
                          $in: phones
                        }
                      }
                    ]
                  })
                  .lean()
                  .exec((err, results) => {
                    if(err) {
                      return res.json({
                        code: CONSTANTS.CODE.SYSTEM_ERROR,
                        message: message.SYSTEM.ERROR
                      });
                    }

                    feedsStillExist.forEach((feed) => {
                      const idCalculate = feed.from.realId || feed.from.id;
                      let totalPost = 0;
                      results.forEach((result) => {
                        if (result.id === idCalculate) {
                          totalPost += result.count;
                        } else if (feed.phone && (result.phone === feed.phone)) {
                          totalPost += result.count;
                        }
                      })

                      feed.from.totalPost = totalPost;
                    });

                    step++;

                    if(step === 2) {
                      return res.json({
                          code: CONSTANTS.CODE.SUCCESS,
                          feeds: feedsStillExist
                      });
                    }
                  })

                // Check phot
                BlackPhone
                  .find({
                    phone: {
                      $in: phones
                    }
                  })
                  .lean()
                  .exec((err, results) => {
                    if(err) {
                      return res.json({
                        code: CONSTANTS.CODE.SYSTEM_ERROR,
                        message: message.SYSTEM.ERROR
                      });
                    }

                    if(results.length) {
                      results.forEach((phoneBlackInf) => {
                        feedsStillExist.forEach((feed) => {
                          if(feed.phone === phoneBlackInf.phone) {
                            feed.isPhot = 1;
                          }
                        })
                      });
                    }

                    step++;

                    if(step === 2) {
                      return res.json({
                          code: CONSTANTS.CODE.SUCCESS,
                          feeds: feedsStillExist
                      });
                    }
                  })
                // async.map(feedsStillExist, (feed, done) => {
                //   let totalPost = 0;
                //   let i = 0;
                //
                //   StatisticFeedFacebook
                //     .find({id: feed.from.realId || feed.from.id})
                //     .lean()
                //     .exec((err, results) => {
                //       if(err) {
                //         return done(err)
                //       }
                //
                //       i++;
                //       results.forEach((result) => {
                //         totalPost += result.count;
                //       });
                //       if(i===3) {
                //         feed.from.totalPost = totalPost;
                //         done()
                //       }
                //     });
                //
                //   if(feed.phone) {
                //     StatisticFeedFacebook
                //       .find({phone: feed.phone})
                //       .lean()
                //       .exec((err, results) => {
                //         if(err) {
                //           return done(err)
                //         }
                //
                //         i++;
                //         results.forEach((result) => {
                //           totalPost += result.count;
                //         });
                //         if(i===3) {
                //           feed.from.totalPost = totalPost;
                //           done()
                //         }
                //       });
                //
                //       StatisticFeedFacebook
                //         .find({phone: feed.phone, id: feed.from.realId || feed.from.id})
                //         .lean()
                //         .exec((err, results) => {
                //           if(err) {
                //             return done(err)
                //           }
                //
                //           i++;
                //           results.forEach((result) => {
                //             totalPost -= result.count;
                //           });
                //           if(i===3) {
                //             feed.from.totalPost = totalPost;
                //             done()
                //           }
                //         });
                //   } else {
                //     i = 2;
                //   }
                // }, (err) => {
                //   if(err) {
                //     return res.json({
                //       code: CONSTANTS.CODE.SYSTEM_ERROR,
                //       message: message.SYSTEM.ERROR
                //     });
                //   }
                //
                //   res.json({
                //       code: CONSTANTS.CODE.SUCCESS,
                //       feeds: feedsStillExist
                //   });
                // });
            })
    },

    deleteAFeed(req, res) {
        const userId = req.user.id;
        const feed_id = _.get(req, 'body.feed_id', '');
        if(!feed_id) {
            return res.json({
                code: CONSTANTS.CODE.SUCCESS
            });
        }

        Feeds
            .remove({member: userId, feed_id})
            .exec((err, result) => {
                if(err) {
                    return res.json({
                        code: CONSTANTS.CODE.SYSTEM_ERROR,
                        message: message.SYSTEM.ERROR
                    });
                }

                res.json({
                    code: CONSTANTS.CODE.SUCCESS
                });
            });
    },

    deleteAllFeeds(req, res) {
        const userId = req.user.id;
        Feeds
            .remove({ member: userId })
            .exec((err, result) => {
                if(err) {
                    return res.json({
                        code: CONSTANTS.CODE.SYSTEM_ERROR,
                        message: message.SYSTEM.ERROR
                    });
                }

                res.json({
                    code: CONSTANTS.CODE.SUCCESS
                });
            });
    },

    updateLocation(req, res) {
        res.json({code: CONSTANTS.CODE.SUCCESS})
        const userId = req.user.id;
        const location = req.body.location;
        LocationManager.add({
          userId,
          location
        })
    },

    chooseMode(req, res) {
      res.json({code: CONSTANTS.CODE.SUCCESS});

      const type = _.get(req, 'body.type', '');      // 0 means shipper, 2 means shop, 1 means admin
      const region = _.get(req, 'body.region', '');

      const userId = req.user.id;
      Members.update({_id: userId}, {type, region}).exec();
    },

    like(req,res){
      return like_dislike(req,res,true)
    },

    dislike(req,res){
      return like_dislike(req,res,false)
    },

    doneTraining(req, res) {
      const userId = req.user.id;
      const authenOnline = req.body.authenOnline;
      const region = req.body.regionName;
      const newVersion = req.body.newVersion;
      let changeMoney = region === 'vietnam:hatinh' ? 50000 : 100000;
      let money = region === 'vietnam:hatinh' ? 50000 : 450000;
      let authenInf;
      let servicesRegisted;
      const openAccount = req.body.openAccount;
      const idBlock = req.body.idBlock;
      let blockLog;

      const getConfigAuthenOnline = (next) => {
        Config
          .get(CONSTANTS.CONFIG_TYPE.SHIPPER_AUTHEN_ONLINE, region, (err, data) => {
            if (err || !data || !data.config || !data.config.moneyCoints) {
              return next();
            }

            changeMoney = data.config.moneyCoints;
            money = data.config.money;

            next();
          })
      }

      const getBlockLog = (next) => {
        if(!openAccount || !idBlock) {
          return next();
        }

        BlockLog
          .findOne({
            _id: idBlock
          })
          .lean()
          .exec((err, result) => {
            if(err) {
              return next(err)
            }
            if(!result) {
              return next({
                code: CONSTANTS.CODE.FAIL
              })
            }
            if(result.amount && !result.statusPayment) {
              return next({
                code: CONSTANTS.CODE.FAIL,
                message: {
                  head:'Thông báo',
                  body: 'Bạn chưa nộp phí đào tạo để được được mở khóa tài khoản.'
                }
              })
            }
            blockLog = result
            next();
          })

      }

      const getAuthenInf = (next) => {
        if (!authenOnline) {
          return next();
        }

        AuthenShipInf
          .findOneAndUpdate({
            member: userId,
            currentStep: CONSTANTS.STEP_AUTHEN.TRAINING
          }, {
            statusTraining: CONSTANTS.STATUS_APPROVE_AUTHEN.APPROVED,
            currentStep: CONSTANTS.STEP_AUTHEN.FINISH
          }, {new: true})
          .lean()
          .exec((err, result) => {
            if (err) {
              return next(err);
            }

            authenInf = result;

            next();
          });
      }

      const transferData = (next) => {
        if (!authenInf || !authenOnline) {
          return next();
        }

        const keysText = ['name', 'address', 'email', 'facebookUrl', 'identityNum', 'identityDate', 'licensePlate', 'drivingLicenseNum', 'note', 'message', 'accommodation', 'phoneRelative1', 'phoneRelative2', 'receiveUniformAddress', 'brand', 'color', 'identityPlace', 'dob'];
        const keysImage = ['avatar', 'facebookImg', 'identityFacadeImg', 'identityBacksideImg', 'drivingLicenseFacadeImg', 'drivingLicenseBacksideImg', 'motobikeFacadeImg', 'motobikeBacksideImg', 'registrationLicenseFacadeImg', 'registrationLicenseBacksideImg', 'avatarWithIdentity', 'bill', 'contract', 'uniform'];

        keysText.forEach((key) => {
          if (authenInf && authenInf[key] && authenInf[key].length) {
            const value = authenInf[key];

            if (key === 'message') {
              authenInf[key] = `${value[value.length - 1]}`;
            } else {
              authenInf[key] = `${value[value.length - 1].value}`;
            }
          } else {
            authenInf[key] = "";
          }
        });

        keysImage.forEach((key) => {
          if (authenInf && authenInf[key] && authenInf[key].length) {
            const value = authenInf[key];
            if (value[value.length - 1].value.startsWith("http") || value[value.length - 1].value.startsWith("https")) {
              authenInf[key] = value[value.length - 1].value;
            } else {
              authenInf[key] = `${mediaServerAddr}${value[value.length - 1].value}`
            }
          } else {
            authenInf[key] = "";
          }
        })

        next();
      }

      const getServicesRegisted = (next) => {
        if (!authenInf || !authenOnline) {
          return next();
        }

        let query = {}
        if (!authenInf.isTaxi) {
          query = {
            carType: null,
            hireDriver: {$ne:1}
          }
        } else if (authenInf.isTaxi) {
          query = {
            carType: {$lte: parseInt(authenInf.carType)}
          }
        } else {
          next()
        }
        OrderType
          .find(query)
          .select('_id')
          .lean()
          .exec((err, results)=>{
            if (results && results.length) {
                servicesRegisted = results.map(orderType=>orderType._id.toString())
            }
            next()
          })
      }


      const updateServices = next =>{

        if (!authenInf || !authenOnline) {
          return next();
        }

        if (servicesRegisted && servicesRegisted.length) {
          Rider
              .findOneAndUpdate({
                member: userId
              },{
                serviceRunning: servicesRegisted
              },{
                new:true
              })
              .select('serviceRunning')
              .lean()
              .exec((err, result) => {
                if(err) {
                  return next({
                    code: CONSTANT.CODE.SYSTEM_ERROR,
                    message: MESSAGE.SYSTEM.ERROR,
                  })
                }
                const dataRedis =  JSON.stringify(result)
                redisConnection.set(`cacheman:cachegoose-cache:rider:${userId}`, dataRedis, (err, res) => {
                });
                const serviceRunning = result.serviceRunning.map(String)

                const options = {
                  method: 'POST',
                  uri: `${locationServerAddr}/api/v2.0/shipper/update-service`,
                  body: {
                    id: userId,
                    services: serviceRunning
                  },
                  json: true
                }
                rp(options)
                  .then((result) => {
                    if(result.code !== 200) {
                      return next({
                        code: CONSTANTS.CODE.FAIL,
                        message: MESSAGE.SYSTEM.ERROR
                      })
                    }
                    next()
                  })
                  .catch((err) => {
                    return next(err);
                  })
              })
        } else {
          next()
        }
      }

      const createShipperAuthentication = (next) => {
        if (!authenInf || !authenOnline) {
          return next();
        }

        let objCreate = {
          ...authenInf,
          photo: authenInf.avatar.replace(mediaServerAddr, ''),
          media: mediaServerAddr,
          identityNumber: authenInf.identityNum,
          onlineAuthen: 1,
          bike: authenInf.isTaxi ? 0 : 1,
          carType: authenInf.carType ? authenInf.carType : null,
          region,
          regionTransaction: region,
          identityCard: authenInf.identityFacadeImg.replace(mediaServerAddr, ''),
          identityCardInf: {
            address: authenInf.address,
            id: authenInf.identityNum,
            name: authenInf.name
          },
          licenseCardInf: {
            address: authenInf.address,
            id: authenInf.drivingLicenseNum,
            name: authenInf.name
          },
          registrationMotorInf: {
            address: authenInf.address,
            brand: authenInf.brand,
            color: authenInf.color,
            plate: authenInf.licensePlate,
            id: '',
            name: authenInf.name
          },
          phonesRelation: [authenInf.phoneRelative1, authenInf.phoneRelative2]
        }
        if(region === 'vietnam:danang' && Date.now() < 1750006800000) {
          objCreate.uniformPromote = 1
        }

        ShipperAuthentication
          .create(objCreate, (err) => {
            next();
          });
      }

      const updateMember = (next) => {
        const objUpdate = { training: 1, receivePushOrder: 1 };
        const cointsChange = newVersion ? changeMoney - money : changeMoney;
        if (authenOnline) {
          objUpdate.regionTransaction = authenInf.region
          objUpdate['ship.isAuthen'] = 1;
          objUpdate['$inc'] = {coints: cointsChange}

          if (authenInf && authenInf.avatar) {
            objUpdate['facebook.picture'] = authenInf.avatar;
          }

          if (authenInf && authenInf.name) {
            objUpdate['name'] = authenInf.name;
            objUpdate['facebook.name'] = authenInf.name;
          }
        }
        if(openAccount && idBlock) {
          objUpdate.blockOrderUtil = 0
          objUpdate.freezed = 0

          BlockLog
          .update({
            _id: idBlock
          }, {
            statusTraining: 1
          })

          if (blockLog.type !== 'REST_BLOCK') {
            ReqUnlockMember
              .create({
                member: userId,
                amount: blockLog.amount,
                reTraining: blockLog.reTraining,
                description: 'Hệ thống tự động mở tài khoản Online',
                type: blockLog.type,
                blockType: blockLog.blockType,
                approvedAt: Date.now(),
                approvedReason: 'Hệ thống tự động mở tài khoản Online',
                requester: '594e082d885ac733cdb9fa26',
                approver: '594e082d885ac733cdb9fa26',
                status: 2,
                region
              })
          }
        }

        Members
          .findOneAndUpdate({ _id: userId }, objUpdate, {new: true})
          .lean()
          .exec((err, result) => {
            if (err || !result) {
              return next(err || new Error('Member not found'));
            }

            if (authenOnline && cointsChange) {
              const initCoints = result.coints - cointsChange;

              const transactionLog = {
                "message": newVersion ? 'Thanh toán tiền đồng phục' : "Nạp tiền xác thực online",
                "member": userId,
                "data": {
                  "type": newVersion ? 54 : 7,
                  "gateway": "online",
                  "bonus": 0,
                  "discount": 0,
                  "amount": cointsChange,
                  "initialCoints": initCoints,
                  "finalCoints": result.coints,
                  "initialRealMoney": result.realMoney,
                  "finalRealMoney": result.realMoney,
                  "initialRealMoneyShop": result.realMoneyShop,
                  "finalRealMoneyShop": result.realMoneyShop,
                  "initialMoney": result.money,
                  "finalMoney": result.money,
                  "idTransaction": uuid(),
                },
                "region": result.region,
                "createdAt": Date.now()
              }

              TransactionLog.create(transactionLog, (err, result) => {});
            }
            if(authenOnline) {
              next(null, {
                code: CONSTANTS.CODE.SUCCESS,
                message: {
                  head:'Thông báo',
                  body: 'Tài khoản của bạn đã được xác thực thành công. Vui lòng kiểm tra trạng thái trực tuyến của ứng dụng để bắt đầu nhận đơn'
                }
              });
            } else if(openAccount && blockLog) {
              next(null, {
                code: CONSTANTS.CODE.SUCCESS,
                message: {
                  head:'Thông báo',
                  body: blockLog.type === 'REST_BLOCK' ? 'Bạn đã gửi thông tin thành công. Vui lòng đợi HeyU duyệt và mở khoá tài khoản cho bạn. Xin cảm ơn.' : 'Tài khoản của bạn đã hoạt động trở lại. Vui lòng kiểm tra trạng thái trực tuyến của ứng dụng để tiếp tục nhận đơn.'
                }
              });
            } else {
              next(null, {
                code: CONSTANTS.CODE.SUCCESS
              });
            }

          })
      }

      async.waterfall([
        getConfigAuthenOnline,
        getBlockLog,
        getAuthenInf,
        transferData,
        getServicesRegisted,
        updateServices,
        createShipperAuthentication,
        updateMember
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: message.SYSTEM.ERROR
        });

        res.json(data || err);
      })
    },

    blockCreateOrder(req, res) {
      const userId = req.body.id;
      if(!userId) {
        return res.json({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: message.SYSTEM.ERROR
        });
      }
      Members
        .update({_id: userId}, {blockCreateOrder: 1})
        .exec((err, result) => {
          if(err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: message.SYSTEM.ERROR
            });
          }

          return res.json({
            code: CONSTANTS.CODE.SUCCESS
          })
        })
    },

    toggleReceiveOrder(req, res) {
      const userId = req.user.id;
      const isReceive = _.get(req, 'body.isReceive', 0);
      const hideMessage = _.get(req, 'body.hideMessage', 0);

      const checkTraining = (next) => {
        if(isReceive === 0) {
          return next();
        }
        Members
          .count({
            _id: userId,
            training: 0
          })
          .exec((err, count) => {
            if(err) {
              return next(err)
            }
            if(count) {
              return next({
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: {
                  head:'Thông báo',
                  body: 'Bạn chưa hoàn thành khóa đào tạo của HeyU, vui lòng tham gia đào tạo để bật trực tuyến dịch vụ. Xin cảm ơn!'
                }
              })
            }
            next();
          })
      }

      const toggle = (next) => {
        if(isReceive === 1 || isReceive === 0) {
          Members
            .update({_id: userId}, {receivePushOrder: isReceive})
            .exec((err, result) => {
              if(err) {
                return next({
                  code: CONSTANTS.CODE.SYSTEM_ERROR,
                  message: message.SYSTEM.ERROR
                });
              }

              if (hideMessage) {
                return next({
                  code: CONSTANTS.CODE.SUCCESS
                });
              } else {
                return next({
                  code: CONSTANTS.CODE.SUCCESS,
                  message: isReceive ? message.USER.TURN_ON_PUSH_ORDER : message.USER.TURN_OFF_PUSH_ORDER
                });
              }
            });
        } else {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: message.SYSTEM.ERROR
          });
        }
      }


      async.waterfall([
        checkTraining,
        toggle
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: message.SYSTEM.ERROR
        });

        res.json(data || err);
      });
    },

    getPoint(req, res) {
      const userId = _.get(req, 'user.id', '');

      if(!userId) {
        return res.json({
          code: CONSTANTS.CODE.FAIL
        })
      }

      Members
        .findById(userId, "point")
        .lean()
        .exec((err, result) => {
          if(err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR
            })
          }

          return res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: result
          })
        })
    },

    historyTransactionPoint(req, res) {
      const from = req.body.from || Date.now();
      const limit = req.body.limit || 10;
      const userId = req.user.id;

      PointTransactionLog
        .find({
          member: userId,
          createdAt: {
            $lt: from
          }
        })
        .sort("-createdAt")
        .limit(limit)
        .lean()
        .exec((err, results) => {
          if(err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: message.SYSTEM.ERROR
            })
          }

          return res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: results
          })
        })
    },

    loginWithoutPassword(req, res) {
      const phone = req.body.phone;
      const token = req.body.token;
      const code = req.body.code;
      const fromPartner = _.get(req, 'body.fromPartner', 0)
      const activatePartner = _.get(req, 'body.activatePartner', 0)
      const appName = _.get(req,'body.appName', '');
      const fromTickBox = req.body.fromTickBox || '';
      const deviceId = req.body.deviceId;

      let memberResult;

      let memberToken = '';
      let userInfDB;
      let memberExists = false
      let tokenRedis;

      const checkParams = (next) => {

        if(phone && fromPartner) {
          return next();
        }

        if (!phone || !token) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: message.SYSTEM.ERROR
          });
        }
        next();
      }

      const getPhoneFromToken = (next) => {
        if(fromPartner) {
          return next();
        }
        const options = {
          method: 'POST',
          uri: `${codePhoneAddr}/api/v1.0/check-code`,
          body: {
              token, code, phone
          },
          json: true // Automatically stringifies the body to JSON
        };

        request(options, (err, response, result) => {
          if(err) {
            mailUtil
              .sendMail(` --- ERR ${codePhoneAddr}/api/v1.0/check-code --- ${err}`);
          }
          if(err || !response || response.statusCode !== 200 || !result || result.code !== 200) {
            if(activatePartner) {
              return next({
                code: CONSTANTS.CODE.WRONG_PARAMS,
                message: message.USER.INVALID_OTP
              })
            }
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: message.SYSTEM.ERROR
            })
          }

          next();
        })
      }
      const checkMemberExists = (next) => {
        memberToken = uuid();
        Members
          .findOne({
            phone: phone
          })
          .lean()
          .exec((err,result) => {
            if(err) {
              return next(err)
            }
            memberExists = result ? true : false
            next();
          })
      }

      const createIfNotExists = (next) => {
        if(memberExists) {
          return next()
        }

        let objCreate = {
          phone: phone,
          status: 1
        }
        if(fromTickBox) {
          objCreate.fromTickBox = 1
        }

        Members
          .create(objCreate,(err, result) => {
            if(err) {
              return next(err)
            }
            if(!result) {
              return next({
                code: CONSTANTS.CODE.WRONG_PARAMS,
                message: message.SYSTEM.ERROR
              })
            }
            userInfDB = result.toObject();
            next();
          })
      }

      const updateIfExits = (next) => {
        if(!memberExists) {
          return next();
        }

        const options = {
          'new': true,
          upsert: true,
          setDefaultsOnInsert: true
        };

        Members
          .findOneAndUpdate({
            phone
          }, {
            status: 1
          }, options)
          .lean()
          .exec((err, result) => {
            if(err || !result) {
              return next(err || new Error(`Not found user with that phone`));
            }

            if(result.blockUtil > Date.now()) {
              ReasonBlock
                .find({member: result._id})
                .sort({"createdAt": -1})
                .limit(1)
                .lean()
                .exec((err, resultBlock) => {
                  if(err) {
                    return next({
                      code: CONSTANTS.CODE.SYSTEM_ERROR,
                      message: message.SYSTEM.ERROR
                    });
                  }

                  let messageBlock;
                  if(resultBlock.length === 1 && resultBlock[0].message) {
                    messageBlock = {
                      head: 'Thông báo',
                      body: `Tài khoản của bạn đã bị khoá tới ${moment(result.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')} (${resultBlock[0].message}). Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                    }
                  } else {
                    messageBlock = {
                      head: 'Thông báo',
                      body: `Tài khoản của bạn đã bị khoá tới ${moment(result.blockUtil).format('HH:mm:ss ngày DD/MM/YYYY')}. Nếu có vấn đề thắc mắc vui lòng liên hệ trực tiếp với chúng tôi. Hotline: 1900.633.689. Xin cảm ơn.`
                    }
                  }

                  return next({
                    code: CONSTANTS.CODE.FAIL,
                    message: messageBlock
                  })
                })

              return;
            }

            userInfDB = result;
            next();
          });
      }

      const handleRegisterService = (next) => {
        OrderType
          .find({move: {$ne: 1}, hireDriver: {$ne:1}}, '_id')
          .lean()
          .exec((err, result) => {
            if (err || !result) {
              return next(err || new Error(`Not found order type info`));
            }

            const orderTypeId = result.map(orderType => orderType._id.toHexString());
            Rider.count({member: userInfDB._id}, (err, count) => {
              if (count > 0) {
                return next();
              }

              Rider.create({
                member: userInfDB._id,
                serviceRunning: service.order.concat(orderTypeId)
              }, (err, result) => {
                if(err) {
                  return next(err);
                }
                next();
              })
            })
          })
      }

      const makeSingleLogin = (next) => {
        const userId = userInfDB._id.toHexString();


        let stringUser = `user:${userId}`

        if (appName) {
          stringUser = `${appName}:${userId}`;

          Members
            .count({
              _id: userId,
              type: 0,
              appName: 'customer'
            })
            .exec((err,count) => {
              if(!err && count) {
                redisConnection.get(`user:${userId}`, (err, tokenDel) => {
                  redisConnection.del(`user:${tokenDel}`, (err, result) => {
                  });
                })
              }
            })
        }

        redisConnection.get(stringUser, (err, token) => {
          if(err) {
            return next(err);
          }

          if(token) {

            let stringToken = `user:${token}`

            if (appName) {
              stringToken = `${appName}:${token}`;
            }

            redisConnection.del(stringToken, (err, result) => {
              if(err) {
                return next(err);
              }

              next();
            });
          } else {
            next();
          }
        });
      }

      const getToken = (next) => {

        redisConnection.get(`user:${userInfDB._id.toHexString()}`, (err, token) => {
          tokenRedis = token;

          next();
        });
      }

      const generateToken = (next) => {
        const userId = userInfDB._id.toHexString();
        const objSign = {
          id: userId,
          accessToken: _.get(req, 'body.access_token', ''),
          platform: req.body.platform || 'unknown'
        }

        let stringUser = `user:${userId}`
        let stringToken = `user:${memberToken}`

        if (appName) {
          stringUser = `${appName}:${userId}`
          stringToken = `${appName}:${memberToken}`
        }

        const fnc =
          redisConnection
            .multi()
            .set(stringToken, JSON.stringify(objSign))
            .set(stringUser, memberToken)

        fnc
          .exec((err, result) => {
            if(err) {
              return next(err);
            }

            redisConnection.del(`otp:${phone}`);

            const data = _.merge({}, userInfDB, {memberToken});
            _.unset(data, 'facebook.token');
            _.unset(data, 'password');
            data.isExpire = (data.expireTime < Date.now());

            // publish event
            if(Date.now() - data.createdAt <= 30000) {
              redisConnectionForPubSub.publish("new_user", data._id.toHexString());
            }
            memberResult = data
            next();
          });
      }

      const pushToOldDevice = (next) => {

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          member: memberResult
        });

        TrackingAction
          .find({
            member: memberResult._id
          })
          .sort('-createdAt')
          .limit(1)
          .lean()
          .exec((err, results) => {

            if(err) {
              return;
            }

            if(!results.length) {
              return;
            }

            if(results[0].otherInf && results[0].otherInf.uniqueId && deviceId !== results[0].otherInf.uniqueId && !fromPartner) {
              let member = results[0].member
              const options = {
                method: 'POST',
                uri: `${notifyServiceAddr}/api/v1.0/push-notification/member`,
                body: {
                    userId: member,
                    title: "Thông báo",
                    appName,
                    message: `Tài khoản của bạn đã đăng nhập vào thiết bị khác. Vui lòng kiểm tra lại nếu bạn không thực hiện hành động này. Liên hệ Hotline 1900.633.689 để được hỗ trợ.`
                },
                json: true
              };

              rp(options)
                .then((result) => {
                })
                .catch((err) => {
                });
            }

          })
      }

      async.waterfall([
        checkParams,
        getPhoneFromToken,
        checkMemberExists,
        createIfNotExists,
        updateIfExits,
        handleRegisterService,
        makeSingleLogin,
        getToken,
        generateToken,
        pushToOldDevice
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: message.SYSTEM.ERROR
        });

        res.json(data || err);
      });
    },
    getDepositTransaction(req, res) {
      const from = req.body.from || Date.now();
      const limit = req.body.limit || 10;
      const userId = req.user.id;

      TransactionLog
      .find({
        member: userId,
        'data.type':{$in:shopTransactionType},
        createdAt: {
          $lt: from
        }
      })
      .sort("-createdAt")
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if(err) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: message.SYSTEM.ERROR
          })
        }
        return res.json({
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      })
    },
    getTickBoxTransaction(req, res) {
      const from = req.body.from || Date.now();
      const limit = req.body.limit || 10;
      const userId = req.user.id;

      TransactionLog
      .find({
        member: userId,
        'data.type':{$in:tickTransactionType},
        createdAt: {
          $lt: from
        }
      })
      .sort("-createdAt")
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        if(err) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: message.SYSTEM.ERROR
          })
        }
        return res.json({
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      })
    },
    changeAmountDeposit(req, res) {
      let deposit = req.body.deposit || 0;
      const userId = req.user.id;
      if(deposit > 10000000) {
        deposit = 10000000
      }
      Members
        .findOneAndUpdate({
          _id: userId
        }, {
          'ship.amountDeposit': deposit
        }, {
          new: true
        })
        .lean()
        .exec((err, result) => {
          if (err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: message.SYSTEM.ERROR
            })
          }
          if(!result || !result.ship) {
            return res.json({
              code: CONSTANTS.CODE.SUCCESS
            })
          }
          if(req.body.deposit > 10000000) {
            res.json({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                amountDeposit: result.ship.amountDeposit
              },
              message: {
                head: 'Thông báo',
                body: 'Đã cài đặt số tiền ứng mang theo ở mức cao nhất - 10.000.000 đ'
              }
            })
          } else {
            res.json({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                amountDeposit: result.ship.amountDeposit
              }
            })
          }
        })
    }
}


// helpers
function like_dislike(req,res,like_dislike){
  let erList ='';
  let edList ='';
  let count ='';
  if (like_dislike) {
    erList = 'likerList'
    edList = 'likedList'
    count = 'likes'
  }else{
    erList = 'dislikerList'
    edList = 'dislikedList'
    count = 'dislikes'
  }
  const userId = req.user.id;
  const facebookId = _.get(req, 'body.facebookId', '');
  if(!facebookId) {
      return res.json({
          code: CONSTANTS.CODE.SUCCESS
      });
  }
  const options = {
      'new': true,
      upsert: true,
      setDefaultsOnInsert: true
  };

  async.waterfall([
    // check liked or not
    (callback)=>{
      Members
          .findOne({_id: userId,[`${edList}.facebookId`]:facebookId})
          .lean()
          // .cache(0, `members:${userId}`)
          .exec((err, result) => {
              if(err) {
                  return callback({
                      code: CONSTANTS.CODE.SYSTEM_ERROR,
                      message: message.SYSTEM.ERROR
                  });
              }
              if (result) {
                // liked
                callback(null,false)
              }else{
                // not yet
                callback(null,true)
              }
          });
    },
    (state,callback)=>{
      let updateObj = {};
      if (state) {
        updateObj = {
          $push: {[`${edList}`] : {facebookId: facebookId } }
        }
      }else{
        updateObj = {
          // $pull: {[`${edList}`] : {facebookId: facebookId } }
        }
      }
      Members
          .findOneAndUpdate({_id: userId},
            updateObj,
            options)
          .lean()
          .exec((err, result) => {
              if(err) {
                  return callback({
                      code: CONSTANTS.CODE.SYSTEM_ERROR,
                      message: message.SYSTEM.ERROR
                  });
              }
              callback(null,state);
          });
    },
    // find facebookId in member table ,
    (state,callback)=>{
      let updateObj = {};
      if (state) {
        updateObj = {
          $inc : {[`${count}`] : 1} ,
          $push: {[`${erList}`] : {userId: userId } }
        }
      }else{
        updateObj = {
          // $inc : {[`${count}`] : -1} ,
          // $pull: {[`${erList}`] : {userId: userId } }
        }
      }
      Members
          .findOneAndUpdate({'facebook.id': facebookId},
            updateObj,
            {'new':true})
          .lean()
          .exec((err, result) => {
              if(err) {
                  return callback({
                      code: CONSTANTS.CODE.SYSTEM_ERROR,
                      message: message.SYSTEM.ERROR
                  });
              }
              if (result) {
                callback('OK',state)
              }else{
                callback(null,state);
              }

          });
    },
    // find realId in ghostMembers table
    (state,callback)=>{
      let updateObj = {};
      if (state) {
        updateObj = {
          $inc : {[`${count}`] : 1} ,
          $push: {[`${erList}`] : {userId: userId } }
        }
      }else{
        updateObj = {
          // $inc : {[`${count}`] : -1} ,
          // $pull: {[`${erList}`] : {userId: userId } }
        }
      }
      GhostMembers
          .findOneAndUpdate({'facebook.id': facebookId},
            updateObj,
            options)
          .lean()
          .exec((err, result) => {
              if(err) {
                  return callback({
                      code: CONSTANTS.CODE.SYSTEM_ERROR,
                      message: message.SYSTEM.ERROR
                  });
              }
              if (result) {
                callback('OK',state)
              }else{
                callback({
                  code: CONSTANTS.CODE.SYSTEM_ERROR,
                  message: 'not found facebook.id : ' + facebookId
                },state);
              }
          });
    }

  ],(err , state)=>{
    if (err && err !== 'OK') {
      return res.json(err);
    }else{
      cachegoose.clearCache(`members:${userId}`);
      return res.json({
        code: CONSTANTS.CODE.SUCCESS,
        state:state
      });
    }
  })

}

function getDataToMerge(realId, callback) {
  let data2Merge;

  GhostMembers
    .find({'facebook.realId': realId})
    .lean()
    .exec((err,doc) => {
      if (err) {
          return callback(err);
      } else if (doc.length > 0) {
        data2Merge = {
          likes : 0,
          likerList:[],
          dislikes : 0,
          dislikerList:[],
          shop: {
            totalPost: 0,
            postList:[]
          }
        }
        for(let i =0; i< doc.length ; i++){
          data2Merge.likes += doc[i].likes;
          data2Merge.likerList = data2Merge.likerList.concat(doc[i].likerList);
          data2Merge.dislikes += doc[i].dislikes;
          data2Merge.dislikerList = data2Merge.dislikerList.concat(doc[i].dislikerList);
          data2Merge.shop.totalPost += _.get(doc[i],'shop.totalPost',0) ;
          data2Merge.shop.postList = data2Merge.shop.postList.concat(_.get(doc[i],'shop.postList',[]))
        }
        // delete doc
        GhostMembers.remove({'facebook.realId':realId}).exec();
      }
      callback(null, data2Merge);
    })
}
