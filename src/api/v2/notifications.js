import _ from 'lodash';
import CONSTANTS from '../../const';

export default {
    getHotNews(req, res) {
      const userId = req.user.id;
      const type = req.body.type;
      const region = _.get(req, 'body.regionName', '');
      const serviceId = req.body.serviceId;

      let isInPromote = false;

      const checkUserIsInPromote = (next) => {
        if(type !== 2 || region !== 'hn') {
          return next();
        }

        Members
          .findOne({
            _id: userId,
            createdAt: {
              $gte: 1555002000000
            },
            coints: 0,
            realMoney: 0,
            'ship.totalRides': 0,
            'ship.totalRejects': 0,
            'ship.isAuthen': 0,
            blockOrderUtil: 0
          })
          .lean()
          .exec((err, result) => {
            if(err) {
              return next(err)
            }

            if(result) {
              isInPromote = true;
            }

            next();
          })
      }

      const getHotNews = (next) => {
        let hotNewPromote;

        const tryPromoteHotNew = (done) => {
          if(!isInPromote) {
            return done();
          }

          HotNew
            .findOneAndUpdate({
              _id: "5be2828b59996b020faff92d",
              members: {
                '$ne': userId
              },
              validUtil: {
                $gte: Date.now()
              },
              hotNewType: {$ne: 'special'}
            }, {
              '$push': {
                members: userId
              }
            })
            .lean()
            .exec((err, data) => {
              if(err) {
                return done(err);
              }

              hotNewPromote = data;

              done();
            })
        }

        const getNormalHotNew = (done) => {
          if(isInPromote && hotNewPromote) {
            return done(null, hotNewPromote);
          }

          HotNew
            .get({userId, type, region, serviceId}, (err, data) => {
              if(err) {
                return done(err);
              }

              done(null, data);
            })
        }

        async.waterfall([
          tryPromoteHotNew,
          getNormalHotNew
        ], (err, data) => {
          if(err) {
            return next(err);
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data
          })
        })
      }

      async.waterfall([
        checkUserIsInPromote,
        getHotNews
      ], (err, data) => {
        err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      })
    },
    notify(req, res) {
        res.json({
            code: 200
        });

        const platform = _.get(req, 'body.platform', '');
        const notify_token = _.get(req, 'body.notify_token', '');
        const member = req.user.id;
        const appName = _.get(req,'body.appName', '');
        const newVersion = _.get(req, 'body.newVersion', 0)
        const isSafari = _.get(req, 'body.isSafari', 0)
        let modelQuery = Notifications;
        if(appName && appName === 'driver') {
          modelQuery = NotificationDrivers
        }
        if(appName && appName === 'tickbox') {
          modelQuery = NotificationTickBox
        }
        if (appName && appName === 'heywow') {
          modelQuery = NotificationHeyWow
        }

        if (platform === 'web') {
          modelQuery = NotificationWeb
        }

        if (appName && appName === 'staff') {
          modelQuery = NotificationStaff
        }

        if(!platform || !notify_token) {
          return;
        }

        const getFollowMember = (next) => {
          modelQuery
            .findOne({member})
            .lean()
            .exec(next)
        }

        const getFollowToken = (next) => {
          modelQuery
            .findOne({notify_token, platform})
            .lean()
            .exec(next)
        }

        async.parallel([
          getFollowToken,
          getFollowMember
        ], (err, results) => {
          if(!err) {
            const followToken = results[0];
            const followMember = results[1];
            const currentTime = Date.now();


            if(!followToken && !followMember) {
              modelQuery
                  .create({member, notify_token, platform, createdAt: currentTime, updatedAt: currentTime, newVersion, isSafari }, (err, result) => {
                  })
            } else if(followToken && !followMember) {
              modelQuery.update({_id: followToken._id}, {member, updatedAt: currentTime, newVersion}).exec();
            } else if(!followToken && followMember) {
              modelQuery
                .create({member, notify_token, platform, createdAt: currentTime, updatedAt: currentTime, newVersion, isSafari }, (err, result) => {
                });
              modelQuery
                .update({_id: followMember._id}, {$unset : { member : 1}}).exec();
            } else {
              if(followMember.notify_token !== notify_token || followMember.platform !== platform) {
                modelQuery
                  .update({_id: followMember._id}, {$unset : { member : 1}}).exec();
                modelQuery.update({_id: followToken._id}, {member, updatedAt: currentTime, newVersion}).exec();
              } else if(newVersion && !followMember.newVersion) {
                modelQuery.update({_id: followToken._id}, {newVersion}).exec();
              }
            }
          }
        })
    },

    voipNotification(req, res) {
        res.json({
          code: 200
        });

        const notify_token = _.get(req, 'body.notify_token', '');
        const member = req.user.id;
        const appName = _.get(req, 'body.appName', '');

        let modelQuery = VoipToken;
        if (appName === 'driver') {
          modelQuery = VoipTokenDriver
        }
        if (appName === 'tickbox') {
          modelQuery = VoipTokenTickBox
        }
        if (appName === 'heywow') {
          modelQuery = VoipTokenHeyWow
        }
        if (appName === 'staff') {
          modelQuery = VoipTokenStaff
        }

        if(!notify_token) {
          return;
        }

        const getFollowMember = (next) => {
          modelQuery
            .findOne({member})
            .lean()
            .exec(next)
        }

        const getFollowToken = (next) => {
          modelQuery
            .findOne({notify_token})
            .lean()
            .exec(next)
        }

        async.parallel([
          getFollowToken,
          getFollowMember
        ], (err, results) => {
          if(!err) {
            const followToken = results[0];
            const followMember = results[1];

            if(!followToken && !followMember) {
              modelQuery
                .create({member, notify_token}, (err, result) => {
                })
            } else if(followToken && !followMember) {
              modelQuery.update({_id: followToken._id}, {member, updatedAt: Date.now()}).exec();
            } else if(!followToken && followMember) {
              modelQuery
                .create({member, notify_token}, (err, result) => {
                });

                modelQuery
                .update({_id: followMember._id}, {$unset : { member : 1}}).exec();
            } else {
              if(followMember.notify_token !== notify_token) {
                modelQuery
                  .update({_id: followMember._id}, {$unset : { member : 1}}).exec();
                  modelQuery.update({_id: followToken._id}, {member, updatedAt: Date.now()}).exec();
              }
            }
          }
        })
    },

    createNotification(req, res) {
      res.json({
        code:CONSTANTS.CODE.SUCCESS
      })
      const title = req.body.title;
      const message = req.body.message;
      const data = req.body.data;
      const roles = req.body.roles
      SavedNotification
        .create({
          title,message,data,roles
        },(err, result) => {})
    },

    count(req,res) {

      const region = req.body.regionName;
      const mode = req.body.modeApp;
      const userId = req.user.id;

      SavedNotification
        .count({
          $or:[
            {
              'region.allow': 'all',
              'region.deny':{
                $ne: region
              }
            },
            {
              'region.allow': region
            }
          ],
          mode,
          seen: { $ne: userId },
          status:1
        })
        .lean()
        .exec((err, result) => {
          if(err) {
            return res.json({
              code:CONSTANTS.CODE.SYSTEM_ERROR
            })
          }
          res.json({
            code:CONSTANTS.CODE.SUCCESS,
            data: result
          })
        })
    },

    list(req,res) {
      const from = req.body.from || Date.now();
      const region = req.body.regionName;
      const mode = req.body.modeApp;
      const userId = req.user.id;

      const query = {
        $or:[
          {
            'region.allow': 'all',
            'region.deny':{
              $ne: region
            }
          },
          {
            'region.allow': region
          }
        ],
        mode,
        status:1,
        createdAt: {$lt:from},
        type: {$ne: 'special'}
      }
      SavedNotification
        .find(query)
        .sort({createdAt:-1})
        .limit(10)
        .lean()
        .exec((err, result) => {
          if(err) {
            return res.json({
              code:CONSTANTS.CODE.SYSTEM_ERROR
            })
          }
          let dataBack = [];
          result.forEach((record) => {
            const seen = (record.seen.indexOf(userId) !== -1);
            dataBack.push({
              title: record.title,
              message: record.message,
              data: record.data,
              createdAt: record.createdAt,
              seen: seen,
              _id: record._id
            })
          })
          res.json({
            code:CONSTANTS.CODE.SUCCESS,
            data: dataBack
          })
        })
    },

    getDetailNotification(req,res) {

      const notificationId = req.body._id;

      SavedNotification
        .findOne({
          _id: notificationId,
          type: {$ne: 'special'}
        })
        .lean()
        .exec((err, result) => {
          if(err) {
            return res.json({
              code:CONSTANTS.CODE.SYSTEM_ERROR
            })
          }

          res.json({
            code:CONSTANTS.CODE.SUCCESS,
            data: result
          })
        })
    },

    seenNotify(req,res) {
      res.json({
        code:CONSTANTS.CODE.SUCCESS
      })
      const notificationId = req.body.notificationId;
      const userId = req.user.id;
      SavedNotification.findOneAndUpdate(
         {
           _id: notificationId
         },
         { $addToSet: { seen: userId } },
         {'new': true}, (err, result) => {});
    },

    seenAllNotify(req,res) {

      res.json({
        code:CONSTANTS.CODE.SUCCESS
      })
      const userId = req.user.id;
      const region = req.body.regionName;
      const mode = req.body.modeApp;

      SavedNotification.update(
          {
            $or:[
              {
                'region.allow': 'all',
                'region.deny':{
                  $ne: region
                }
              },
              {
                'region.allow': region
              }
            ],
            mode,
            status:1
          },
          { $addToSet: { seen: userId } },
          { multi: true}, (err, result) => {});
    }
}
