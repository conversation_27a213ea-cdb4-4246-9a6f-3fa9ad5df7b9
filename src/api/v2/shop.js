import _ from 'lodash';
import async from 'async';
import util from 'util';
import CONSTANTS from '../../const';
import MESSAGE from '../../message';
import Joi from 'joi'
import fs from 'fs.extra'
import uuid from 'uuid/v4'
import rp from 'request-promise'
import {currentServerAddr, amazonS3Addr, cdnServerAddr} from '../../config'

export default {
    authen(req, res) {
      const schemaInput = {
        address: Joi.string().required(),
        storeName: Joi.string().required(),
        phone: Joi.string().required()
      }

      let prefixFile = '';

      const checkParams = (next) => {
        const result = Joi.validate(req.body, schemaInput, {allowUnknown: true, convert: true});

        if(result.error) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGE.SYSTEM.ERROR
          })
        }

        next(null);
      }

      const checkFileExist = (next) => {
        if(!req.files || !req.files.avatar.length || !req.files.frontCer.length || !req.files.backCer.length) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGE.SYSTEM.ERROR
          })
        }

        next(null);
      }

      const handleSaveFile = (next) => {
        prefixFile = uuid();

        const MAP_FOLDER = {
          'avatar': 'avatar',
          'frontCer': 'front-identity',
          'backCer': 'back-identity'
        }

        async.map(['avatar', 'frontCer', 'backCer'], (key, done) => {
          const filePath = `shop/${MAP_FOLDER[key]}/${prefixFile}_${key}.png`;

          const options = {
            method: 'POST',
            uri: `${amazonS3Addr}/api/v1.0/upload`,
            formData: {
              bucket: 'sanship',
              key: filePath,
              fileUpload: {
                value: req.files[key][0].buffer,
                options: {
                  filename: `${key}.png`
                }
              }
            },
            json: true
          }

          rp(options)
            .then((result) => {
              if(result.code !== 200) {
                return done({
                  code: CONSTANTS.CODE.FAIL,
                  message: MESSAGE.SYSTEM.ERROR
                })
              }

              done();
            })
            .catch((err) => {
              return done(err);
            })
        }, (err, result) => {
          if(err) {
            return next(err);
          }

          next(null);
        })
      }

      const createInDb = (next) => {
        const obj = {
          ...req.body,
          member: req.user.id,
          avatar: `/shop/avatar/${prefixFile}_avatar.png`,
          frontCer: `/shop/front-identity/${prefixFile}_frontCer.png`,
          backCer: `/shop/back-identity/${prefixFile}_backCer.png`
        }

        AuthenShopInf.create(obj, (err, result) => {
          if(err) {
            return next(err);
          }

          return next({
            code: 200,
            data: {
              '_id': result._id,
              'updatedAt': result.updatedAt
            }
          })
        });
      }

      async.waterfall([
        checkParams,
        handleSaveFile,
        createInDb
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGE.SYSTEM.ERROR
        });

        res.json(data || err);
      })
    },

    getAuthenInf(req, res) {
      const userId = req.user.id;

      AuthenShopInf
        .findOne({member: userId}, "-member")
        .lean()
        .exec((err, result) => {
          if(_.isError(err)) {
            logger.logError([err], req.originalUrl, req.body);
          }
          if(err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGE.SYSTEM.ERROR
            })
          }

          if(result) {
            result.avatar = `${cdnServerAddr}${result.avatar}`;
            result.frontCer = `${cdnServerAddr}${result.frontCer}`
            result.backCer = `${cdnServerAddr}${result.backCer}`
          }

          res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: result
          });
        })
    },

    modifyAuthenInf(req, res) {
      const schemaInput = {
        id: Joi.string().required(),
        address: Joi.string(),
        phone: Joi.string(),
        storeName: Joi.string()
      }

      const MAP_FOLDER = {
        'avatar': 'avatar',
        'frontCer': 'front-identity',
        'backCer': 'back-identity'
      }

      let currentAuthenInf;
      let prefixFile;
      let listImageChange = [];

      const checkParams = (next) => {
        const result = Joi.validate(req.body, schemaInput, {allowUnknown: true, convert: true});

        if(result.error) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGE.SYSTEM.ERROR
          })
        }

        next(null);
      }

      const checkImageChange = (next) => {
        Object.keys(req.files).forEach((key) => {
          if((key === 'avatar' || key === 'frontCer' || key === 'backCer') && req.files[key].length) {
            listImageChange.push(key);
          }
        })

        next(null);
      }

      const saveFileChange = (next) => {
        if(listImageChange.length === 0) {
          return next(null);
        }

        prefixFile = uuid();

        async.map(listImageChange, (key, done) => {
          const filePath = `shop/${MAP_FOLDER[key]}/${prefixFile}_${key}.png`;

          const options = {
            method: 'POST',
            uri: `${amazonS3Addr}/api/v1.0/upload`,
            formData: {
              bucket: 'sanship',
              key: filePath,
              fileUpload: {
                value: req.files[key][0].buffer,
                options: {
                  filename: `${key}.png`
                }
              }
            },
            json: true
          }

          rp(options)
            .then((result) => {
              if(result.code !== 200) {
                return done({
                  code: CONSTANTS.CODE.FAIL,
                  message: MESSAGE.SYSTEM.ERROR
                })
              }

              done();
            })
            .catch((err) => {
              return done(err);
            })
        }, (err, result) => {
          if(err) {
            return next(err);
          }

          next(null);
        })
      }

      const updateAuthenInf = (next) => {
        const objUpdate = {
          status: 0,
          updatedAt: Date.now()
        }

        if(req.body.storeName) {
          objUpdate.storeName = req.body.storeName
        }

        if(req.body.address) {
          objUpdate.address = req.body.address
        }

        if(req.body.phone) {
          objUpdate.phone = req.body.phone
        }

        listImageChange.forEach((key) => {
          objUpdate[key] = `/shop/${MAP_FOLDER[key]}/${prefixFile}_${key}.png`
        });

        AuthenShopInf
          .findOneAndUpdate({
            _id: req.body.id,
            member: req.user.id
          }, objUpdate, {'new': false})
          .lean()
          .exec((err, result) => {
            if(err) {
              return next(err);
            }

            if(!result) {
              return next(new Error(`Not found authen inf`));
            }

            currentAuthenInf = result;

            next();
          })
      }

      const removeOldFile = (next) => {
        listImageChange.forEach((key) => {
          fs.rmrf(`${__dirname}/public${currentAuthenInf[key]}`, (err) => {
          });
        });

        next(null, {
          code: 200
        });
      }

      async.waterfall([
        checkParams,
        checkImageChange,
        saveFileChange,
        updateAuthenInf,
        removeOldFile
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGE.SYSTEM.ERROR
        });

        res.json(data || err);
      })
    }
}
