import _ from 'lodash';
import async from 'async';
import util from 'util';
import CONSTANTS from '../../const';
import MESSAGE from '../../message';
import Joi from 'joi'
import fs from 'fs.extra'
import uuid from 'uuid/v4'
import {currentServerAddr, cmsServerAddr, mediaServerAddr, notifyServiceAddr} from '../../config'

export default {
  authen(req, res) {
    return res.json({
      code: CONSTANTS.CODE.FAIL,
      message: {
        head: 'Thông báo',
        body: '<PERSON>ui lòng cập nhật phiên bản Săn Ship - HeyU mới nhất trên CHPlay hoặc AppStore để trải nghiệm tính năng xác thực online'
      }
    })
  },
  getAuthenInfNew(req, res) {
    const userId = req.user.id;

    let authenInf;
    const getAuthenInf = (next) => {
      AuthenShipInf
        .findOne({
          member: userId
        })
        .lean()
        .exec((err, result) => {
          if(err) {
            return next(err);
          }

          authenInf = result;

          next();
        });
    }

    const transferData = (next) => {
      if(!authenInf) {
        return next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: authenInf
        })
      }

      const keysText = ['name', 'address', 'facebookUrl', 'message'];
      const keysImage = ['avatar', 'identity', 'motobike', 'avatarWithIdentity'];

      keysText.forEach((key) => {
        if(authenInf && authenInf[key] && authenInf[key].length) {
          const value = authenInf[key];
          if(key === 'message') {
            authenInf[key] = `${value[value.length-1]}`;
          } else {
            authenInf[key] = `${value[value.length-1].value}`;
          }
        } else {
          authenInf[key] = "";
        }
      });
      authenInf.message = 'Vui lòng cập nhật phiên bản Săn Ship - HeyU mới nhất trên CHPlay hoặc AppStore để trải nghiệm tính năng xác thực online'

      keysImage.forEach((key) => {
        if(authenInf && authenInf[key] && authenInf[key].length) {
          const value = authenInf[key];
          if(value[value.length-1].value.startsWith("http") || value[value.length-1].value.startsWith("https")) {
            authenInf[key] = value[value.length-1].value;
          } else {
            authenInf[key] = `${currentServerAddr}${value[value.length-1].value}`
          }
        } else {
          authenInf[key] = "";
        }
      })

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: authenInf
      });
    }

    async.waterfall([
      getAuthenInf,
      transferData
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGE.SYSTEM.ERROR
      });

      res.json(data || err);
    })
  },
  updateAuthenInf(req, res) {
    return res.json({
      code: CONSTANTS.CODE.FAIL,
      message: {
        head: 'Thông báo',
        body: 'Vui lòng cập nhật phiên bản Săn Ship - HeyU mới nhất trên CHPlay hoặc AppStore để trải nghiệm tính năng xác thực online'
      }
    })
  },
  getAuthenInf(req, res) {
    const userId = req.user.id;

    ShipperAuthentication
      .findOne({
        member: userId
      })
      .lean()
      .exec((err, result) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }

        if(err) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGE.SYSTEM.ERROR
          })
        }

        if(result) {
          result.photo = `${cmsServerAddr}${result.photo}`
          result.identityCard = `${cmsServerAddr}${result.identityCard}`
        }

        return res.json({
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        })
      })
  },
  sendSign(req, res) {
    // const userId = req.user.id;
    const id = req.body.id;

    const schemaInput = {
      signContract: Joi.string(),
    }

    let currentAuthenInf;
    let objUpdate = {
      updatedAt: Date.now(),
      $push: {}
    }

    const options = {
      method: 'POST',
      uri: `${notifyServiceAddr}/api/v1.0/push-notification/member`,
      json: true // Automatically stringifies the body to JSON
    };

    const checkParams = (next) => {
      const result = Joi.validate(req.body, schemaInput, { allowUnknown: true, convert: true });

      if (result.error) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGE.SYSTEM.ERROR
        })
      }

      next(null);
    }

    const getCurrentAuthenInf = (next) => {
      AuthenShipInf
        .findOne({
          _id: req.body.id
        })
        .populate('member', 'phone')
        .lean()
        .exec((err, result) => {
          if (err || !result) {
            return next(err || new Error(`Not found authen inf`));
          }

          currentAuthenInf = result;
          // if (currentAuthenInf.currentStep < CONSTANTS.STEP_AUTHEN.CONTRACT) {
          //   return next({
          //     code: CONSTANTS.CODE.FAIL,
          //     message: {
          //       head: 'Thông báo',
          //       body: 'Bạn chưa được phép gửi hợp đồng khi chưa hoàn thành các bước trước'
          //     }
          //   })
          // }
          if (currentAuthenInf.statusContract === 1) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: {
                head: 'Thông báo',
                body: 'Hợp đồng hiện tại đã được duyệt, bạn không cần gửi hợp đồng nữa.'
              }
            })
          }
          next();
        });
    }

    const generateObjectUpdate = (type) => {

      if (req.body[type] && req.body[type].startsWith(mediaServerAddr)) {
        req.body[type] = req.body[type].replace(mediaServerAddr, "");
      }

      objUpdate['$push'][type] = {
        value: req.body[type],
        valid: false,
        uploadedAt: Date.now()
      }
    }

    const updateAuthenInf = (next) => {
      if (req.body.signContract) {
        generateObjectUpdate('signContract');
      }

      if (currentAuthenInf.statusContract < 0) {
        objUpdate.statusContract = 0;
      }

      if (currentAuthenInf.shipperRejected) {
        objUpdate.shipperRejected = 0;
      }

      AuthenShipInf
        .findOneAndUpdate({
          _id: req.body.id,
          statusContract: { $lt: 0 }
        }, objUpdate)
        .lean()
        .exec((err, result) => {
          if (err) {
            return next(err);
          }

          if (!result) {
            return next(new Error(`Not found authen inf`));
          }

          next();
        })
    }

    const pushToAdmin = (next) => {
      next(null, {
        code: CONSTANTS.CODE.SUCCESS
      });

      Config
        .get(CONSTANTS.CONFIG_TYPE.ADMIN_AUTHEN_ONLINE, currentAuthenInf.region, (err, data) => {
          if (data && data.config && data.config.listAdmin) {
            data.config.listAdmin.map((admin) => {
              options.body = {
                userId: admin,
                title: 'Hợp đồng',
                message: `Tài xế ${currentAuthenInf.member.phone} vừa gửi hợp đồng.`,
              }

              request(options, (err, response) => { });
            })
          }
        })
    }

    async.waterfall([
      checkParams,
      getCurrentAuthenInf,
      updateAuthenInf,
      pushToAdmin
    ], (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGE.SYSTEM.ERROR
      });

      options.body = {
        userId: currentAuthenInf.member.toString(),
        title: '',
        appName: 'driver',
        message: '',
        eventName: 'online_authen_update'
      }

      request(options, (err, response) => { });

      res.json(data || err);
    })
  },
  sendBill(req, res) {
    const userId = req.body.userId;

    const schemaInput = {
      userId: Joi.required(),
      receiveUniformAddress: Joi.string().empty('').default(''),
      size: Joi.string().empty('').default(''),
    }

    let currentAuthenInf;
    let objUpdate = {
      sizeUniform: req.body.size,
      currentStep: 3,
      updatedAt: Date.now(),
      paymentAt: Date.now()
    }

    const checkParams = (next) => {
      const result = Joi.validate(req.body, schemaInput, { allowUnknown: true, convert: true });

      if (result.error) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS
        })
      }

      next();
    }

    const getCurrentAuthenInf = (next) => {
      let objQuery = {
        member: userId
      }

      if (!req.body.receiveUniformAddress && req.body.regionName !== 'vietnam:hatinh') {
        objQuery.isTaxi = 1
      } else {
        objQuery.isTaxi = {
          $ne: 1
        }
      }

      AuthenShipInf
        .findOne(objQuery)
        .populate('member', 'phone')
        .lean()
        .exec((err, result) => {
          if (err || !result) {
            return next(err || new Error(`Not found authen inf`));
          }

          currentAuthenInf = result;
          if (currentAuthenInf.currentStep < CONSTANTS.STEP_AUTHEN.PAYMENT) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: {
                head: 'Thông báo',
                body: 'Bạn chưa được phép gửi thông tin thanh toán khi chưa hoàn thành các bước trước'
              }
            })
          }

          next();
        });
    }

    const generateObjectUpdate = (type) => {

      if (req.body[type] && req.body[type].startsWith(mediaServerAddr)) {
        req.body[type] = req.body[type].replace(mediaServerAddr, "");
      }

      if (!objUpdate['$push']) {
        objUpdate['$push'] = {};
      }

      objUpdate['$push'][type] = {
        value: req.body[type],
        valid: true,
        uploadedAt: Date.now()
      }
    }

    const updateAuthenInf = (next) => {

      if (req.body.receiveUniformAddress) {
        generateObjectUpdate('receiveUniformAddress');
      }

      let query = {
        _id: currentAuthenInf._id
      }

      if (currentAuthenInf.statusPayment !== 1) {
        if (currentAuthenInf.statusPayment < 0) {
          objUpdate.statusPayment = 1;
        }

        if (currentAuthenInf.shipperRejected) {
          objUpdate.shipperRejected = 0;
        }

        query.statusPayment = { $lt: 0 };
      }

      AuthenShipInf
        .findOneAndUpdate(query, objUpdate)
        .lean()
        .exec((err, result) => {
          if (err) {
            return next(err);
          }

          if (!result) {
            return next(new Error(`Not found authen inf`));
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS
          });
        })
    }

    async.waterfall([
      checkParams,
      getCurrentAuthenInf,
      updateAuthenInf
    ], (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR
      });

      const options = {
        method: 'POST',
        uri: `${notifyServiceAddr}/api/v1.0/push-notification/member`,
        json: true // Automatically stringifies the body to JSON
      };

      if (currentAuthenInf && currentAuthenInf.member) {
        options.body = {
          userId: currentAuthenInf.member.toString(),
          title: '',
          appName: 'driver',
          message: '',
          eventName: 'online_authen_update'
        }

        request(options, (err, response) => { });
      }

      res.json(data || err);
    })
  },
  paymentBlockAccount(req, res) {
    const userId = req.body.userId;
    const blockId = req.body.blockId;
    const amount = req.body.amount;

    const schemaInput = {
      userId: Joi.required(),
      blockId: Joi.required(),
      amount: Joi.required()
    }

    let objUpdate = {
      statusPayment: 1,
      updatedAt: Date.now()
    }

    const checkParams = (next) => {
      const result = Joi.validate(req.body, schemaInput, { allowUnknown: true, convert: true });

      if (result.error) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS
        })
      }

      next();
    }

    const decreaseCoints = (next) => {
      Members
        .findOneAndUpdate({ _id: userId, coints: {$gte: amount} }, {'$inc': {coints: -amount}}, { new: true })
        .lean()
        .exec((err, result) => {
          if (err) {
            return next(err || new Error('Member not found'));
          }
          if(!result) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: {
                head: 'Thông báo',
                body: 'Số coints còn lại của bạn không đủ để thanh toán phí đào tạo. Vui lòng nạp thêm tiền và thanh toán lại'
              }
            });
          }
          const initCoints = result.coints + amount;

          const transactionLog = {
            "message": 'Thanh toán phí đào tạo lại',
            "member": userId,
            "data": {
              "type": 55,
              "gateway": "online",
              "bonus": 0,
              "discount": 0,
              "amount": -amount,
              "initialCoints": initCoints,
              "finalCoints": result.coints,
              "initialRealMoney": result.realMoney,
              "finalRealMoney": result.realMoney,
              "initialRealMoneyShop": result.realMoneyShop,
              "finalRealMoneyShop": result.realMoneyShop,
              "initialMoney": result.money,
              "finalMoney": result.money,
              "idTransaction": uuid(),
            },
            "region": result.region,
            "createdAt": Date.now()
          }

          TransactionLog.create(transactionLog, (err, result) => { });

          next();
        })
    }

    const updateBlockLog = (next) => {

      let query = {
        _id: blockId,
        member: userId,
        statusPayment: 0
      }

      BlockLog
        .findOneAndUpdate(query, objUpdate)
        .lean()
        .exec((err, result) => {
          if (err) {
            return next(err);
          }

          if (!result) {
            return next(new Error(`Not found block log inf`));
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS
          });
        })
    }

    async.waterfall([
      checkParams,
      decreaseCoints,
      updateBlockLog
    ], (err, data) => {
      if (_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR
      });

      const options = {
        method: 'POST',
        uri: `${notifyServiceAddr}/api/v1.0/push-notification/member`,
        json: true // Automatically stringifies the body to JSON
      };

      options.body = {
        userId: userId.toString(),
        title: '',
        appName: 'driver',
        message: '',
        eventName: 'block_update'
      }

      request(options, (err, response) => { });

      res.json(data || err);
    })
  }
}
