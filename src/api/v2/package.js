import _ from 'lodash';
import async from 'async';
import util from 'util';
import CONSTANTS from '../../const';
import message from '../../message';

export default {
    list(req, res) {
        Package.list(null, (err, results) => {
            if(err) {
                return res.json({
                    code: CONSTANTS.CODE.SYSTEM_ERROR,
                    message: MESSAGE.SYSTEM.ERROR
                })
            }

            res.json({
                code: CONSTANTS.CODE.SUCCESS,
                data: results
            })
        })
    },

    buyPackage(req, res) {
        const _id = _.get(req, 'body._id', '');
        const type = _.get(req, 'body.type', '0');
        let user;
        let packageInf;

        const getUserInf = (next) => {
            Members
                .findById(req.user.id)
                .lean()
                .exec((err, result) => {
                    if(err || !result) {
                        return next(err || new Error(`Not found member`));
                    }

                    user = result;
                    next(null);
                });
        }

        const getServiceInf = (next) => {
            Package
                .get(_id, (err ,result) => {
                    if(err || !result) {
                        return next(err || new Error(`Not found package`));
                    }

                    packageInf = result;
                    next(null);
                })
        }

        const getUserAndServiceInf = (next) => {
            async.parallel([
                getUserInf,
                getServiceInf
            ], (err, results) => {
                if(err) {
                    return next(err);
                }

                next(null);
            })
        }

        const buyPackageByCoint = (next) => {
            if(type !== '0') {
              return next(null, 'buyByRealMoney');
            }
            let realPrice = Math.floor(packageInf.price*(100-packageInf.bonus)/100);
            if(user.coints < realPrice) {
                return next({
                    code: CONSTANTS.CODE.NOT_ENOUGH_COUNT,
                    needCoints: realPrice
                    // message: message.PACKAGE.NOT_ENOUGH_COINT,
                })
            }

            const currentDateMil = Date.now();
            let newExpireTime;

            if(user.expireTime < currentDateMil) {
                newExpireTime = currentDateMil + packageInf.time;
            } else {
                newExpireTime = user.expireTime + packageInf.time;
            }

            const query = {
                _id: req.user.id,
                coints: {
                    $gte: realPrice
                }
            }

            const update = {
                expireTime: newExpireTime,
                $inc: {
                    coints: -realPrice
                }
            }

            const options = {
                new: true
            }

            Members
                .findOneAndUpdate(query, update, options)
                .exec((err, result) => {
                    if(err || !result) {
                        return next(err || new Error(`Not found user`));
                    }

                    next(null ,{
                        code: CONSTANTS.CODE.SUCCESS,
                        message: message.PACKAGE.SUCCESS,
                        newExpireTime: result.expireTime,
                        newCoints: result.coints
                    });
                })
        }

        const buyPackageRealMoney = (data, next) => {
            if(type !== '1') {
              return next(null, data);
            }
            let realPrice = packageInf.price;
            if(user.realMoney < realPrice) {
                return next({
                    code: CONSTANTS.CODE.NOT_ENOUGH_COUNT,
                    needRealMoney: realPrice
                })
            }

            const currentDateMil = Date.now();
            let newExpireTime;

            if(user.expireTime < currentDateMil) {
                newExpireTime = currentDateMil + packageInf.time;
            } else {
                newExpireTime = user.expireTime + packageInf.time;
            }

            const query = {
                _id: req.user.id,
                realMoney: {
                    $gte: realPrice
                }
            }

            const update = {
                expireTime: newExpireTime,
                $inc: {
                    realMoney: -realPrice
                }
            }

            const options = {
                new: true
            }

            Members
                .findOneAndUpdate(query, update, options)
                .exec((err, result) => {
                    if(err || !result) {
                        return next(err || new Error(`Not found user`));
                    }

                    next(null ,{
                        code: CONSTANTS.CODE.SUCCESS,
                        message: message.PACKAGE.SUCCESS,
                        newExpireTime: result.expireTime,
                        newRealMoney: result.realMoney
                    });
                })
        }

        async.waterfall([
            getUserAndServiceInf,
            buyPackageByCoint,
            buyPackageRealMoney
        ], (err, data) => {
            // Write logs
            const objLog = {
                type: type,
                member: req.user.id,
                createdAt: Date.now(),
                error: _.isError(err) ? util.inspect(err) : undefined,
            }

            if(user) {
              objLog.initialCoints = user.coints;
              objLog.initialRealMoney = user.realMoney;
              objLog.initialExpireTime =  user.expireTime;
              objLog.finalCoints = (data && data.newCoints) ? data.newCoints : user.coints;
              objLog.finalRealMoney = (data && data.newRealMoney) ? data.newRealMoney : user.realMoney;
              objLog.finalExpireTime =  (data && data.newExpireTime) ? data.newExpireTime : user.expireTime;
            }

            if(packageInf) {
              objLog.moreTime = packageInf.time;
              objLog.price = packageInf.price;
              objLog.bonus = packageInf.bonus;
            }

            PackageLogs.create(objLog, (err, result) => {
            });
            // end write log

            err && _.isError(err) && (data = {
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: message.SYSTEM.ERROR
            });

            if(!err && !data) {
              data = {
                  code: CONSTANTS.CODE.SYSTEM_ERROR,
                  message: message.SYSTEM.ERROR
              }
            }

            res.json(data || err);
        });
    }
}
