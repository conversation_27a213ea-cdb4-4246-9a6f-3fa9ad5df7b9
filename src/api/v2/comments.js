import * as async from 'async';
import _ from 'lodash';
import { Router } from 'express';
import { auth, comment_access_token_1, comment_access_token_2, comment_access_token_3, comment_access_token_4, comment_access_token_5 } from '../../config';
import cache from 'memory-cache';
import CONSTANTS from '../../const';
import MESSAGE from '../../message';
import rp from 'request-promise';
import {proxyFacebookServerAddr, commentServerAddr} from '../../config'
import request from 'request';

const commentTokenArr = [];
commentTokenArr.push(comment_access_token_1, comment_access_token_2, comment_access_token_3, comment_access_token_4, comment_access_token_5 );

// let idQuerySearching={}
// setInterval(()=>{
//   idQuerySearching={}
// },1000*60*2)

export default {
    uploadCookies(req, res) {
      const userId = req.user.id;
      const cookies = req.body.cookies;
      const options = {
        'new': true,
        upsert: true,
        setDefaultsOnInsert: true
      };

      Cookie
        .findOneAndUpdate(
          {member: userId},
          {cookies}, options)
        .lean()
        .exec((err, result) => {
          if(err) {
            return res.json({
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: MESSAGE.SYSTEM.ERROR
            });
          }

          res.json({
            code: CONSTANTS.CODE.SUCCESS
          });
        });
    },
    postComment(req, res) {
        const userId = req.user.id;
        let cookies = "";
        let cookieString = "";

        const checkCookiesExists = (next) => {
          Cookie
            .findOne({member: userId})
            .lean()
            .exec((err, result) => {
              if(err) {
                return next(err);
              }

              if(!result) {
                return next({
                  code: 404
                });
              }

              cookies = result;
              next();
            });
        }

        const postComment = (next) => {
          const options = {
            method: 'POST',
            uri: `${commentServerAddr}/api/v1.0/facebook/post-comment`,
            body: {
                userId,
                feed_id: req.body.feed_id,
                message: req.body.message,
                cookies
            },
            json: true // Automatically stringifies the body to JSON
          };

          request(options, (err, response, result) => {
            if(err) {
              return next(err);
            }

            next(result);
            if(result.code === 200) {
              Feeds
                  .create({
                      feed_id: req.body.feed_id,
                      member: userId,
                      createdAt: new Date()
                  }, (err, result) => {

                  });
            }
          });
          // const message = _.get(req, 'body.message', '');
          // const feed_id = req.body.feed_id;
          // console.log(message, feed_id);
          // Object.keys(cookies.cookies).forEach((key) => {
          //   cookieString += `${key}=${cookies.cookies[key]};`
          // });
          //
          // const options = {
          //   method: 'GET',
          //   uri: `https://m.facebook.com/groups/${feed_id.split("_")[0]}?view=permalink&id=${feed_id.split("_")[1]}&_rdr`,
          //   headers: {
          //     'user-agent': 'Mozilla/5.0 (Series40; Nokia6300/07.30; Profile/MIDP-2.0 Configuration/CLDC-1.1) Gecko/20100401 S40OviBrowser/********.14',
          //     'cookie': cookieString
          //   }
          // };
          //
          // request(options, (err, response, body) => {
          //   console.log(err, body);
          //   if(err) {
          //     return next(err);
          //   }
          //
          //   const $ = cheerio.load(body);
          //   const title = $("title").text();
          //   if(title === 'Không tìm thấy nội dung' || title === 'Content Not Found') {
          //     return next({
          //       code: 555,
          //       message: {
          //         head: 'Thông báo',
          //         body: 'Có thể bạn chưa gia nhập Group này hoặc bạn đã bị band khỏi group hoặc bài đăng này đã bị xoá, vui lòng ấn vào bình luận để biết chi tiết hơn. Xin cảm ơn.'
          //       }
          //     })
          //   }
          //
          //   if(title === 'Đang chuyển hướng...' || title === 'Redirecting...') {
          //     return next({
          //       code: 300
          //     });
          //   }
          //
          //   const formPostComment = $(`form[method="post"]`); // Cookies died
          //   const uriPostComment = formPostComment.attr("action");
          //   const hiddenValue = formPostComment.children(`input[type="hidden"]`).attr("value");
          //
          //   const options = {
          //     method: 'POST',
          //     uri: `https://m.facebook.com${uriPostComment}`,
          //     headers: {
          //       'content-type': `application/x-www-form-urlencoded`,
          //       'user-agent': 'Mozilla/5.0 (Series40; Nokia6300/07.30; Profile/MIDP-2.0 Configuration/CLDC-1.1) Gecko/20100401 S40OviBrowser/********.14',
          //       'cookie': cookieString
          //     },
          //     body: `fb_dtsg=${hiddenValue}&comment_text=${message}`,
          //   };
          //
          //   request(options, (err, response, result) => {
          //     console.log(err, result);
          //
          //     if(err) {
          //       return next(err);
          //     }
          //
          //     next(null, {code: 200});
          //   });
          // });
        }

        async.waterfall([
            checkCookiesExists,
            postComment
        ], (err, data) => {
          err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGE.SYSTEM.ERROR
          });

          res.json(data || err);
        });
        return;
        const message = _.get(req, 'body.message', '');
        const idComment = req.body.comment_id || req.body.feed_id;
        const access_token = req.user.accessToken

        if(!message) {
            return res.json({
                code: CONSTANTS.CODE.WRONG_PARAMS,
                message: MESSAGE.COMMENT.MESSAGE_EMPTY
            });
        }

        if(!idComment) {
            return res.json({
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: MESSAGE.SYSTEM.ERROR
            });
        }

        // let FB_API_URI = `${auth.facebook.FB_API}/${idComment}/comments?access_token=${req.user.accessToken}`;
        //
        // let options = {
        //     url: FB_API_URI,
        //     form: {
        //         message: req.body.message
        //     }
        // };
        //
        // request.post(options, (error, response, body) => {
        const options = {
          method: 'POST',
          uri: `${proxyFacebookServerAddr}/api/v1.0/facebook/post-comment`,
          body: {
              access_token,
              id: idComment,
              message
          },
          json: true // Automatically stringifies the body to JSON
        };

        // rp(options)
          // .then((result) => {
        request(options, (err, response, result) => {
          if(err || !response || response.statusCode !== 200 || !result) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGE.SYSTEM.ERROR
            });
          }

          const data = result.data || {};

          if (data.id) {
              return res.json({
                  code: CONSTANTS.CODE.SUCCESS
              });
          } else if(data.error && data.error.message === '(#100) Error finding the requested story') {
              return res.json({
                  code: CONSTANTS.CODE.FEED_DELETED,
                  message: MESSAGE.POST.DELETED,
                  subInf: 'Bài viết đã bị xoá'
              });
          } else if(data.error && data.error.message === '(#200) Permissions error') {
              return res.json({
                  code: CONSTANTS.CODE.JOIN_GROUP,
                  subInf: 'Chưa gia nhập group'
              });
          } else if(data.error && data.error.message === '(#100) Error getting the message') {
              return res.json({
                  code: CONSTANTS.CODE.BLOCKED_GROUP,
                  message: MESSAGE.GROUP.LOCKED,
                  subInf: 'Bị block khỏi group'
              });
          } else if(data.error && data.error.message === '(#200) You do not have sufficient permissions to perform this action') {
              return res.json({
                  code: CONSTANTS.CODE.NOT_ACCEPT_PERMISSION,
                  message: MESSAGE.PERMISSION.COMMENT_ERROR,
                  subInf: 'Chưa cho quyền thay mặt đăng lên group'
              });
            } else if(data.error && data.error.message.indexOf('validating') >= 0) {
                return res.json({
                    code: CONSTANTS.CODE.TOKEN_EXPIRY,
                    message: MESSAGE.TOKEN.EXPIRY,
                    subInf: 'Token hết hạn hoặc die'
                });
            } else if(data.error && data.error.message.indexOf('object_id') >= 0) {
                return res.json({
                    code: CONSTANTS.CODE.JOIN_GROUP,
                    subInf: 'Chưa gia nhập group' // // mã lỗi này trả về khi trả lời 1 bình luận,khác với tạo 1 bình luận
                });
            } else if(data.error && data.error.message.indexOf('Unsupported') >= 0) {
                return res.json({
                    code: CONSTANTS.CODE.FEED_DELETED,
                    message: MESSAGE.POST.DELETED,
                    subInf: 'Bài viết đã bị xoá' // // mã lỗi này trả về khi trả lời 1 bình luận,khác với tạo 1 bình luận
                });
            } else if(data.error && data.error.code === 368) {
                return res.json({
                    code: CONSTANTS.CODE.BLOCKED_GROUP,
                    message: MESSAGE.GROUP.PRIVACY
                });
            } else {
                return res.json({
                    code: CONSTANTS.CODE.FAIL,
                    message: (data && data.error && data.error.code && data.error.message ) ? {
                      'head': 'Thông báo',
                      'body': `${data.error.message}. Code: ${data.error.code}. Chụp ảnh lại rồi mở Menu ấn vào Hỗ trợ rồi gửi ảnh cho Săn Ship hỗ trợ nhé.`
                    } : MESSAGE.SYSTEM.ERROR
                });
            }
        });
    },

    getCommentsByFeedID(req, res) {
        const feed_id = _.get(req, 'body.feed_id', '');
        if(!feed_id) {
            return res.json({
                code : CONSTANTS.CODE.WRONG_PARAMS,
                // message: message.SYSTEM.ERROR
            });
        }

        const options = {
          method: 'POST',
          uri: `${proxyFacebookServerAddr}/api/v1.0/facebook/get-comment`,
          body: {
              feed_id,
          },
          json: true // Automatically stringifies the body to JSON
        };

        request(options, (err, response, result) => {
          if(err || !response || response.statusCode !== 200 || !result) {
            return res.json({
                code : CONSTANTS.CODE.SYSTEM_ERROR
            });
          }

          if(result.code !== CONSTANTS.CODE.SUCCESS) {
            return res.json({
                code : CONSTANTS.CODE.SYSTEM_ERROR
            });
          }

          res.json({
            code: CONSTANTS.CODE.SUCCESS,
            comments: {
              data: result.comments
            }
          })
        });

        // rp(options)
        //   .then((result) => {
        //     console.timeEnd('test1');
        //     if(result.code !== CONSTANTS.CODE.SUCCESS) {
        //       return res.json({
        //           code : CONSTANTS.CODE.SYSTEM_ERROR
        //       });
        //     }
        //
        //     res.json({
        //       code: CONSTANTS.CODE.SUCCESS,
        //       comments: {
        //         data: result.comments
        //       }
        //     })
        //   })
        //   .catch((err) => {
        //     return res.json({
        //       code : CONSTANTS.CODE.SYSTEM_ERROR
        //     });
        //   })
        // getCommentOfFeed(feed_id, (err, comments,searchRealId) => {
        //     if(err) {
        //         return res.json({
        //             code : CONSTANTS.CODE.SYSTEM_ERROR
        //         });
        //     }
        //
        //     return res.json({
        //         code: CONSTANTS.CODE.SUCCESS,
        //         comments: comments
        //     });
        //     // find real id of each author comment
        //     async.waterfall([
        //       (callback)=>{
        //         let countComment =0;
        //         comments && comments.data && comments.data.forEach((current)=>{
        //           redisConnection.get(`facebookId:${current.from.id}`, (err, realId) => {
        //             if(err) {
        //               countComment ++;
        //             }else{
        //               countComment ++;
        //               if (realId) {
        //                 current.from.realId = realId;
        //               }else{
        //                 // not have => publish to SearchIDBot
        //                 if (searchRealId) {
        //                   if (!idQuerySearching[current.from.id]) {
        //                       idQuerySearching[current.from.id] = true;
        //                   }
        //                 }
        //               }
        //             }
        //             if (countComment >= comments.data.length) {
        //               callback(err,null)
        //             }
        //           })
        //         })
        //         if (!comments || !comments.data || !comments.data.length || comments.data.length === 0 ) {
        //           callback(null,null)
        //         }
        //       }
        //     ],(err,result)=>{
        //       if(err) {
        //         return res.json({
        //             code : CONSTANTS.CODE.SYSTEM_ERROR
        //         });
        //       }else{
        //         res.json({
        //             code: CONSTANTS.CODE.SUCCESS,
        //             comments: comments
        //         });
        //       }
        //     })
        //
        // });
    }
}

function getCommentOfFeed(feed_id, callback) {
    let access_token = _.sample(commentTokenArr);
    let searchRealId = false;
    let tempArray = feed_id ? feed_id.split('_'):[];
    // if (tempArray.length === 2 && tempArray[0]==='628810487239665') {
    //   searchRealId=false;
    // }
    let FB_API_URI = `${auth.facebook.FB_API}/${feed_id}/comments?fields=message,from,created_time,comments&access_token=${access_token}`;

    request(FB_API_URI, (error, response, body) => {

        let data = body && typeof body === 'string' ?  JSON.parse(body) : {};

        if(data.error) {
            callback(data.error.message, null);
        } else if (!error && response.statusCode == 200) {
            callback(null, data,searchRealId);
        } else {
            callback('error', null);
        }

    });
}
