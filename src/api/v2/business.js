import _ from 'lodash';
import async from 'async';
import util from 'util';
import fs from 'fs.extra'
import {amazonS3Addr, notifyServiceAddr, locationServerAddr, service, orderType, orderTypeGroup} from '../../config';
import CONSTANTS from '../../const';
import MESSAGE from '../../message';
import uuid from 'uuid/v4';
import rp from 'request-promise'
var Util = require('../../Util');
import * as mailUtil from '../../mailUtil';
const ms = require('ms')

// import geoip from 'geoip-lite';

export default {
    getUserBusinessModel(req, res) {

      const userId = req.user.id
      let businessName = ''
      const checkParams = (next) => {
        if(!userId) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }
        next();
      }

      const getUserModel = (next) => {
        BusinessReport
          .find({
            member: userId
          })
          .sort('-createdAt')
          .limit(1)
          .populate('business','name')
          .select('business')
          .lean()
          .exec((err, results) => {
            if(err) {
              return next(err)
            }
            if(results.length) {
              businessName = results[0].business && results[0].business.name
            }
            next(null,{
              code: CONSTANTS.CODE.SUCCESS,
              data: businessName
            });
          })
      }

      async.waterfall([
        checkParams,
        getUserModel
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    list(req, res) {

      const userId = req.user.id
      let business = ''
      const checkParams = (next) => {
        if(!userId) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }
        next();
      }

      const getUserModel = (next) => {
        BusinessReport
          .find({
            member: userId
          })
          .sort('-createdAt')
          .limit(1)
          .select('business')
          .lean()
          .exec((err, results) => {
            if(err) {
              return next(err)
            }
            if(results.length) {
              business = results[0].business
            }
            next();
          })
      }

      const listModel = (next) => {
        BusinessModel
          .find()
          .lean()
          .exec((err, results) => {
            if(err) {
              return next({
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: MESSAGE.SYSTEM_ERROR
              })
            }
            results.forEach((item, i) => {
              if(business && item._id.toString() === business.toString()) {
                item.active = 1
              }
            });

            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: results
            })
          })
      }

      async.waterfall([
        checkParams,
        getUserModel,
        listModel
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    listBusinessItemByModel(req,res) {

      const userId = req.user.id
      const businessModel = req.body.id
      let userItem;

      const checkParams = (next) => {
        if(!userId || !businessModel) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }
        next();
      }

      const getUserItems = (next) => {
        BusinessReport
          .find({
            member: userId
          })
          .sort('-createdAt')
          .limit(1)
          .select('report business')
          .lean()
          .exec((err, results) => {
            if(err) {
              return next(err)
            }
            if(results.length && results[0].business.toString() === businessModel) {
              userItem = results[0].report
            }
            next();
          })
      }

      const listItems = (next) => {

        BusinessItem
          .find({
            business: businessModel
          })
          .select('-business')
          .sort('order')
          .lean()
          .exec((err, results) => {
            if(err) {
              return next(err)
            }

            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: results,
              userItem
            })
          })
      }
      async.waterfall([
        checkParams,
        getUserItems,
        listItems
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    sendBusinessReport(req,res) {

      const userId = req.user.id
      const businessModel = req.body.businessModel
      const report = req.body.report
      let items = []
      Object.keys(report).forEach((item, i) => {
        items.push(item)
      });

      const checkParams = (next) => {
        if(!userId || !businessModel || !report || !items.length) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }
        next();
      }

      const updateOrCreateReport = (next) => {
        BusinessReport
          .findOneAndUpdate({
            member: userId
          },{
            member: userId,
            business: businessModel,
            report,
            items,
            updatedAt: Date.now()
          },{
            new: true,
            upsert: true,
            setDefaultsOnInsert: true
          },(err, results) => {
            if(err) {
              return next(err)
            }
            next()
          })
      }

      const saveReport = (next) => {

        BusinessReportLog
          .create({
            member: userId,
            business: businessModel,
            report,
            items
          },(err, results) => {
            if(err) {
              return next(err)
            }
            next(null, {
              code: CONSTANTS.CODE.SUCCESS
            })
          })
      }
      async.waterfall([
        checkParams,
        updateOrCreateReport,
        saveReport
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    }
}
