import { Router } from 'express';
import _ from 'lodash';
const router = new Router();
const async = require('async')
import * as helpers from '../../helpers';
import CONSTANTS from '../../const';

export default {
  callback(req,res) {
    if(!req.query || !req.query.loanId
      || !req.query.status || !req.query.message
    ) {
      return res.json({
        code:CONSTANTS.CODE.WRONG_PARAMS
      })
    }
    res.json({
      code:CONSTANTS.CODE.SUCCESS
    })
  }
}
