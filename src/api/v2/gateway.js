import _ from 'lodash';
import async from 'async';
import uuid from 'uuid/v4';
import util from 'util';
import rp from 'request-promise';
import CONSTANTS from '../../const';
import MESSAGE from '../../message';
import {cardServerAddr} from '../../config';

const GATEWAY_CODE = {
  SUCCESS: '00',
  CARD_USED: '07',
  CARD_LOCKED: '08',
  CARD_EXPIRED: '09',
  CARD_NOT_ACTIVE: '10'
}

const BAOKIM_GATEWAY_CODE = {
  SUCCESS: 200,
  SLOWMOTION: 202,
  DATA_ERROR: 450,
  GATEWAY_ERROR: 460
}

const MAPPING = {
  viettel: 'VIETEL',
  vinaphone: 'VINA',
  mobifone: 'MOBI',
  VNM: 'VNM',
  GATE: 'GATE'
}

const MAPPING_NGANLUONG = {
  viettel: 'VIETTEL',
  vinaphone: 'VNP',
  mobifone: 'VMS',
  GATE: 'GATE'
}

const MAPPING_APPOTA = {
  viettel: 'viettel',
  vinaphone: 'vinaphone',
  mobifone: 'mobifone'
}

export default {
  getConfig(req, res) {
    return res.json({
      code: 200,
      data: {
        cards: [
          // {
          //   code: 'vinaphone',
          //   label: 'Vinaphone'
          // },
          // {
          //   code: 'viettel',
          //   label: 'Viettel'
          // },
          // {
          //   code: 'mobifone',
          //   label: 'Mobifone'
          // }
        ],
        defaultCardType: "viettel",
        isOpenBanking: 1,
        isOpenVimo: 1,
        isOpenMomo: 1,
        isOpenSMS: 1
      }
    })
  },
  getConfigForCard(req, res) {
    return res.json({
      code: 200,
      data: {
        discount: 20,
        needChoosePrice: 1,
        priceList: [
          // {
          //   code: 10000,
          //   label: "10.000"
          // },
          {
            code: 20000,
            label: "20.000"
          },
          {
            code: 30000,
            label: "30.000"
          },
          {
            code: 50000,
            label: "50.000"
          },
          {
            code: 100000,
            label: "100.000"
          },
          {
            code: 200000,
            label: "200.000"
          }
        ],
        cards: [
          // {
          //   code: 'vinaphone',
          //   label: 'Vinaphone',
          //   image: "https://vnreview.vn/image/33/40/334017.jpg"
          // }
          // ,
          {
            code: 'viettel',
            label: 'Viettel',
            image: 'https://dichvudidong.vn/wp-content/uploads/2014/11/cach-nhan-tin-nhan-khuyen-mai-viettel.jpg'
          }
          // ,
          // {
          //   code: 'mobifone',
          //   label: 'Mobifone',
          //   image: 'http://images1.cafef.vn/Images/Uploaded/DuLieuDownload/LogoCorpLarge/MOBIFONE.png'
          // }
        ],
        defaultCardType: ''
      }
    })
  },
  cardChargingRedpay(req, res) {
    const userId = req.user.id;
    const pin = _.get(req, 'body.pin', '');
    const serial = _.get(req, 'body.serial', '');
    const type = _.get(req, 'body.type', '');
    const amount = _.get(req, 'body.amount', 0);

    const checkParams = (next) => {
      if(!pin || !serial || !type || !amount) {
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGE.SYSTEM.ERROR
        });
      }

      next(null);
    }

    const makeRequest = (next) => {
      const options = {
        method: 'POST',
        uri: `${cardServerAddr}/api/v1.0/payment/charge`,
        body: {
            userId: userId,
            pin: pin,
            serial: serial,
            type: type,
            amount: amount
        },
        json: true // Automatically stringifies the body to JSON
      };

      request(options, (err, response, result) => {
        if(err || !response || response.statusCode !== 200 || !result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGE.SYSTEM.ERROR
          })
        }

        next(null, result);
      })
    }

    async.waterfall([
      checkParams,
      makeRequest
    ], (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGE.SYSTEM.ERROR
      });

      res.json(data || err);
    });
  },
  cardChargingAppota(req, res) {
    const pin = _.get(req, 'body.pin', '');
    const serial = _.get(req, 'body.serial', '');
    const isDonate = _.get(req, 'body.isDonate', '');

    let type = _.get(req, 'body.type', '');
    type = MAPPING_APPOTA[type];

    let resultCard = {};

    const checkParams = (next) => {
      if(!pin || !serial || !type) {
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGE.SYSTEM.ERROR
        });
      }

      next(null);
    }

    const cardCharging = (next) => {
      next({
        code: CONSTANTS.CODE.FAIL,
        message: "Not supported"
      })
      // appotaGateway.cardCharging(type, pin, serial, (err, result) => {
      //   if(err) {
      //     return next(err);
      //   }
      //
      //   resultCard = result;
      //   next(null);
      // });
    }

    const handleResultGateway = (next) => {
      if(resultCard.error_code === 0) {
        let amount = _.get(resultCard, 'data.amount', 0);

        Members.increaseCoint(req.user.id, amount, (err, resultUpdate) => {
          if(err) {
            return next(err);
          }

          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
              head: 'Thông báo',
              body: isDonate ? `Cảm ơn bạn đã tạo thêm động lực cho bên mình để tiếp tục phát triển, hoàn thiện hơn nữa. Chúc bạn và gia đình luôn luôn mạnh khoẻ, hạnh phúc và gặp nhiều may mắn trong cuộc sống. Đội ngũ Săn Ship!` : `Nạp thẻ thành công mệnh giá ${amount} VND. Vui lòng kiểm tra lại số coint, nếu có vấn đề gì vui lòng liên hệ trực tiếp với chúng tôi. Xin cảm ơn.`
            },
            coints: resultUpdate.coints
          });
        });
      } else {
        let message = {
          head: 'Thông báo',
          body: _.get(resultCard, 'message', 'Đã có lỗi xảy ra vui lòng thử lại.')
        }

        next(null, {
          code: CONSTANTS.CODE.FAIL,
          message
        });
      }
    }

    async.waterfall([
      checkParams,
      cardCharging,
      handleResultGateway
    ], (err, data) => {
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGE.SYSTEM.ERROR
      });

      res.json(data || err);

      // Write logs
      const obj = {
        pin,
        serial,
        type,
        member: req.user.id,
        createdAt: Date.now(),
        error: _.isError(err) ? util.inspect(err) : undefined,
        resultCard
      }

      CardLogs.create(obj, (err, result) => {
        if(!err) {
          redisConnection.publish('cardCharging', result._id.toHexString());
        }
      });
    })
  },
  cardChargingNganLuong(req, res) {
    const pin = _.get(req, 'body.pin', '');
    const serial = _.get(req, 'body.serial', '');
    let type = _.get(req, 'body.type', '');
    type = MAPPING_NGANLUONG[type];

    let resultCard = {};

    const checkParams = (next) => {
      if(!pin || !serial || !type) {
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGE.SYSTEM.ERROR
        });
      }

      next(null);
    }

    const cardCharging = (next) => {
      next({
        code: CONSTANTS.CODE.FAIL,
        message: "Not supported"
      })
      // nlGateway.cardCharging(pin, serial, type, (err, result) => {
      //   if(err) {
      //     return next(err);
      //   }
      //
      //   resultCard = {
      //     data: {
      //       amount: parseInt(result.card_amount),
      //       errorMessage: nlGateway.transferErrorMessage(result.error_code),
      //       amount: result.card_amount,
      //       transaction_id: result.transaction_id
      //     },
      //     status: result.error_code === nlGateway.CODE.SUCCESS ? 200 : result.error_code,
      //     transaction_id: result.transaction_id
      //   };
      //   next(null);
      // });
    }

    const handleResultGateway = (next) => {
      // if(resultCard.status === 200) {
      //   let amount = _.get(resultCard, 'data.amount', 0);
      //
      //   Members.increaseCoint(req.user.id, amount, (err, resultUpdate) => {
      //     if(err) {
      //       return next(err);
      //     }
      //
      //     return next(null, {
      //       code: CONSTANTS.CODE.SUCCESS,
      //       message: {
      //         head: 'Thông báo',
      //         body: `Nạp thẻ thành công mệnh giá ${amount} VND. Vui lòng kiểm tra lại số coint, nếu có vấn đề gì vui lòng liên hệ trực tiếp với chúng tôi. Xin cảm ơn.`
      //       },
      //       coints: resultUpdate.coints
      //     });
      //   });
      // } else {
      //   let messageFromNL = nlGateway.transferErrorMessage(resultCard.error_code);
      //   if(messageFromNL.search(/(NgânLượng.vn|merchant|Telco)/i) !== -1) {
      //     messageFromNL = 'Đã có lỗi xảy ra vui lòng thử lại'
      //   }
      //   let message = {
      //     head: 'Thông báo',
      //     body: _.get(resultCard, 'data.errorMessage', '')
      //   }
      //
      //   next(null, {
      //     code: CONSTANTS.CODE.FAIL,
      //     message
      //   });
      // }
    }

    async.waterfall([
      checkParams,
      cardCharging,
      handleResultGateway
    ], (err, data) => {
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGE.SYSTEM.ERROR
      });

      res.json(data || err);

      // Write logs
      const obj = {
        pin,
        serial,
        type,
        member: req.user.id,
        createdAt: Date.now(),
        error: _.isError(err) ? util.inspect(err) : undefined,
        resultCard
      }

      CardLogs.create(obj, (err, result) => {
        if(!err) {
          redisConnection.publish('cardCharging', result._id.toHexString());
        }
      });
    })
  },
  cardCharging(req, res) {
    const pin = _.get(req, 'body.pin', '');
    const serial = _.get(req, 'body.serial', '');
    const isDonate = _.get(req, 'body.isDonate', '');

    let type = _.get(req, 'body.type', '');
    type = MAPPING[type];

    let resultCard = {};

    const checkParams = (next) => {
      if(!pin || !serial || !type) {
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGE.SYSTEM.ERROR
        });
      }

      next(null);
    }

    const cardCharging = (next) => {
      next({
        code: CONSTANTS.CODE.FAIL,
        message: "Not supported"
      })
      // bkGateway.cardCharging(pin, serial, type, (err, result) => {
      //   if(err) {
      //     return next(err);
      //   }
      //
      //   resultCard = result;
      //   next(null);
      // });
    }

    const handleResultGateway = (next) => {
      if(resultCard.status === BAOKIM_GATEWAY_CODE.SUCCESS) {
        let amount = _.get(resultCard, 'data.amount', 0);

        Members.increaseCoint(req.user.id, amount, (err, resultUpdate) => {
          if(err) {
            return next(err);
          }

          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
              head: 'Thông báo',
              body: isDonate ? `Cảm ơn bạn đã tạo thêm động lực cho bên mình để tiếp tục phát triển, hoàn thiện hơn nữa. Chúc bạn và gia đình luôn luôn mạnh khoẻ, hạnh phúc và gặp nhiều may mắn trong cuộc sống. Đội ngũ Săn Ship!` : `Nạp thẻ thành công mệnh giá ${amount} VND. Vui lòng kiểm tra lại số coint, nếu có vấn đề gì vui lòng liên hệ trực tiếp với chúng tôi. Xin cảm ơn.`
            },
            coints: resultUpdate.coints
          });
        });
      } else {
        let message = {
          head: 'Thông báo',
          body: _.get(resultCard, 'data.errorMessage', '')
        }

        next(null, {
          code: CONSTANTS.CODE.FAIL,
          message
        });
      }
    }

    async.waterfall([
      checkParams,
      cardCharging,
      handleResultGateway
    ], (err, data) => {
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGE.SYSTEM.ERROR
      });

      res.json(data || err);

      // Write logs
      const obj = {
        pin,
        serial,
        type,
        member: req.user.id,
        createdAt: Date.now(),
        error: _.isError(err) ? util.inspect(err) : undefined,
        resultCard
      }

      CardLogs.create(obj, (err, result) => {
        if(!err) {
          redisConnection.publish('cardCharging', result._id.toHexString());
        }
      });
    })
  },
  cardCharging1Pay(req, res) {
    const pin = _.get(req, 'body.pin', '');
    const serial = _.get(req, 'body.serial', '');
    const type = _.get(req, 'body.type', '');

    let resultCard = {};

    const checkParams = (next) => {
      if(!pin || !serial || !type) {
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGE.SYSTEM.ERROR
        });
      }

      next(null);
    }

    const cardCharging = (next) => {
      next({
        code: CONSTANTS.CODE.FAIL,
        message: "Not supported"
      })
      // gatewayCardCharging.cardCharging(type, pin, serial, (err, result) => {
      //   if(err) {
      //     return next(err);
      //   }
      //
      //   resultCard = result;
      //   next(null);
      // });
    }

    const handleResultGateway = (next) => {
      if(resultCard.status === GATEWAY_CODE.SUCCESS) {
        Members.increaseCoint(req.user.id, resultCard.amount, (err, resultUpdate) => {
          if(err) {
            return next(err);
          }

          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
              head: 'Thông báo',
              body: `Nạp thẻ thành công mệnh giá ${resultCard.amount} VND. Vui lòng kiểm tra lại số coint, nếu có vấn đề gì vui lòng liên hệ trực tiếp với chúng tôi. Xin cảm ơn.`
            },
            coints: resultUpdate.coints
          });
        });
      } else {
        let message;
        switch(resultCard.status) {
          case GATEWAY_CODE.CARD_USED:
          message = MESSAGE.CARD.CARD_USED
          break;
          case GATEWAY_CODE.CARD_LOCKED:
          message = MESSAGE.CARD.CARD_LOCKED
          break;
          case GATEWAY_CODE.CARD_EXPIRED:
          message = MESSAGE.CARD.CARD_EXPIRED
          break;
          case GATEWAY_CODE.CARD_NOT_ACTIVE:
          message = MESSAGE.CARD.CARD_NOT_ACTIVE
          break;
          default:
          message = MESSAGE.CARD.COMMON
          break;
        }

        next(null, {
          code: CONSTANTS.CODE.FAIL,
          message
        });
      }
    }

    async.waterfall([
      checkParams,
      cardCharging,
      handleResultGateway
    ], (err, data) => {
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGE.SYSTEM.ERROR
      });

      res.json(data || err);

      // Write logs
      const obj = {
        pin,
        serial,
        type,
        member: req.user.id,
        createdAt: Date.now(),
        error: _.isError(err) ? util.inspect(err) : undefined,
        resultCard
      }

      CardLogs.create(obj, (err, result) => {
        if(!err) {
          redisConnection.publish('cardCharging', result._id.toHexString());
        }
      });
    })

  },

  bankCharging(req, res) {
    res.json({
      code: CONSTANTS.CODE.FAIL,
      message: "Not supported"
    })
    // const amount = _.toSafeInteger(req.body.amount);
    // const userId = req.user.id;
    //
    // const options = {
    //   method: 'POST',
    //   uri: `http://103.63.109.105:1972/api/v2.0/gateway/bank-begin`,
    //   body: {
    //     userId,
    //     amount
    //   },
    //   json: true
    // };
    //
    // rp(options)
    //   .then((result) => {
    //     return res.json(result);
    //   })
    //   .catch((err) => {
    //     return res.json({
    //       code: CONSTANTS.CODE.SYSTEM_ERROR,
    //       message: MESSAGE.SYSTEM.ERROR
    //     })
    //   })
    //
    // return;
    //
    //
    // let order_id;
    // let order_info;
    // let resultGateway;
    //
    // const checkParams = (next) => {
    //   if(amount < 10000) {
    //     return next(new Error('Money is smaller than 10000'));
    //   }
    //
    //   next(null);
    // }
    //
    // const getRedirectURL = (next) => {
    //   order_id = uuid();
    //   order_info = `SanShip`;
    //   gatewayBankCharging.beginTransaction(amount, order_id, order_info, (err, result) => {
    //     if(err) {
    //       return next(err);
    //     }
    //     if(  (_.get(result, 'status', '') !== 'init')
    //     || !_.get(result, 'trans_ref', '')
    //     || !_.get(result, 'pay_url', '')) {
    //       return next(new Error('Gateway error when get redirect URL'));
    //     }
    //
    //     resultGateway = result;
    //     next(null);
    //   });
    // }
    //
    // const writeLog = (next) => {
    //   const obj = {
    //     order_id,
    //     amount,
    //     member: req.user.id,
    //     transRef: resultGateway.trans_ref,
    //     createdAt: Date.now(),
    //     status: 0
    //   }
    //
    //   BankingLog.create(obj, (err, result) => {
    //     if(err) {
    //       return next(err);
    //     }
    //
    //     const data = {
    //       code: CONSTANTS.CODE.SUCCESS,
    //       pay_url: resultGateway.pay_url
    //     }
    //
    //     next(null, data);
    //   });
    // }
    //
    // async.waterfall([
    //   checkParams,
    //   getRedirectURL,
    //   writeLog
    // ], (err, data) => {
    //   console.log(err);
    //   err && _.isError(err) && (data = {
    //     code: CONSTANTS.CODE.SYSTEM_ERROR,
    //     message: MESSAGE.SYSTEM.ERROR
    //   });
    //
    //   res.json(data || err);
    // });
  },

  bankReturn(req, res) {
    res.json({
      code: CONSTANTS.CODE.FAIL,
      message: "Not supported"
    })
    // let bonus = 10;
    // let transInf; // include member
    //
    // const checkCondition = (next) => {
    //   if(!gatewayBankCharging.checkSecureResponse(req.query)) {
    //     return next('Not a valid return from gateway');
    //   }
    //
    //   if(req.query.response_code !== GATEWAY_CODE.SUCCESS) {
    //     return next('Trans not success');
    //   }
    //
    //   next();
    // }
    //
    // const getTransAndMemberInf = (next) => {
    //   BankingLog
    //   .findOneAndUpdate({
    //     order_id: req.query.order_id,
    //     transRef: req.query.trans_ref,
    //     amount: _.toSafeInteger(req.query.amount),
    //     status: 0
    //   }, {
    //     $inc: {
    //       status: 1
    //     }
    //   })
    //   .populate('member', 'coints _id')
    //   .lean()
    //   .exec((err, result) => {
    //     if(err) {
    //       return next(err);
    //     }
    //
    //     if(!result) {
    //       return next('Not found trans info in db or maybe trans has been processed');
    //     }
    //
    //     if(!result.member) {
    //       return next('Not found member');
    //     }
    //
    //     transInf = result;
    //     next();
    //   });
    // }
    //
    // const updateUserInf = (next) => {
    //   const amount = _.toSafeInteger(req.query.amount);
    //   const moreCoints = Math.floor(amount + amount*bonus/100);
    //   Members.increaseCoint(transInf.member._id, moreCoints, (err, resultUpdate) => {
    //     if(err) {
    //       return next(err)
    //     }
    //
    //     next(null, resultUpdate);
    //   })
    // }
    //
    // async.waterfall([
    //   checkCondition,
    //   getTransAndMemberInf,
    //   updateUserInf
    // ], (err, userInfUpdated) => {
    //   const update = {
    //     resultBank: req.query
    //   };
    //
    //   if(err) {
    //     if(!transInf) {
    //       update['$inc'] = {
    //         status: 1
    //       }
    //     }
    //
    //     update.error = util.inspect(err);
    //     res.redirect('https://heyu.asia/banking-error');
    //   } else {
    //     update.initialCoints = transInf.member.coints;
    //     update.finalCoints = userInfUpdated.coints;
    //     res.redirect(`https://heyu.asia/banking-success?finalCoints=${userInfUpdated.coints}`);
    //   }
    //
    //   BankingLog
    //   .update({
    //     order_id: req.query.order_id,
    //     transRef: req.query.trans_ref
    //   }, update)
    //   .exec((err, result) => {
    //
    //   })
    // })
  }
}
