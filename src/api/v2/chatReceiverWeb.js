import _ from 'lodash';
import CONSTANTS from '../../const';
import MESSAGES from '../../message';
import * as async from 'async';

export default {
    list(req, res) {
      const userId = req.user.id;
      const from = _.toSafeInteger(req.body.from) || (Date.now() + 100000);
      const limit = _.clamp(_.toSafeInteger(req.body.limit), 5, 100);

      ConversationReceivers
        .find({
          users: userId,
          updatedAt: {
            '$lt': from
          }
        })
        .populate("users", "facebook.name facebook.picture _id")
        .populate("latestMessage")
        .sort("-updatedAt")
        .limit(limit)
        .lean()
        .exec((err, results) => {
          if(err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            });
          }

          results.forEach((result) => {
            for(let i=0; i<result.users.length; i++) {
              if(result.users[i]._id.toHexString() === userId) {
                result.users = _.cloneDeep(result.users);
                result.users.splice(i, 1);
                break;
              }
            }
          });

          res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: results
          });
        });
    },
    getConversationInf(req, res) {
      const conversationId = req.body.id;

      let conversation;
      const getConversation = (next) => {
        ConversationReceivers
        .findById(conversationId)
        .populate("latestMessage")
        .lean()
        .exec((err, result) => {
          if(err || !result) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            });
          }

          conversation = result
          next();
        })
      }

      const findUserInf = (next) => {
 
        let users = []
        async.eachSeries(conversation.users, (user, done) => {
          Members
          .findOne({
            _id: user
          })
          .select('facebook')
          .lean()
          .exec((err, result) => {
            if(result) {
              user = result
              users.push(user)
            } 
            done();
          })
        },(error) => {
          conversation.users = users
          next(null,{
            code: CONSTANTS.CODE.SUCCESS,
            data: conversation
          });
        })
  
      }

      async.waterfall([
        getConversation,
        findUserInf
      ], (err, data) => {
        console.log('ahihi getConversationInf',err,data)
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
        });

        res.json(data || err);
      });
    },
    getMessagesByConversation(req, res) {
      const from = _.toSafeInteger(req.body.from) || (Date.now() + 100000);
      const limit = _.clamp(_.toSafeInteger(req.body.limit), 10, 100);
      const conversation = req.body.conversation;
      const idOrder = req.body.idOrder;

      MessageReceivers
        .find({
          conversation: conversation,
          // idOrder,
          createdAt: {
            $lt: from
          }
        }, "-conversation")
        .sort("-createdAt")
        .limit(limit)
        .lean()
        .exec((err, results) => {
          if(err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            });
          }

          res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              messages: results
            }
          })
        });
    },
    getMessagesByUser(req, res) {
      const from = _.toSafeInteger(req.body.from) || (Date.now() + 100000);
      const limit = _.clamp(_.toSafeInteger(req.body.limit), 10, 100);
      const orderId = req.body.orderId;
      const sequence = req.body.sequence;
      const userId = orderId+sequence;

      !Array.isArray(req.body.listUserInGroup) && (req.body.listUserInGroup = []);
      const listUserInGroup = req.body.listUserInGroup;
      let conversation;

      const checkParams = (next) => {
        if(!listUserInGroup.length) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.SYSTEM.ERROR
          });
        }

        next();
      }

      const getConversation = (next) => {
        listUserInGroup.push(userId);

        ConversationReceivers
          .findOne({
            users: {
              '$all': listUserInGroup
            }
          }, "-name -isGroup")
          .lean()
          .exec((err, result) => {
            if(err) {
              return next(err);
            }

            if(!result) {
              return next({
                code: CONSTANTS.CODE.SUCCESS,
                data: result
              })
            }

            // Remove info of current user request
            const usersInfo = result.users;
            for(let i=0; i<usersInfo.length; i++) {
              if(usersInfo[i] === userId) {
                usersInfo.splice(i, 1);
                break;
              }
            }

            conversation = result;
            next();
          });
      }

      const findUserInf = (next) => {
        if(!conversation) {
          return next();
        }
        let users = []
        async.eachSeries(conversation.users, (user, done) => {
          Members
          .findOne({
            _id: user
          })
          .select('facebook')
          .lean()
          .exec((err, result) => {
            if(result) {
              user = result
            } else {
              user = {
                _id: user,
                facebook: {
                  name:  `Người nhận ${user[user.length-1]}`
                }
              }
            }
            users.push(user)
            done();
          })
        },(error) => {
          conversation.users = users
          next();
        })
  
      }

      const getMessages = (next) => {
        MessageReceivers
          .find({
            conversation: conversation._id,
            // idOrder,
            createdAt: {
              $lt: from
            }
          }, "-conversation")
          .sort("-createdAt")
          .limit(limit)
          .lean()
          .exec((err, results) => {
            if(err) {
              return next(err);
            }

            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                conversation,
                messages: results
              }
            })
          });
      }

      async.waterfall([
        checkParams,
        getConversation,
        findUserInf,
        getMessages
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
        });

        res.json(data || err);
      });
    },
    getMessagesByListUser(req, res) {
      const from = _.toSafeInteger(req.body.from) || (Date.now() + 100000);
      const limit = _.clamp(_.toSafeInteger(req.body.limit), 10, 100);
      !Array.isArray(req.body.listUserInGroup) && (req.body.listUserInGroup = []);
      const listUserInGroup = req.body.listUserInGroup;
      let conversation;

      const checkParams = (next) => {
        if(!listUserInGroup.length) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.SYSTEM.ERROR
          });
        }

        next();
      }

      const getConversation = (next) => {
        ConversationReceivers
          .findOne({
            users: {
              '$all': listUserInGroup
            }
          }, "-name -isGroup -latestMessage -createdAt -updatedAt")
          .lean()
          .exec((err, result) => {
            if(err) {
              return next(err);
            }

            if(!result) {
              return next({
                code: CONSTANTS.CODE.SUCCESS,
                data: result
              })
            }

            conversation = result;
            next();
          });
      }

      const getMessages = (next) => {
        MessageReceivers
          .find({
            conversation: conversation._id,
            createdAt: {
              $lt: from
            }
          }, "-conversation")
          .sort("createdAt")
          .limit(limit)
          .lean()
          .exec((err, results) => {
            if(err) {
              return next(err);
            }

            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: results
            })
          });
      }

      async.waterfall([
        checkParams,
        getConversation,
        getMessages
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
        });

        res.json(data || err);
      });
    },
    getNewOrderMessage(req, res) {

      const receiverId = req.body.receiverId || [];
      const orderId = req.body.orderId || ''
      const sequence = req.body.sequence || ''
      let userId
      
      let conversationResult = [];

      const checkParams = (next) => {
        if(!orderId || isNaN(sequence) || receiverId.length === 0) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          });
        }
        userId = orderId + sequence;
        next();
      }

      const findConversationReceivers = (next) => {
        let queryArray = []
        receiverId.forEach((rId) => {
          queryArray.push([
            userId,rId
          ])
        })
        const arrayQuery = []
        queryArray.forEach((query) => {
          arrayQuery.push({
            users:{$all:query}
          })
        })
        ConversationReceivers.find({
          $or:arrayQuery
        })
        .lean()
        .select({
          latestMessage:1
        })
        .exec((err, result) => {
          if(err) {
            return next(err);
          }
          conversationResult = result;
          if(conversationResult.length === 0) {
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              data: []
            });
          }
          next();
        })
      }

      const findNewMessage = (next) => {

        let queryArray = [];
        conversationResult.forEach((conversation) => {
          queryArray.push({
            _id: conversation.latestMessage,
          })
        })
        MessageReceivers.find({
          seen: 0,
          $or:queryArray,
          senderId: { $ne:userId }
        })
        .lean()
        .exec((err,result) => {
          if(err) {
            return next(err);
          }

          if(result.length === 0) {
            return next({
              code: CONSTANTS.CODE.SUCCESS,
              data: []
            })
          }
          let senderArray = [];
          result.forEach((message) => {
            senderArray.push(message.senderId)
          })
          next({
            code: CONSTANTS.CODE.SUCCESS,
            data: senderArray
          })
        })
      }

      async.waterfall([
        checkParams,
        findConversationReceivers,
        findNewMessage
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    }
}
