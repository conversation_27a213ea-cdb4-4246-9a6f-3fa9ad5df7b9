// import * as dotenv from 'dotenv'
// dotenv.config();

export const port = process.env.PORT || 8000;
export const host = process.env.WEBSITE_HOSTNAME || `localhost:${port}`;
export const SANSHIP_FB_APPID = '1732474927007355';
export const SANSHIP_HEYU_FB_APPID = '1913555415398264';

/**** Development ****/
// export const MONGO_DB = process.env.MONGO_DB || 'mongodb://127.0.0.1:27017/shipping-management';
// export const REDIS_DB = {
//     "host": "127.0.0.1",
//     "port": 6379,
//     "database": 0
// }
/**** production ****/
export const MONGO_DB = process.env.MONGO_DB || 'mongodb://************:27227/shipping-management';
export const MONGO_DB_OPTIONS = {
	user: '',
	pass: ''
}

export const mongo = {
	"connections": {
		"master": {
			"host": "************",
			"port": 27227,
			"database": "shipping-management",
			"options": {
				"user": "",
				"pass": ""
			}
		},
		"sub": {
			"host": "************",
			"port": 27227,
			"database": "shipping-management",
			"options": {
				"user": "",
				"pass": ""
			}
		},
		"trackingAction": {
			"host": "************",
			"port": 27227,
			"database": "shipping-management",
			"options": {
				"user": "",
				"pass": ""
			}
		},
		"cms": {
			"host": "************",
			"port": 27227,
			"database": "shipping-management",
			"options": {
				"user": "",
				"pass": ""
			}
		}
	}
}

export const REDIS_DB = {
	"host": "************",
	"port": 6379,
	"database": 0,
	"password": "SuperShip@SanShip@123!@#2112"
}
export const auth = {

  jwt: { secret: process.env.JWT_SECRET || 'Shipping Management' },

  // https://developers.facebook.com/
  facebook: {
      FB_API: 'https://graph.facebook.com/v2.3'
  }

};

export const service = {
	bike: ["5d7717128b162864317d4980","5db9c9a934033834fda81e9d"],
	order: ['5d4cea1268731c9493253a91', '5df993f87709dc2a56912b09'],
  car: ['5d7717128b162864317d4981'],
  lend: ['5e0eb4f0d084b25ee5ce058c'],
  food: ['5d4cea5468731c9493253bb9', '5ecb8ef5e5cc236f417bae25'],
  errand: ['5e8d575ded99aa22acde13e2', '5ecb90e2e5cc236f417bae2a'],
	hub: ['6096369fa9b6fb64b4b7fddd'],
	hireDriver: ['6603cfa4786b6014fdb90f63'],
	heyCare: ['663499f98a901e3737c2f22c'],
	heyClean: ['66a0771147950f7c9da8f1be'],
	"transfer": ['6603ce35786b6014fdb90f5b']
};

export const orderType = {
	"bike": ["5dce26cdbc5b9d29bb3767ef", "5ecc05fae5cc236f417bae9e", "5f07d295e1baaf2b3f691242", "60405db4bba6896707dd8e70","60405db4bba6896707dd8e70","607e35bda9b6fb64b4b7e92a"],
	"carFour": ["5eec325eed3b2a35431dee17", "5f07d2e7e1baaf2b3f691246","611253fb20dde52d9edf400d"],
	"carSeven": ["5f10030339a2f3417fa0b86e", "5f279c782377c11a0b08bcdf","6112543020dde52d9edf400e"],
	"hireDriver": ['6603ce35786b6014fdb90f5b'],
	"transfer": ['6603ce35786b6014fdb90f5b']
};

export const shopTransactionType = [11,18,25,26,33, 38, 39, 49, 56, 65, 69]
export const tickTransactionType = [29,30,31, 32,34,35,36]
export const staffTransactionType = [57, 58, 59, 60, 61, 62, 63, 64, 66, 67, 68]

export const orderTypeGroup = {
  "common": ["594b351ed177170872f2b257","5d3a9fa233a5080b994bac23","5ecc0578e5cc236f417bae9b","5f07d243e1baaf2b3f69123f", "60405c6abba6896707dd8e6c", "60405c6abba6896707dd8e6c","607e3581a9b6fb64b4b7e927","60a8908804302079b89a1706"],
  "save":["594b3571d177170872f2b258","5ecc0598e5cc236f417bae9c","5f07d257e1baaf2b3f691240", "60405c9fbba6896707dd8e6d","60405c9fbba6896707dd8e6d","607e3595a9b6fb64b4b7e928","60a8908804302079b89a1707"],
  "express":["597eb6a08dd7c30dc429ab2d","5d3a9fb933a5080b994bac2a","5ecc05afe5cc236f417bae9d","5f07d27de1baaf2b3f691241", "60405cd0bba6896707dd8e6e","60405cd0bba6896707dd8e6e","607e35aea9b6fb64b4b7e929","60a8908804302079b89a1708"],
  "food":["5bb5bbfbbbfcc61f8bfc46ed","5ed1478f039cfb27ce71ca86","5f07d2c6e1baaf2b3f691244", "604efc6c5be9753c6e44867f","604efc6c5be9753c6e44867f", "607e35eea9b6fb64b4b7e92c","60a8908804302079b89a170b"],
  "co-op":["5cc28f8709f5e7e51472179c"],
  "bike":["5dce26cdbc5b9d29bb3767ef","5ecc05fae5cc236f417bae9e","5f07d295e1baaf2b3f691242","60405db4bba6896707dd8e70","6064ba33d39e2a791053551e","607e35bda9b6fb64b4b7e92a","60a8908804302079b89a1709","616d122881b14b396afe56ad","616d15b181b14b396afe56c2","6178d42681b14b396afe5a99","6201d01259efe71bfe1d9e7c","62258f547e51b21a5a08e45a","622da145af1b773a1355a9a9","6258f0154beec8218b129579","62ce2e316df1da1c078fc8b4","6306360b3d3463199863244f"],
  "car4":["5eec325eed3b2a35431dee17","5f07d2e7e1baaf2b3f691246","611253fb20dde52d9edf400d","616d122881b14b396afe56b1","6178d42681b14b396afe5a9d","6201d01259efe71bfe1d9e80","62ce2e316df1da1c078fc8b8"],
  "car7":["5f10030339a2f3417fa0b86e","5f279c782377c11a0b08bcdf","6112543020dde52d9edf400e","616d122881b14b396afe56b2","6178d42681b14b396afe5a9e","6201d01259efe71bfe1d9e81","62ce2e316df1da1c078fc8b9"],
  "active":["5e4174bc503e8e0a1c38f8c5","5ecc0622e5cc236f417bae9f","5f07d2aae1baaf2b3f691243","5e4174bc503e8e0a1c38f8c5"],
  "errand":["5e9aa7b8ad0e9942f2a6e2f1","5ed148e1039cfb27ce71ca91","5f07d2d6e1baaf2b3f691245", "60405dccbba6896707dd8e71","60405dccbba6896707dd8e71", "607e35d0a9b6fb64b4b7e92b","60a8908804302079b89a170a"],
	"hub":["60a55db69156406d77dbc989","60963a43a9b6fb64b4b7fde4"],
	"hireDriver": ['6603ce35786b6014fdb90f5b'],
	"transfer": ['6603ce35786b6014fdb90f5b']
}


export const environment = 'dev';
export const workhard = '0OASx05M0dEppbcd'
export const listEmailAlert = ['<EMAIL>'];

//Get Comment Tokens
export const comment_access_token_1 = 'EAAC5DqzZBQ5QBAKoVjRWWZCcRSOTvonSvG5T7KduZCDZCqZBp205o8YtTkSNy616rh0IlNNPtbmx9MOAVLrSk3zXznfXqiKZBpFHQaNWI3ndiGcMlA0yj8EK6L1AyvgJaqhmT74qcWt7bXps1pwUUlDxdHDZBQ9pJgZD'; // Tran Hao Nam NhaXinhHaNoi app; <EMAIL>
export const comment_access_token_2 = 'EAAB0KUDdJM8BABgk5bjHoK5ulqia52gkEGjuvPucEgX4N5sZAxJ1n6zpKfA1uXAjgZBMuuYIkiU99RrqZBRH3n7b4J9k7yBjCZAQzvMqOd5spClexlFgYsDaZCaogRbZCzLIH5jmRnlOl2ECwYHOAnUwmC2vtRZCEcZD'; //Son Hai - <EMAIL> (SmartHome App)
export const comment_access_token_3 = 'EAACdg5Yw9ZC4BAK2Iw6QRROBTQIdXGZClalZBnspukGb8Lxd39KpMNdyBqcLbDOqsr4GJyhBs3cuChJTEFUNSlBP2frL3ZAcoNpcn03wN3HnpRznZB8QEuSrWUWrzRHrczlvdXaNFsk3UfUnscx8NK2PsB023eImxzogwwFvZArAZDZD'; // Pham The Anh * - <EMAIL> (NhaXinhHN App)
export const comment_access_token_4 = 'EAAB0KUDdJM8BANZAGd4b8uxGkNh33Dlcpngn7RG90bZA73Hz4Wtaftn4KRpfwbJ4C4s6aylZAApT8RZBbMteGVagrFOQmj4bweDKSKsLK3QNF81U7sEpC7q2j0ueCYsXYT02FLjqUUxHZBt7Wj3OXM8SqBdijy5IZD'; // Nguyen Hang (SmartHome App)
export const comment_access_token_5 = 'EAAC5DqzZBQ5QBAMAoL7k7W6Ej3RZBp9CWil3xLTPZA906ZCgzh7HkNFIIZBH7ZAhApZACath27ZBzqqwnfGBhkTYbv980t6bll64SE5ueMYKh5AKkZA6rUHPJTXZBToGZCjLIc4FNmoA6ZBhBagX2xxqtZBXcZCpZBZAifZA3TxcZD'; // Nguyen Hang (SmartHome App)

export const bonusFirstTime = "30m"
export const maximumPhone = 1

// export const proxyFacebookServerAddr = "http://************:1993"
// export const cardServerAddr = "http://************:1973"
// export const commentServerAddr = "http://************:1993"
// export const currentServerAddr = "https://api.heyu.asia"
// export const authenServiceAddr = "http://***********:1974"
// export const notifyServiceAddr = "http://************:2098"
// export const cmsServerAddr = "http://cms.sanship.info"
// export const cdnServerAddr = "https://s3-ap-southeast-1.amazonaws.com/sanship/shipper"
// export const amazonS3Addr = "http://***********:1978"
// export const codePhoneAddr = "http://************:1989"
// export const mediaServerAddr = "https://media.heyu.asia"
// export const locationServerAddr = "https://location.heyu.asia"
// export const productAddr = "https://stores.heyu.asia"
// export const bookWebAddr = "https://book.heyu.vn"
// export const loggingAddr = "https://logging.heyu.asia"
// export const keyCache = "configrefreshtokens"

export const proxyFacebookServerAddr = "http://*************:1993"
export const cardServerAddr = "http://*************:1973"
export const commentServerAddr = "http://**************:1993"
export const currentServerAddr = "http://************:8000"
export const authenServiceAddr = "http://**************:1974"
export const notifyServiceAddr = "http://************:2098"
export const cmsServerAddr = "http://cms.sanship.info"
export const cdnServerAddr = "https://s3-ap-southeast-1.amazonaws.com/sanship/shipper"
export const amazonS3Addr = "http://**************:1978"
export const codePhoneAddr = "http://************:1989"
export const mediaServerAddr ="http://************:2019"
export const locationServerAddr = "http://************:2009"
export const productAddr = "http://************:2026"
export const bookWebAddr = "https://book-dev.heyu.vn"
export const loggingAddr = "https://logging.heyu.asia"
export const locationHeyCareAddr = "http://************:2090"
export const keyCache = "configrefreshtokensdev"

export const weatherApiKey = "759649159f784e3cbfa33607242112";