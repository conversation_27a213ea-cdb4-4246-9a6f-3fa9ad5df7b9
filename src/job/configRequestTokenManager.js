const _ = require('lodash');
const ms = require('ms');
import {environment} from '../config';

class ConfigRequestTokenManager {
  constructor() {
    this.init();
  }

  init() {
    const configRT = {
      isOpen: false,
      shouldSendMail: true,
      timeoutCheck: 1800000,
      whiteListIP: ["*************","*************","**************","*************","*************","**************","**************","**************","*************","*************","**************","*************","*************","**************","***********","**************","**************","*************","************","*************","************"],
      whiteListUrl: ["/admin/set-config-request-token","/admin/get-config-request-token"],
      blackListIP: []
    }

    global.app.set('configRT',configRT)

    this.syncConfig();
    setInterval(() => {
      this.syncConfig();
    }, ms("5m"))
  }

  syncConfig() {
    let keyCache = `configrequesttokens`;
    if(environment && environment === 'dev') {
      keyCache = `configrequesttokensdev`
    }
    ConfigRequestToken
      .findOne({})
      // .cache(0, keyCache)
      .lean()
      .exec((err, result) => {
        if(!err && result) {
          global.app.set('configRT',result)
        }
      })
  }
}

module.exports = new ConfigRequestTokenManager
