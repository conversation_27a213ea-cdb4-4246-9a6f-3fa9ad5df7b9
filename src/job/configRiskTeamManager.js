const _ = require('lodash');
const ms = require('ms');
import {environment} from '../config';

class ConfigRiskTeamManager {
  constructor() {
    const listUsers = [
      "6125266e36f2e15c85308bb8", // <PERSON><PERSON>
      "625fde2b7057924b4339a37e", // Trung <PERSON>eu
      "61c3dc07ee94ca2c28df8cf0", // <PERSON>uang Hu<PERSON>
      "5b3e10fb152aaf7c4efcf122", // 0987654321
      "62d0e7f409816543c5eb6e03", // <PERSON><PERSON>h
      "5c9add8cb95642140cfdb212", // Thao
      "5b57f22d23f327247ef2b0bb", // 09728692722
      "5bd2793a053fd824b1320157", // Pham The Anh
      "61687985471ff1316ebcdee8", // 0357948523
      "616a793d91113b31602aebc3", // 0975938998
      "5c02186da34f95cc6ab15096", // 1900633689
      "637ed4a325bf0608ee4a0748",  // Duong Huu Lam
    ]
    global.app.set('listUsers', listUsers)
    this.init();
  }

  init() {
    this.syncConfig();
    setInterval(() => {
      this.syncConfig();
    }, ms("5m"))
  }

  syncConfig() {
    ConfigRiskTeam
      .findOne({})
      .lean()
      .exec((err, result) => {
        if(!err && result) {
          global.app.set('listUsers', result.listUsers)
        }
      })
  }
}

module.exports = new ConfigRiskTeamManager
