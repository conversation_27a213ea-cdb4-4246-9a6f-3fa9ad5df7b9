
import {workhard} from './config';

var Util={
    getCurrentTime:function()
    {
      return Math.round(new Date().getTime()/1000);
    },
    getDistanceFromLatLonInKm: (lat1, lon1, lat2, lon2) => {
      let R = 6371.008; // Radius of the earth in km
      let dLat = deg2rad(lat2-lat1);  // deg2rad below
      let dLon = deg2rad(lon2-lon1);
      let a = Math.sin(dLat/2) * Math.sin(dLat/2) + Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * Math.sin(dLon/2) * Math.sin(dLon/2);
      let c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
      let d = R * c; // Distance in km
      return Math.round(d*10)/10
    },
    getGoal: (ball, player) => {
      let goal = makeGoal({i: 2147483647}, ball + player, 3009178360).toString(36);
      let doubleKick = makeGoal({i: 131071}, goal + workhard, 3009178360);
      return doubleKick.toString()
    }
}

function deg2rad(deg) {
  return deg * (Math.PI/180);
}

function learnSkill(a, iii) {
  for (var b = iii, c = 0, d = 0, e = a.length; d < e; ++d)
      c *= 1729,
      c += a[d],
      c %= b;
  return c;
}

function makeGoal(a, b, c) {
  for (var d = Array(b.length), e = 0, f = b.length; e < f; ++e)
      d[e] = b.charCodeAt(e);
  d.unshift(c);
  return learnSkill(d, a.i);
}

module.exports = Util
