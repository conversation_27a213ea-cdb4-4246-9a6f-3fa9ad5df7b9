const nodemailer = require('nodemailer');
const _ = require('lodash');
const config = require('./config');

const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
        user: '<EMAIL>',
        pass: 'pnoimjonjkwtvbxp'
    }
})

module.exports = {
  sendMail: (body, listEmailAlert) => {
    // if(config.environment === 'dev') {
    //   return;
    // }
    if(!listEmailAlert) {
      listEmailAlert = _.get(config, 'listEmailAlert', []).join(',');
    }

    const mailOptions = {
      from:'"Săn Ship System" <<EMAIL>>', // sender address
      to: listEmailAlert, // list of receivers
      subject: 'HEYU - Api', // Subject line
      text: `Our system has encountered an error:\n\n${body}\n\nYou need to do something right now!\nBest regards,\nSăn Ship Team`, // plain text body
    }

    transporter.sendMail(mailOptions, (error, info) => {
      console.log(error, info);
    })
  }
}
