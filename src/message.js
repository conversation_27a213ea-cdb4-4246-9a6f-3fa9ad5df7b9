export default {
	USER: {
		INVALID_OTP: {
			head: 'Thông báo',
			body: '<PERSON><PERSON> xác thực không chính xác. Vui lòng thử lại'
		},
		NOT_ALLOW_TO_CHANGE_PHONE: {
			head: 'Thông báo',
			body: 'Tà<PERSON> khoản của đối tác đã được xác thực. Quý đối tác vui lòng thay đổi số điện thoại tại văn phòng. Số điện thoại muốn đổi phải là số chính chủ của quý đối tác. Xin cảm ơn.'
		},
		MULTIPLE_ACCOUNT: {
			head: 'Thông báo',
			body: 'Số điện thoại bạn vừa nhập đã được đăng ký cho nhiều hơn 1 tài khoản. Vui lòng liên hệ 1900.633.689 để được hỗ trợ. Xin cảm ơn.'
		},
		TIME_TRANSACTION_SSM: {
			head: 'Thông báo',
			body: '<PERSON><PERSON><PERSON> dịch SSM sẽ được thực hiện từ 22:00 đến 9:00 hằng ngày. Kính báo.'
		},
		TRANSFER_SSM_TO_COINT: {
			head: 'Thông báo',
			body: 'Chuyển SSM sang Coints thành công'
		},
		BLACK_DEVICE: {
			head: 'Thông báo',
			body: 'Lỗi nội bộ hệ thống vui lòng thử lại trong giây lát. Rất xin lỗi bạn vì sự cố này. Chúng tôi sẽ khắc phục sớm nhất có thể.'
		},
		TOKEN_EXPIRE: {
			head: 'Thông báo',
			body: 'Phiên làm việc đã hết hoặc do người nào đó đã đăng nhập tài khoản của bạn ở thiết bị khác. Vui lòng đăng nhập lại. Mọi thắc mắc xin vui lòng liên hệ qua hotline 1900.633.689. Xin cảm ơn.'
		},
		BONUS_TIME: {
            head: 'Thông báo',
            body: 'Miễn phí trọn đời cho Shop và Shipper có 30 phút miễn phí để trải nghiệm các tính năng của ứng dụng. Mọi thắc mắc xin vui lòng liên hệ qua hotline 1900.633.689. Xin cảm ơn.'
		},
		EXIST_PHONE: {
			head: 'Thông báo',
			body: 'Số điện thoại bạn muốn thay đổi đã được cập nhật cho tài khoản nào đó. Mọi thắc mắc xin vui lòng liên hệ qua Hotline 1900.633.689. Xin cảm ơn.'
		},
		MAXIMUM_PHONE: {
			head: 'Thông báo',
			body: 'Số điện thoại này đã được cập nhật cho tài khoản nào đó. Hoặc bạn đang sử dụng 1 tài khoản Facebook khác với tài khoản mà bạn đã đăng kí với Săn Ship trước đây. Vui lòng đăng xuất tài khoản Facebook hiện tại ở trình duyệt (gõ facebook.com trên trình duyệt) và đăng xuất tiếp tài khoản hiện tại trên ứng dụng Facebook. Bước tiếp theo đăng nhập bằng đúng nick Facebook mà bạn đã đăng kí với Săn Ship trước kia,sau đó quay lại ứng dụng Săn Ship để thử truy cập lại. Mọi thắc mắc xin vui lòng liên hệ qua hotline 1900.633.689. Xin cảm ơn.'
		},
		MAXIMUM_FACEBOOK: {
			head: 'Thông báo',
			body: 'Tài khoản Facebook bạn vừa liên kết đã từng được sử dụng để đăng nhập vào hệ thống, vui lòng sử dụng chức năng đăng nhập bằng Facebook để sử dụng tài khoản với Facebook đó. Mọi thắc mắc xin vui lòng liên hệ qua hotline 1900.633.689. Xin cảm ơn'
		},
		UPDATE_PHONE: {
			head: 'Thông báo',
			body: 'Cập nhật số điện thoại thành công'
		},
		FEEDBACK: {
			head: 'Thông báo',
			body: 'Gửi phản hồi thành công. HeyU sẽ phản hồi lại sớm nhất có thể. Xin cảm ơn'
		},
		WELCOME: {
			head: 'Tin nhắn hệ thống',
			body: 'Chào mừng bạn đến với Săn Ship, hi vọng ứng dụng sẽ hỗ trợ các bạn phần nào đó trong công việc. Mọi vấn đề cần giải đáp các bạn gửi vào phản hồi cho bọn mình nhé. Thân!'
		},
		TURN_ON_PUSH_ORDER: {
			head: 'Thông báo',
			body: 'Đã bật chức năng nhận đơn hệ thống'
		},
		TURN_OFF_PUSH_ORDER: {
			head: 'Thông báo',
			body: 'Đã tắt chức năng nhận đơn hệ thống'
		},
		CAN_NOT_CHANGE_NAME: {
			head: 'Thông báo',
			body: 'Tài khoản của bạn đã xác thực vì thế bạn không thể thay đổi tên người dùng của tài khoản. Xin cảm ơn.'
		},
		CAN_NOT_CHANGE_BIRTHDAY: {
			head: 'Thông báo',
			body: 'Tài khoản của bạn đã xác thực vì thế bạn không thể thay đổi ngày sinh của tài khoản. Xin cảm ơn.'
		},
		MIN_MONEY_TRANSFER: {
			head: 'Thông báo',
			body: 'Giao dịch không thành công. Số SSM giao dịch tối thiểu là 50.000 đ'
		}
	},
	SYSTEM: {
		NOT_SUPPORTED_REGION: {
			head: 'Thông báo',
			body: 'Hiện tại hệ thống không còn hỗ trợ ở khu vực bạn đã chọn, vui lòng chọn khu vực khác được hệ thống hỗ trợ, xin cảm ơn.'
		},
		ERROR: {
			head: 'Thông báo',
			body: 'Hệ thống đang bận vui lòng thử lại. Mọi thắc mắc xin vui lòng liên hệ qua hotline 1900.633.689. Xin cảm ơn.'
		}
	},
	COMMENT: {
		MESSAGE_EMPTY: {
			head: 'Thông báo',
			body: 'Nội dung bình luận không được để trống'
		}
	},
  PERMISSION: {
      COMMENT_ERROR: {
          head: 'Thông báo',
          body: 'Bạn chưa uỷ quyền cho chúng tôi thay mặt bạn đăng lên Facebook.Xin vui lòng đăng xuất ra đăng nhập lại để thực hiện thao tác uỷ quyền. Mọi thắc mắc xin vui lòng liên hệ qua hotline 1900.633.689. Xin cảm ơn.'
      }
  },
  TOKEN : {
      EXPIRY: {
          head: 'Thông báo',
          body: 'Vì lý do nào đó phiên đăng nhập Facebook đã hết hạn.Vui lòng ấn đăng xuất rồi đăng nhập lại. Mọi thắc mắc xin vui lòng liên hệ qua hotline 1900.633.689. Xin cảm ơn.'
      }
  },

	POST: {
		DELETED: {
			head: 'Thông báo',
			body: 'Đơn hàng này đã bị xoá bởi người đăng vì thế bạn không thể nhận ngay hoặc bình luận.'
		}
	},
    PUSH: {
        LIMIT: {
            head: 'Thông báo',
            body: 'Bạn đã sử dụng hết số lần đẩy cho đơn hàng này'
        }
    },
	GROUP: {
		LOCKED: {
			head: 'Thông báo',
			body: 'Bạn đã bị group đăng bài này cấm do đó không thể bình luận vào đơn hàng này'
		},
		PRIVACY: {
			head: 'Thông báo',
			body: 'Bạn đang bình luận quá nhanh. Vui lòng vào mục cài đặt để thay đổi nội dung bình luận và thử lại trong giây lát. Xin cảm ơn.'
		}
	},
	CARD: {
		COMMON: {
			head: 'Thông báo',
			body: 'Vui lòng kiểm tra lại serial và mã thẻ, xin cảm ơn!'
		},
		CARD_USED: {
			head: 'Thông báo',
			body: 'Thẻ đã được sử dụng, hoặc thẻ sai.'
		},
    	CARD_LOCKED: {
			head: 'Thông báo',
			body: 'Thẻ bị khóa'
		},
    	CARD_EXPIRED: {
			head: 'Thông báo',
			body: 'Thẻ hết hạn sử dụng.'
		},
    	CARD_NOT_ACTIVE: {
			head: 'Thông báo',
			body: 'Thẻ chưa được kích hoạt hoặc không tồn tại.'
		}
	},
	PACKAGE: {
		NOT_ENOUGH_COINT: {
			head: 'Thông báo',
			body: 'Bạn không đủ coint để mua gói cước này vui lòng nạp thêm coint. Xin cảm ơn.'
		},
		SUCCESS: {
			head: 'Thông báo',
			body: 'Bạn đã mua gói cước thành công, nếu có lỗi xảy ra vui lòng đăng nhập lại hoặc vui lòng liên hệ qua hotline 1900.633.689. Xin cảm ơn.'
		},
		INVALID_MEMBER: {
			head: 'Thông báo',
			body: 'Tài khoản của bạn không đủ điều kiện để mua gói ưu đãi này!'
		},
		PACKAGE_NOT_EXISTS: {
			head: 'Thông báo',
			body: 'Gói ưu đãi không tồn tại, vui lòng nhập lại. Xin cảm ơn.'
		},
		INVALID_VERSION: {
			head: 'Thông báo',
			body: 'Hãy cập nhật phiên bản ứng dụng mới nhất để tận hưởng gói ưu đãi này, xin cảm ơn!'
		},
		FULL: {
			head: 'Thông báo',
			body: 'Gói ưu đãi đã hết số lần mua.'
		},
		CODE_NOT_EXISTS: {
			head: 'Thông báo',
			body: 'Ưu đãi không tồn tại, vui lòng thử lại sau.'
		},
		PROMOTE_EXISTS: {
			head: 'Thông báo',
			body: 'Đã xảy ra lỗi trong quá trình đổi mã KM của bạn, vui lòng thử lại sau.'
		},
		OVER: {
			head: 'Thông báo',
			body: 'Số lần mua gói ưu đãi này của bạn vượt quá số lần cho phép.'
		},
		ORDER: {
			head: 'Thông báo',
			body: 'Để đổi Voucher này bạn cần tạo thành công ít nhất 1 đơn hàng trong ngày hôm nay'
		},
		SHOPPING_SUCCESS: {
			head: 'Xin chúc mừng',
			body: 'Bạn đã mua gói ưu đãi thành công. Cảm ơn bạn đã tin tưởng và sử dụng HeyU!'
		},
		FAIL: {
			head: 'Thông báo',
			body: 'Gói ưu đãi không còn hợp lệ với tài khoản của bạn, vui lòng thử các gói ưu đãi khác, xin cảm ơn!'
		}
	},
    FEEDS: {
	    NOT_FOUND:{
        head: "Thông báo",
        body: "Đơn hàng của bạn đang được đẩy. Bạn vui lòng thử lại sau"
      },
      DONE_ALREADY:{
        head: "Thông báo",
        body: "Đơn hàng đã done"
      },
			NOT_ENOUGH_TO_SAVE: {
				head: 'Thông báo',
				body: 'Tài khoản của bạn đã hết hạn sử dụng, vui lòng nạp coint để sử dụng chức năng'
			}
		},
		EBILL: {
			SUCCESS: {
							head: "Thông báo",
							body: "Cập nhật thông tin hóa đơn thành công. Bạn vui lòng đợi trong 3-5 ngày để được phê duyệt",
			},
			FAILED: {
							head: "Thông báo",
							body: "Cập nhật hóa đơn thất bại",
			},
			DELETE: {
							head: 'Thông báo',
							body: 'Xóa thông tin xuất hóa đơn thành công'
			},
			CREATE: {
				head: 'Thông báo',
				body: 'Tạo yêu cầu xuất hóa đơn thành công. Bạn vui lòng đợi trong 3-5 ngày để được phê duyệt'
			},
			EXISTED: {
				head: 'Thông báo',
				body: 'Thông tin yêu cầu xuất hóa đơn đã tồn tại ở tài khoản này'
			}
		},
}
