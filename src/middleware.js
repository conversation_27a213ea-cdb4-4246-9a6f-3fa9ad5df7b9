'use strict';

import {auth} from './config';
import message from './message';
import CONSTANTS from './const';
import _ from 'lodash';
import Util from './Util';
import mailUtil from './mailUtil';
const ms = require('ms');

export function tokenToUserMiddleWare (req, res, next) {
  const memberToken = req.body.memberToken || req.query.memberToken;

  let appName = '';
  if(req.body && req.body.appName) {
    appName = req.body.appName
  }
  if(req.query && req.query.appName) {
    appName = req.query.appName
  }


  if(!memberToken) {
    return res.json({
      code: CONSTANTS.CODE.TOKEN_EXPIRE,
      message: message.USER.TOKEN_EXPIRE
    });
  }

  let stringToken = `user:${memberToken}`
  if(appName) {
    stringToken = `${appName}:${memberToken}`
  }
  redisConnection.get(stringToken, (err, result) => {
    if(err) {
      return res.json({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: message.SYSTEM.ERROR
      });
    }

    if(!result) {
      return res.json({
        code: CONSTANTS.CODE.TOKEN_EXPIRE,
        message: message.USER.TOKEN_EXPIRE
      });
    }

    try {
      const objSign = JSON.parse(result);
      if(!_.has(objSign, 'id') || !_.has(objSign, 'accessToken')) {
        return res.json({
          code: CONSTANTS.CODE.TOKEN_EXPIRE,
          message: message.USER.TOKEN_EXPIRE
        });
      }

      objSign.memberToken = memberToken;
      req.user = objSign;

      next();
    } catch(e) {
      return res.json({
        code: CONSTANTS.CODE.TOKEN_EXPIRE,
        message: message.USER.TOKEN_EXPIRE
      });
    }
  });
}

export function xAccessTokenHeader (req, res, next) {
  let token = req.headers['x-access-token'];

  if (token && (token === 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************.38hjO99PEhzk1IT8l16zbKemikhPHHAqZzsSw8lmWtE')) {
    next();
  } else {
    return res.json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      inf: 'Internal Server Error'
    })
  }
}

export function validateRequestHeader (req, res, next) {
  let payload;
  const headers = req.headers;
  const path = req.path;

  if(req.method === 'GET'){
    payload = req.params
  }
  if(req.method === 'POST'){
    payload = req.body
  }

  const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress
  const configRT = global.app.get('configRT')
  if(!configRT) {
    return next();
  }

  if(configRT.whiteListUrl && configRT.whiteListUrl.length && configRT.whiteListUrl.includes(path)) {
    return next();
  }

  if(ip && configRT.whiteListIP && configRT.whiteListIP.length && configRT.whiteListIP.includes(ip)) {
    return next();
  }

  if(ip && configRT.blackListIP && configRT.blackListIP.length && configRT.blackListIP.includes(ip)) {
    return res.json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      inf: 'Internal Server Error'
    })
  }

  if(payload && headers && headers.goal) {
    const goalCompare = Util.getGoal(path,JSON.stringify(payload));
    const timeoutCheck = configRT.timeoutCheck;
    const timeServer = Date.now();
    if(headers.goal !== goalCompare || (timeServer - payload.tShoot) > timeoutCheck) {
      if(req.path === '/api/v2.1/member/send-code') {
        configRT.isOpen = true
        configRT.shouldSendMail = false
      }
      if(configRT.shouldSendMail) {
        let message = '';
        if(headers.goal !== goalCompare) {
          message +=` --- Wrong token: reqT=${headers.goal}, serverT=${goalCompare}`
        }
        if((timeServer - payload.tShoot) > timeoutCheck) {
          message +=`\n\n --- Wrong time check: timeDiff: ${convertTime(timeServer - payload.tShoot)}`
        }
        message += `\n\n path=${path}\n\n headers=${JSON.stringify(headers)}\n\n payload=${JSON.stringify(payload)}`

        if(ip) {
          message += `\n\n ip = ${ip}`
        }

        if(payload.memberToken) {
          let appName = '';
          if(payload.appName) {
            appName = payload.appName
          }
          let stringToken = `user:${payload.memberToken}`
          if(appName) {
            stringToken = `${appName}:${payload.memberToken}`
          }
          redisConnection.get(stringToken, (err, result) => {
            if(result) {
              const objSign = JSON.parse(result);
              if(objSign.id) {
                message += `\n\n id = ${objSign.id}`
              }
            }
            mailUtil
              .sendMail(message);
          });

        } else {
          mailUtil
          .sendMail(message);
        }

      }

      if(configRT.isOpen) {
        console.log('ahihi block successfully',ip)

        return res.json({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          inf: 'Internal Server Error'
        })
      }
    }
  }
  next();
}

function convertTime(time) {
  let h,m,s;

  h = Math.floor(time/1000/60/60);
  m = Math.floor((time/1000/60/60 - h)*60);
  s = Math.floor(((time/1000/60/60 - h)*60 - m)*60);
  if(h > 0) {
    return `${h} giờ ${m} phút ${s} giây`
  } else {
    if(m > 0) {
      return `${m} phút ${s} giây`
    } else {
      return `${s} giây`
    }
  }
}
