import path from 'path';
import express from 'express';
import bodyParser from 'body-parser';
import React from 'react';
import expressValidator from 'express-validator';
import fs from 'fs.extra';
import redis from "redis";
import _ from "lodash";
import multipart from 'connect-multiparty';
import multer from 'multer'
const multipartMiddleware = multipart();
const uploadMulter = multer();
const ioredis = require('ioredis')
const ms = require('ms')
const Logger = require('./logger')
global.logger = Logger(`${__dirname}/logs`);

import { port, MONGO_DB, REDIS_DB, MONGO_DB_OPTIONS, bookWebAddr} from './config';
import * as middleware from './middleware';
// v1.0
import Members_v1 from './api/v1/members';
import Feeds_v1 from './api/v1/feeds';
import Comments_v1 from './api/v1/comments';
import HotUpdate_v1 from './api/v1/hotUpdate';
import HotUpdateDriver from './api/v1/hotUpdateDriver';
import Notifications_v1 from './api/v1/notifications';
import Shipper_v1 from './api/v1/shipper';
import FeedBack_v1 from './api/v1/feedback';
import Ebill_v1 from './api/v1/ebill';
// v2.0
import Shipper_v2 from './api/v2/shipper';
import Shipper_v21 from './api/v2.1/shipper';
import Shop_v2 from './api/v2/shop';
import Shop_v21 from './api/v2.1/shop';
import Shop_v3 from './api/v3/shop';
import Members_v2 from './api/v2/members';
import Comments_v2 from './api/v2/comments';
import Comments_system_v2 from './api/v2/commentsSystem';
import Notifications_v2 from './api/v2/notifications';
import Feeds_v2 from './api/v2/feeds';
import Gateway_v2 from './api/v2/gateway';
import Gateway_v21 from './api/v2.1/gateway';
import App from './api/v1/app';
import App_v2 from './api/v2/app';
import App_v21 from './api/v2.1/app';
import Package_v2 from './api/v2/package';
import Chat_v2 from './api/v2/chat';
import ChatReceiver from './api/v2/chatReceiver';
import ChatReceiverWeb from './api/v2/chatReceiverWeb';
import Lending from './api/v2/lending';
// v2.1
import Members_v21 from './api/v2.1/members';
import Business from './api/v2/business'
import mailUtil from './mailUtil';

global.mongoose           = require('mongoose');
global.cachegoose         = require('cachegoose');
global.redisConnection    = new ioredis(REDIS_DB);
global.redisConnectionForPubSub    = new ioredis(REDIS_DB);
global.redisConnectionCachegoose    = new ioredis(REDIS_DB);
global.mongoConnections = require('./connections/mongo')
global.request            = require('request');
global._                  = require('lodash');
global.async              = require('async');
global.jwt                = require('jsonwebtoken');
global.util               = require('util');
global.navigator = global.navigator || {};
global.navigator.userAgent = global.navigator.userAgent || 'all';

// Connect to redis
redisConnectionForPubSub.on('ready', () => {
  console.log('[REDIS-PubSub] ready');
  setInterval(() => {
    redisConnectionForPubSub.ping((err,data) => {
      if (err) {
        logger.logError([`Redis PubSub keepalive error ${err}`])
      }
    });
  }, 30000);
});

redisConnection.on('ready', () => {
  console.log('[REDIS-master] ready');
  setInterval(() => {
    redisConnection.ping((err,data) => {
      if (err) {
        logger.logError([`Redis master keepalive error ${err}`])
      }
    });
  }, 30000);
});

redisConnectionCachegoose.on('ready', () => {
  console.log('[REDIS-Cachegoose] ready');
  setInterval(() => {
    redisConnectionCachegoose.ping((err,data) => {
      if (err) {
        logger.logError([`Redis Cachegoose keepalive error ${err}`])
      }
    });
  }, 30000);
});

// Connect to database
mongoose.Promise = global.Promise;
// mongoose.connect(MONGO_DB, MONGO_DB_OPTIONS,function(err) {
//     if (err) {
//       mailUtil
//         .sendMail(` --- ERR MONGO --- ${err}`);
//     }
// });

// Caching with redis
// cachegoose(mongoose, {
//   engine: 'redis',
//   ...REDIS_DB
// });

// Load Model
fs.readdirSync(__dirname + '/data').forEach(function (file) {
  global[_.upperFirst(_.camelCase(file.replace('.js', '')))] = require(__dirname + '/data/' + file);
});

const app = global.app = express();

const ConfigRequestTokenManager = require('./job/configRequestTokenManager');
const ConfigRiskTeamManager = require('./job/configRiskTeamManager');

app.set('views', path.join(__dirname, 'content'));
app.set('view engine', 'ejs');
app.use(express.static(path.join(__dirname, 'public')));
app.use(bodyParser());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json({limit: '25mb'}));
app.use(expressValidator());

const logResponseBody = (req, res, next) => {
  next();

  const oldEnd = res.end;

  const chunks = [];

  res.end = (chunk, ...args) => {
    if (chunk) {
      chunks.push(chunk);
    }
    try{
      const body = JSON.parse(Buffer.concat(chunks).toString('utf8'));
      if(req.body && body && body.code && body.code !== 200) {
        console.error(req.path,req.body, body);
      }
      if(req.query && body && body.code && body.code !== 200) {
        console.error(req.path,req.query, body);
      }
    }
    catch(error) {
      if(req.body) {
        console.error(req.path,req.body, error);
      }
      if(req.query) {
        console.error(req.path,req.query, error);
      }
    }
    return oldEnd.apply(res, [chunk, ...args]);
  };
}

// app.use(logResponseBody);

const tokenToUserMiddleWare = middleware.tokenToUserMiddleWare;
const xAccessTokenHeader = middleware.xAccessTokenHeader;
const validateRequestHeader = middleware.validateRequestHeader;

app.use(validateRequestHeader);

app.get('/', (req, res) => res.redirect('https://heyu.asia'));

// App
app.post('/admin/set-config-request-token',(req, res) => {

  const isOpen = req.body.isOpen;
  const shouldSendMail = req.body.shouldSendMail;
  const timeoutCheck = req.body.timeoutCheck;

  if(typeof isOpen !== 'boolean') {
    return res.json({
      code: 300,
      message: "isOpen should be boolean"
    })
  }

  if(typeof shouldSendMail !== 'boolean') {
    return res.json({
      code: 300,
      message: "shouldSendMail should be boolean"
    })
  }

  if(!timeoutCheck) {
    return res.json({
      code: 300,
      message: "timeoutCheck not be set"
    })
  }

  if(typeof timeoutCheck !== 'number') {
    return res.json({
      code: 300,
      message: "timeoutCheck should be number in ms"
    })
  }

  app.set('configRT',{
    isOpen,
    shouldSendMail: shouldSendMail,
    timeoutCheck: timeoutCheck
  })

  res.json({
    code: 200
  })
});

app.post('/admin/get-config-request-token',(req, res) => {
  res.json({
    code: 200,
    config:app.get('configRT')
  })
});

app.post('/api/v2.1/app/get-contact', tokenToUserMiddleWare, App_v21.getContact);
app.post('/api/v2.0/app/get-api-key-google-map', App_v2.getAPIKeyGoogleMap);
app.post('/api/v2.0/app/tracking-action', tokenToUserMiddleWare, App_v2.tracking);
app.post('/api/v2.1/app/get-config-for-background-location', App_v21.getConfigForBackgroundLocation);
app.post('/api/v2.0/app/get-message-warning-authentication', App_v2.getConfigForMessageAuthen);
app.post('/api/v2.0/app/get-config-for-update-location', App_v2.getConfigForUpdateLocation);
app.post('/api/v2.2/app/list-service-available',tokenToUserMiddleWare, App_v21.listServiceAvailableV2);
app.post('/api/v2.1/app/get-news-category', tokenToUserMiddleWare, App_v21.getNewsCategory);
app.post('/api/v2.1/app/get-news', tokenToUserMiddleWare, App_v21.getNews);
app.post('/api/v2.1/app/get-promotion-heyu', tokenToUserMiddleWare, App_v21.getPromotionHeyU);
app.post('/api/v2.1/app/get-promotion-category', tokenToUserMiddleWare, App_v21.getPromotionCategory);
app.post('/api/v2.0/app/get-detail-news', App_v2.getDetailNews);
app.post('/api/v2.1/app/get-banner', App_v21.getBanner);
app.post('/api/v2.1/app/get-banner-food', App_v21.getBannerFood);
app.post('/api/v2.1/app/get-config-for-mount', App_v21.getConfigForLending);
app.post('/api/v2.1/app/get-config-show-news', tokenToUserMiddleWare, App_v21.getConfigShowNews);
app.post('/api/v2.0/app/get-config-zippo',tokenToUserMiddleWare, App_v2.getConfigContact);
app.post('/api/v2.0/app/sync-zippo',tokenToUserMiddleWare, App_v2.syncContact);
app.post('/api/v2.0/app/get-config-tag', App_v2.getConfigTag);
app.post('/api/v2.0/app/get-config-payment', App_v21.getConfigForReview);
app.post('/api/v2.0/app/get-config-map', App_v21.getConfigMap);
app.post('/api/v2.0/app/send-crash-log', App_v2.sendCrashReport);
app.post('/api/v2.0/app/get-config-analytics', App_v2.getConfigAnalytics);
app.post('/api/v2.0/app/list-service-register', tokenToUserMiddleWare, App_v2.listServiceRegister);
app.post('/api/v2.1/app/list-service-register', tokenToUserMiddleWare, App_v21.listServiceRegister);
app.post('/api/v2.0/app/switch-service', tokenToUserMiddleWare, App_v2.switchService);
app.post('/api/v2.0/app/sync-data', App_v2.handleSyncData);
app.post('/api/v2.0/app/get-config-history-facebook', tokenToUserMiddleWare, App_v2.getConfigHistoryFacebook);
app.post('/api/v2.0/app/get-config-point', App_v2.getConfigPoint);
app.post('/api/v2.0/app/list-topup-address', App_v2.listTopupAddress);
app.post('/api/v2.0/app/get-config-login', App_v2.getConfigLogin);
app.post('/api/v2.0/app/get-hotline-book-car', tokenToUserMiddleWare, App_v2.getHotLineBookCar);
app.post('/api/v2.0/app/get-state-machine-rider', tokenToUserMiddleWare, App_v2.getStateMachineRider);
app.post('/api/v2.0/app/get-config-for-phone-athentication', App_v2.getConfigForPhoneAthentication);
app.post('/api/v2.0/app/get-config-necessary', App_v2.getConfigNecessary);
app.post('/api/v2.0/app/get-config-change-phone', App_v2.getConfigChangePhone);
app.post('/api/v2.0/app/check-can-turn-on-push-order', tokenToUserMiddleWare, App_v2.checkCanTurnOnPushOrder);
app.post('/api/v2.0/app/get-config-show-max-service-charge', App_v2.getConfigShowMaxServiceCharge);
app.post('/api/v2.0/app/get-config-referral-code', App_v2.getConfigReferralCode);
app.post('/api/v2.0/app/get-config-payment-method', App_v2.getPaymentMethodConfig);
app.post('/api/v2.0/app/get-config-payment-inapp', App_v2.getConfigPaymentInApp);
app.post('/api/v2.0/app/get-config-show-payment-inapp', App_v2.getConfigShowPaymentInapp);
app.post('/api/v1.0/app/get-link-youtube', App_v2.getLinkYoutube);
// app.post('/api/v1.0/app/get-link-youtube-shipper', App_v2.getLinkYoutubeShipper);
app.post('/api/v1.0/app/get-config-shipper-authen-online', App_v2.getConfigShipperAuthenOnline);
app.post('/api/v2.0/app/get-config-covid_passport', tokenToUserMiddleWare, App_v2.getConfigCovidPassport);
app.post('/api/v1.0/app/get-config-shortcut-service', tokenToUserMiddleWare, App_v2.getConfigShortcutService);
app.post('/api/v1.0/app/get-config-force-app-driver', tokenToUserMiddleWare, App_v2.getConfigForceAppDriver);
app.post('/api/v1.0/app/config-routing-shop', App_v2.getConfigRoutingShop);
app.post('/api/v1.0/app/config-time-accept-order', App_v2.getConfigTimeAcceptOrder);
app.post('/api/v1.0/app/config-image-frame-banner', App_v2.getConfigImageFrameBanner);
app.post('/api/v1.0/app/config-code-push', App_v2.getConfigCodePush);
app.post('/api/v1.0/app/get-config-hotline', App_v2.getConfigHotline);
app.post('/api/v1.0/app/config-guide', App_v2.getConfigGuide);
app.post('/api/v1.0/app/config-authen', tokenToUserMiddleWare, App_v2.getConfigAuthen);
app.post('/api/v1.0/app/get-state-authen', tokenToUserMiddleWare, App_v2.getStateAuthen);
app.post('/api/v1.0/app/remove-account', tokenToUserMiddleWare, App_v2.removeAccount);
app.post('/api/v1.0/app/get-config-remove-account', tokenToUserMiddleWare, App_v2.getConfigRemoveAccount);
app.post('/api/v1.0/app/get-config-location-shipper', tokenToUserMiddleWare, App_v2.getConfigLocationShipper);
app.post('/api/v1.0/app/get-config-amount-deposit', tokenToUserMiddleWare, App_v2.getConfigAmountDeposit);
app.post('/api/v1.0/app/get-weather', App.getWeather);
//Ebill
app.post('/api/v1.0/ebill/create-ebill', tokenToUserMiddleWare, Ebill_v1.createBill);
app.post('/api/v1.0/ebill/update-ebill', tokenToUserMiddleWare, Ebill_v1.updateBill);
app.post('/api/v1.0/ebill/delete-ebill', tokenToUserMiddleWare, Ebill_v1.deleteBill);
app.post('/api/v1.0/ebill/get-ebill', tokenToUserMiddleWare, Ebill_v1.getBill);
app.post('/api/v1.0/ebill/approve-ebill', Ebill_v1.approveBill);
app.post('/api/v1.0/ebill/reject-ebill', Ebill_v1.rejectBill);
app.post('/api/v1.0/app/save-html-zalo', App_v2.saveHtmlZalo);

// Feeds v2
app.post('/api/v2.0/feeds/get',                          tokenToUserMiddleWare, Feeds_v2.getInitFeeds);
app.post('/api/v2.0/feed/get-config-feed-facebook', Feeds_v2.getConfigFacebookFeed);

// Shipper /v2
app.post('/api/v2.0/shipper/authen/get',                  tokenToUserMiddleWare, Shipper_v2.getAuthenInf);
app.post('/api/v2.0/shipper/authen/create',               multipartMiddleware, tokenToUserMiddleWare, Shipper_v2.authen);
app.post('/api/v2.0/shipper/authen/get-new-approve',      tokenToUserMiddleWare, Shipper_v2.getAuthenInfNew);
app.post('/api/v2.0/shipper/authen/update',               multipartMiddleware, tokenToUserMiddleWare, Shipper_v2.updateAuthenInf);
app.post('/api/v2.0/shipper/authen/send-sign',            Shipper_v2.sendSign);
app.post('/api/v2.0/shipper/authen/send-bill',            Shipper_v2.sendBill);
app.post('/api/v2.0/shipper/payment-block-account',       Shipper_v2.paymentBlockAccount);

app.post('/api/v2.1/shipper/authen/create',               tokenToUserMiddleWare, Shipper_v21.authen);
app.post('/api/v2.1/shipper/authen/get-new-approve',      tokenToUserMiddleWare, Shipper_v21.getAuthenInfNew);
app.post('/api/v2.1/shipper/authen/update',               tokenToUserMiddleWare, Shipper_v21.updateAuthenInf);
app.post('/api/v2.1/shipper/authen/get-config',            tokenToUserMiddleWare, Shipper_v21.getConfigAuthen);
app.post('/api/v2.1/shipper/authen/send-contract',             Shipper_v21.sendContract);
app.post('/api/v2.1/shipper/authen/send-bill',            tokenToUserMiddleWare, Shipper_v21.sendBill);
app.post('/api/v2.1/shipper/authen/send-uniform',            tokenToUserMiddleWare, Shipper_v21.sendUniform);
app.post('/api/v1.0/shipper/authen/get-status-uniform',   tokenToUserMiddleWare, Shipper_v21.getStatusUniform);
app.post('/api/v1.0/shipper/authen/get-status-buy-uniform',   tokenToUserMiddleWare, Shipper_v21.getStatusBuyUniform);

// Shop v2
app.post('/api/v2.0/shop/authen/create',                  uploadMulter.fields([{ name: 'avatar', maxCount: 1 }, { name: 'frontCer', maxCount: 1 }, { name: 'backCer', maxCount: 1 }]), tokenToUserMiddleWare, Shop_v2.authen);
app.post('/api/v2.0/shop/authen/get',                     tokenToUserMiddleWare, Shop_v2.getAuthenInf);
app.post('/api/v2.0/shop/authen/modify',                  uploadMulter.fields([{ name: 'avatar', maxCount: 1 }, { name: 'frontCer', maxCount: 1 }, { name: 'backCer', maxCount: 1 }]), tokenToUserMiddleWare, Shop_v2.modifyAuthenInf);

// Shop v2.1
app.post('/api/v2.1/shop/authen/create',                  tokenToUserMiddleWare, Shop_v21.authen);
app.post('/api/v2.1/shop/authen/get',                     tokenToUserMiddleWare, Shop_v21.getAuthenInf);
app.post('/api/v2.1/shop/authen/modify',                  tokenToUserMiddleWare, Shop_v21.modifyAuthenInf);
app.post('/api/v1.0/shop/config-authen-shop',              tokenToUserMiddleWare, Shop_v21.configAuthenShop);

app.post('/api/v3.0/shop/authen/create',                  tokenToUserMiddleWare, Shop_v3.authen);
app.post('/api/v3.0/shop/authen/get',                     tokenToUserMiddleWare, Shop_v3.getAuthenInf);
app.post('/api/v3.0/shop/authen/modify',                  tokenToUserMiddleWare, Shop_v3.modifyAuthenInf);
app.post('/api/v2.0/shop/config-authen-shop',              tokenToUserMiddleWare, Shop_v3.configAuthenShop);

//*** Member ***//
// app.post('/api/v2.0/member/login',                        xAccessTokenHeader, Members_v2.login); // Facebook login
app.post('/api/v2.0/member/get-hot-news',                 tokenToUserMiddleWare, Notifications_v2.getHotNews);
app.post('/api/v2.0/member/send-code',                    Members_v2.getPhoneCode);
app.post('/api/v2.1/member/send-code',                    Members_v21.getPhoneCode);
app.post('/api/v2.0/member/check-code',                   Members_v2.checkCode);
app.post('/api/v2.0/member/login-by-phone',               Members_v2.loginByPhone);
app.post('/api/v2.0/member/logout',                       tokenToUserMiddleWare, Members_v2.logout);
app.post('/api/v2.0/member/get',                          tokenToUserMiddleWare, Members_v2.get);
app.post('/api/v2.1/member/getInfoFromToken',             tokenToUserMiddleWare, Members_v2.getInfoFromToken_v2);
app.post('/api/v2.0/member/get-user-id',                  Members_v2.getUserId);
app.post('/api/v2.0/member/reset-password',               Members_v2.resetPassword);
app.post('/api/v2.1/member/reset-password',               Members_v21.resetPassword);
app.post('/api/v2.0/member/get-by-id',                    Members_v2.getById);
app.post('/api/v2.0/member/get-by-phone',                 tokenToUserMiddleWare, Members_v2.getUsersByPhone);
// app.post('/api/v2.0/member/update-phone',                 tokenToUserMiddleWare, Members_v2.updatePhone); // Account kit screen
app.post('/api/v2.0/member/change-phone-for-user',        tokenToUserMiddleWare, Members_v2.changePhoneForUser);
app.post('/api/v2.1/member/change-phone-for-user',        tokenToUserMiddleWare, Members_v21.changePhoneForUser);
// app.post('/api/v2.0/member/change-phone',                 Members_v2.changePhone); // For CMS temp disabled
app.post('/api/v2.0/member/feed/save',                    tokenToUserMiddleWare, Members_v2.saveFeed);
app.post('/api/v2.0/member/feed/check-black-phone',       tokenToUserMiddleWare, Feeds_v2.checkBlackPhone);
app.post('/api/v2.0/member/feed/all',                     tokenToUserMiddleWare, Members_v2.getFeeds);
app.post('/api/v2.0/member/feed/delete',                  tokenToUserMiddleWare, Members_v2.deleteAFeed);
app.post('/api/v2.0/member/feed/delete-all',              tokenToUserMiddleWare, Members_v2.deleteAllFeeds);
app.post('/api/v2.0/member/update-location',              tokenToUserMiddleWare, Members_v2.updateLocation);
app.post('/api/v2.0/member/add-presenter-code',           tokenToUserMiddleWare, Members_v2.addPresenterCode);
app.post('/api/v2.0/member/choose-mode',                  tokenToUserMiddleWare, Members_v2.chooseMode);
app.post('/api/v2.1/member/choose-mode',                  tokenToUserMiddleWare, Members_v21.chooseMode);
app.post('/api/v2.0/member/done-training',                tokenToUserMiddleWare, Members_v2.doneTraining);
app.post('/api/v2.0/member/block-create-order',                                  Members_v2.blockCreateOrder);
app.post('/api/v2.0/member/toggle-order',                 tokenToUserMiddleWare, Members_v2.toggleReceiveOrder);
app.post('/api/v2.0/member/transfer-ssm-to-coint',        tokenToUserMiddleWare, Members_v2.transferSSMToCoint);
app.post('/api/v2.0/member/history-transaction',          tokenToUserMiddleWare, Members_v2.historyTransaction);
app.post('/api/v2.0/member/get-point',                    tokenToUserMiddleWare, Members_v2.getPoint);
app.post('/api/v2.0/member/history-transaction-point',    tokenToUserMiddleWare, Members_v2.historyTransactionPoint);
app.post('/api/v2.0/member/get-deposit-transaction',      tokenToUserMiddleWare, Members_v2.getDepositTransaction);
app.post('/api/v2.0/member/get-region',                   tokenToUserMiddleWare, Members_v2.getRegionByLatLng);
app.post('/api/v2.0/feeds/get-history',                   tokenToUserMiddleWare, Feeds_v2.getHistoryOrder);
app.post('/api/v2.0/feeds/get-statistic',                 tokenToUserMiddleWare, Feeds_v2.getStatistic);
app.post('/api/v2.0/member/transfer-cod-to-money',        tokenToUserMiddleWare, Members_v2.transferCODToMoney);

app.post('/api/v2.0/chat/get-history',                    tokenToUserMiddleWare, Chat_v2.getMessagesByUser);
app.post('/api/v2.0/chat/get-message-by-list-user',       Chat_v2.getMessagesByListUser);
app.post('/api/v2.0/chat/get-history-by-conversation',    tokenToUserMiddleWare, Chat_v2.getMessagesByConversation);
app.post('/api/v2.0/chat/list-history',                   tokenToUserMiddleWare, Chat_v2.list);
app.post('/api/v2.0/chat/get-conversation-inf',           tokenToUserMiddleWare, Chat_v2.getConversationInf);
app.post('/api/v2.0/chat/get-new-order-message',           tokenToUserMiddleWare, Chat_v2.getNewOrderMessage);

app.post('/api/v2.0/chat/get-history-receiver',                    tokenToUserMiddleWare, ChatReceiver.getMessagesByUser);
app.post('/api/v2.0/chat/get-message-by-list-user-receiver',       ChatReceiver.getMessagesByListUser);
app.post('/api/v2.0/chat/get-history-by-conversation-receiver',    tokenToUserMiddleWare, ChatReceiver.getMessagesByConversation);
app.post('/api/v2.0/chat/list-history-receiver',                   tokenToUserMiddleWare, ChatReceiver.list);
app.post('/api/v2.0/chat/get-conversation-inf-receiver',           tokenToUserMiddleWare, ChatReceiver.getConversationInf);
app.post('/api/v2.0/chat/get-new-order-message-receiver',           tokenToUserMiddleWare, ChatReceiver.getNewOrderMessage);
app.post('/api/v2.0/chat/get-sequence-can-chat-receiver',           tokenToUserMiddleWare, ChatReceiver.getSequenceCanChat);

app.post('/api/v2.0/chat/get-history-receiver-web',                    ChatReceiverWeb.getMessagesByUser);
app.post('/api/v2.0/chat/get-message-by-list-user-receiver-web',       ChatReceiverWeb.getMessagesByListUser);
app.post('/api/v2.0/chat/get-history-by-conversation-receiver-web',    ChatReceiverWeb.getMessagesByConversation);
app.post('/api/v2.0/chat/list-history-receiver-web',                   ChatReceiverWeb.list);
app.post('/api/v2.0/chat/get-conversation-inf-receiver-web',           ChatReceiverWeb.getConversationInf);
app.post('/api/v2.0/chat/get-new-order-message-receiver-web',          ChatReceiverWeb.getNewOrderMessage);

app.post('/api/v2.0/member/login-without-password',       Members_v2.loginWithoutPassword);

// member v2.1
app.post('/api/v2.1/member/check-phone-exist',            Members_v21.checkExistsByPhone);
app.post('/api/v2.1/member/update-profile',               tokenToUserMiddleWare, Members_v21.updateProfile);
app.post('/api/v2.1/member/update-location',              tokenToUserMiddleWare, Members_v21.updateLocation);
app.post('/api/v2.1/member/sync-update-location',         tokenToUserMiddleWare, Members_v21.syncLocationFail);
app.post('/api/v2.1/member/add-default-location',         tokenToUserMiddleWare, Members_v21.addDefaultLocation);
app.post('/api/v2.1/member/unshift-default-location',         tokenToUserMiddleWare, Members_v21.unshiftDefaultLocation);
app.post('/api/v2.1/member/remove-default-location',         tokenToUserMiddleWare, Members_v21.removeDefaultLocation);
app.post('/api/v2.1/member/modify-default-location',         tokenToUserMiddleWare, Members_v21.modifyDefaultLocation);
app.post('/api/v2.1/member/list-default-location',         tokenToUserMiddleWare, Members_v21.listDefaultLocation);
app.post('/api/v2.1/member/modify-list-default-location', tokenToUserMiddleWare, Members_v21.modifyListDefaultLocation);
app.post('/api/v2.1/member/block',                        Members_v21.blockUser);
app.post('/api/v2.1/member/get',                          tokenToUserMiddleWare, Members_v21.get);
app.post('/api/v2.1/member/login-without-password',       Members_v21.loginWithoutPassword);
app.post('/api/v1.0/member/change-amount-deposit',        tokenToUserMiddleWare, Members_v2.changeAmountDeposit);
app.post('/api/v1.0/member/add-loyal-customer',           tokenToUserMiddleWare, Members_v1.addLoyalCustomer);
app.post('/api/v1.0/member/remove-loyal-customer',        tokenToUserMiddleWare, Members_v1.removeLoyalCustomer);
app.post('/api/v1.0/member/modify-loyal-customer',        tokenToUserMiddleWare, Members_v1.modifyLoyalCustomer);
app.post('/api/v1.0/member/list-loyal-customers',         tokenToUserMiddleWare, Members_v1.listLoyalCustomers);
app.post('/api/v1.0/member/modify-list-loyal-customers',  tokenToUserMiddleWare, Members_v1.modifyListLoyalCustomers);
app.post('/api/v1.0/member/unshift-loyal-customers',         tokenToUserMiddleWare, Members_v1.unshiftLoyalCustomers);

//*** Comment v2 ***//
app.post('/api/v2.0/comment/post',                        tokenToUserMiddleWare, Comments_v2.postComment);
app.post('/api/v2.0/comment/upload-cookies',              tokenToUserMiddleWare, Comments_v2.uploadCookies);

//*** Shipper v1 ***//
app.post('/api/v1.0/shipper/get-nearest',                 tokenToUserMiddleWare, Shipper_v1.getNearest());
app.post('/api/v1.0/shipper/reward',(req, res) => {
  return res.redirect(`${bookWebAddr}/reward?memberToken=${req.body.memberToken}`);
});
//*** App v1 ***//
app.post('/api/v1.0/app/create-new-hot-update',           HotUpdate_v1.createUpdate());
app.post('/api/v1.0/app/get-lastest-update',              HotUpdate_v1.lastestUpdate());
app.post('/api/v1.0/app/create-new-hot-update-driver',           HotUpdateDriver.createUpdate());
app.post('/api/v1.0/app/get-lastest-update-driver',              HotUpdateDriver.lastestUpdate());
//*** Notifications v2 ***//
app.post('/api/v2.0/app/notify',                          tokenToUserMiddleWare, Notifications_v2.notify);
app.post('/api/v2.0/app/voip-notify',                     tokenToUserMiddleWare, Notifications_v2.voipNotification);

app.post('/api/v2.0/app/create-notification',              Notifications_v2.createNotification);
app.post('/api/v2.0/app/count-notification',              tokenToUserMiddleWare, Notifications_v2.count);
app.post('/api/v2.0/app/list-notification',              tokenToUserMiddleWare, Notifications_v2.list);
app.post('/api/v2.0/app/seen-notification',              tokenToUserMiddleWare, Notifications_v2.seenNotify);
app.post('/api/v2.0/app/seen-all-notification',              tokenToUserMiddleWare, Notifications_v2.seenAllNotify);
app.post('/api/v2.0/app/get-detail-notification',              tokenToUserMiddleWare, Notifications_v2.getDetailNotification);
//*** FeedBack ***//
app.post('/api/v2.0/app/feedback',                        uploadMulter.single('image'), tokenToUserMiddleWare, App_v2.feedback);
app.post('/api/v2.1/app/feedback',                        tokenToUserMiddleWare, App_v21.feedback);
app.post('/api/v2.1/app/send-feedback',                        tokenToUserMiddleWare, App_v21.sendFeedback);
app.post('/api/v2.1/app/list-feedback',                        tokenToUserMiddleWare, App_v21.getFeedbackList);
app.post('/api/v2.1/app/get-feedback-samples',                        tokenToUserMiddleWare, App_v21.getFeedbackSample);
app.post('/api/v2.1/app/get-feedback-samples-general',                        tokenToUserMiddleWare, App_v21.getFeedbackSampleGeneral);
app.post('/api/v2.1/app/get-simple-feedback',                        tokenToUserMiddleWare, App_v21.getSimpleFeedback);

//*** Gateway ***
app.post('/api/v2.0/gateway/get-config',                  Gateway_v2.getConfig);
app.post('/api/v2.1/gateway/get-config',                  Gateway_v21.getConfig);
app.post('/api/v2.0/gateway/get-config-for-card',         Gateway_v2.getConfigForCard);
app.post('/api/v2.0/gateway/card-charging',               tokenToUserMiddleWare, Gateway_v2.cardChargingRedpay);
app.post('/api/v2.0/gateway/bank-charging',               tokenToUserMiddleWare, Gateway_v2.bankCharging);

//*** Lending ***
app.get('/api/v2.0/lending/callback',                  Lending.callback);

//*** Bussiness ***
app.post('/api/v2.0/business/get-model',     tokenToUserMiddleWare,           Business.list);
app.post('/api/v2.0/business/get-items',      tokenToUserMiddleWare,          Business.listBusinessItemByModel);
app.post('/api/v2.0/business/send-report',      tokenToUserMiddleWare,          Business.sendBusinessReport);
app.post('/api/v2.0/business/get-user-model',      tokenToUserMiddleWare,          Business.getUserBusinessModel);

//*** TickBox ***
app.post('/api/v2.0/app/get-config-payment-tickbox', tokenToUserMiddleWare, App_v2.getConfigPaymentTickBox);
app.post('/api/v2.0/app/get-config-show-payment-tickbox', App_v2.getConfigShowPaymentTickBox);
app.post('/api/v2.0/member/get-tickbox-transaction',      tokenToUserMiddleWare, Members_v2.getTickBoxTransaction);
app.post('/api/v2.0/app/get-payment-method-tickbox', tokenToUserMiddleWare, App_v2.getPaymentMethodTickbox);

app.post('/api/v2.0/app/get-config-payment-heywow', tokenToUserMiddleWare, App_v2.getConfigPaymentHeywow);

app.post('/api/v2.0/app/get-config-block-shipper', tokenToUserMiddleWare, App_v2.getConfigBlockShipper);

// hey care
app.post('/api/v1.0/app/list-service-register', tokenToUserMiddleWare, App.listServiceRegisterHeyCare);
app.post('/api/v1.0/app/switch-service', tokenToUserMiddleWare, App.switchServiceHeyCare);
app.post('/api/v1.0/app/get-config-for-update-location', App.getConfigForUpdateLocation);
app.post('/api/v1.0/app/get-config-for-background-location', App.getConfigForBackgroundLocation);
app.post('/api/v1.0/app/config-authen-heycare', tokenToUserMiddleWare, App.getConfigAuthenHeyCare);
app.post('/api/v1.0/app/get-config-block-staff', tokenToUserMiddleWare, App.getConfigBlockStaff);
app.post('/api/v1.0/app/get-contact-heycare', tokenToUserMiddleWare, App.getContactHeyCare);
app.post('/api/v1.0/app/check-can-turn-on-push-order', tokenToUserMiddleWare, App.checkCanTurnOnPushOrder);
app.post('/api/v1.0/app/tracking-hey-care', tokenToUserMiddleWare, App.trackingHeyCare);
app.post('/api/v1.0/app/update-identity-block-shipper', tokenToUserMiddleWare, App.updateIdentityBlockShipper);

app.post('/api/v1.0/member/history-transaction',          tokenToUserMiddleWare, Members_v1.historyTransactionStaff);
app.post('/api/v1.0/member/transfer-ssm-to-coint',        tokenToUserMiddleWare, Members_v1.transferSSMToCointStaff);

app.post('/api/v1.0/member/list-package',        tokenToUserMiddleWare, Members_v1.listPackage);
app.post('/api/v1.0/member/get-package',        tokenToUserMiddleWare, Members_v1.getPackage);
app.post('/api/v1.0/member/list-my-package',        Members_v1.listMyPackage);
app.post('/api/v1.0/member/get-my-package',        tokenToUserMiddleWare, Members_v1.getMyPackage);
app.post('/api/v1.0/member/buy-package',        tokenToUserMiddleWare, Members_v1.buyPackage);

app.listen(process.env.PORT || port, () => {
  console.log(`The server is running at http://localhost:${port}/`);
  logger.logError([`Service has been started at port: ${port}`])
});

process.on('uncaughtException', (err) => {
  logger.logError([`uncaughtException ${err}`])
});
/* eslint-enable no-console */
