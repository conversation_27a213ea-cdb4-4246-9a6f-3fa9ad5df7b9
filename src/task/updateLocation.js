const _ = require('lodash');

class LocationManager {
  constructor() {
    this.list = [];
    this.processing = false;
  }

  add(jobInf) {
    this.list.unshift(jobInf);
    if(!this.processing) {
      this.processing = true;
      this.process();
    }
  }

  process() {
    const jobInf = this.list.pop();
    if(jobInf) {
      const userId = jobInf.userId;
      const location = jobInf.location;

      if(_.isPlainObject(location) && _.isFinite(location.lat) && _.isFinite(location.lng)) {
          const update = {
            updatedAt: Date.now(),
            location: {
              coordinates: [location.lng, location.lat],
              type: 'Point'
            }
          }

          Members
              .update({_id: userId}, update)
              .exec((err, result) => {
                this.process();
              });

          // const objCreate = {
          //   member: userId,
          //   location: update.location
          // }
          //
          // if(jobInf.isSync) {
          //   objCreate.sync = 1;
          //   objCreate.updatedAt = location.updatedAt || Date.now()
          // }
          //
          // Location
          //   .create(objCreate, () => {})
      } else {
        this.process();
      }
    } else {
      this.processing = false;
    }
  }
}

export default new LocationManager
