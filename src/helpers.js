import {locationServerAddr} from './config';
const rp = require('request-promise');
import CONSTANTS from './const';
import mailUtil from './mailUtil'

export function getNearest(location, radius, service,limit, cb) {
  const options = {
    method: 'POST',
    uri: `${locationServerAddr}/api/v2.0/shipper/get-nearest`,
    useQuerystring: true,
    body: {
      location,
      radius,
      service,
      limit
    },
    json: true // Automatically stringifies the body to JSON
  }

  rp(options)
    .then((result) => {
      if(result.code !== CONSTANTS.CODE.SUCCESS) {
        return cb(new Error(`St wrong with api`))
      }

      cb(null, result.data)
    })
    .catch((err) => {
      mailUtil
        .sendMail(` --- ERR getNearest ${locationServerAddr}/api/v2.0/shipper/get-nearest --- ${err}`);
      cb(err)
    })
}

export function createID() {
  function s4() {
    return Math.floor((1 + Math.random()) * 0x10000).toString(16).substring(1);
  }

  return s4() + s4() + s4() + '-' + s4() + s4() + s4() + '-' + s4() + s4() + s4();
}

export function getAliasPhone (phone) {
  let aliasPhone;

  if(phone.search(/(032|033|034|035|036|037|038|039)/i) === 0) { // Viettel
    aliasPhone = phone.replace("03", "016");
  } else if(phone.search(/(070|079|078|077|076)/i) === 0) { // Vinaphone
    if(phone.startsWith("079")) {
      aliasPhone = phone.replace("079", "0121");
    } else if(phone.startsWith("077")) {
      aliasPhone = phone.replace("077", "0122");
    } else {
      aliasPhone = phone.replace("07", "012");
    }
  } else if(phone.search(/(081|082|083|084|085)/i) === 0) { // Mobiphone
    if(phone.startsWith("081")) {
      aliasPhone = phone.replace("081", "0127");
    } else if(phone.startsWith("082")) {
      aliasPhone = phone.replace("082", "0129");
    } else {
      aliasPhone = phone.replace("08", "012");
    }
  } else if(phone.search(/(059)/i) === 0) { // Gmobile
    aliasPhone = phone.replace("059", "0199");
  } else if(phone.search(/(056|058)/i) === 0) { // Vietnammobile
    aliasPhone = phone.replace("05", "018");
  }

  return aliasPhone;
}

export function convertToNewPhone(phone) {
  phone = phone.trim();
  let newPhone;
  if(phone.search(/(0162|0163|0164|0165|0166|0167|0168|0169)/i) === 0) { // Viettel
    newPhone =  phone.replace("016", "03");
  } else if(phone.search(/(0123|0124|0125|0127|0129)/i) === 0) { // Vinaphone
    if(phone.startsWith("0127")) {
      newPhone = phone.replace("0127", "081");
    } else if(phone.startsWith("0129")) {
      newPhone = phone.replace("0129", "082");
    } else {
      newPhone = phone.replace("012", "08");
    }
  } else if(phone.search(/(0120|0121|0122|0126|0128)/i) === 0) { // Mobiphone
    if(phone.startsWith("0121")) {
      newPhone = phone.replace("0121", "079");
    } else if(phone.startsWith("0122")) {
      newPhone = phone.replace("0122", "077");
    } else {
      newPhone = phone.replace("012", "07");
    }
  } else if(phone.search(/(0186|0188)/i) === 0) { // Vietnammobile
    newPhone = phone.replace("018", "05");
  } else if(phone.search(/(0199)/i) === 0) { // Gmobile
    newPhone = phone.replace("0199", "059");
  }

  return newPhone;
}
