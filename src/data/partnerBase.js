var PartnerBase = new mongoose.Schema({
  partner : {type: mongoose.Schema.Types.ObjectId, ref: 'PartnerMember'},
  email: {type: String},
  phone: {type: String},
  name: {type: String},
  address: {type: String},
  website: {type: String},
  description: {type: String},
  active: {type: Number},
  status: {type: Number},
  location: {type: mongoose.Schema.Types.Mixed},
  workingTime: {type: mongoose.Schema.Types.Mixed},
  region: {type: String},
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, {versionKey: false});

// module.exports = mongoose.model('partnerbase', PartnerBase);
module.exports = mongoConnections('master').model('partnerbase', PartnerBase);
