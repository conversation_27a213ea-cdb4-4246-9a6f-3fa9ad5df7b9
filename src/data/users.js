var UserSchema = new mongoose.Schema({

    email : { type: String},
    password : { type: String},
    name: {
        first: { type:String },
        last: { type:String },
    },
    facebook: {
        id: {type: String},
        name: {type: String},
        email: {type: String},
        phone: {type: String},
        token: {type: String}
    },
    access_level : { type: Number},
    access_token: {type: String},
    created: { type: Date, default: Date.now },

}, {id: false, versionKey: 'v'});

// module.exports = mongoose.model('User', UserSchema);
module.exports = mongoConnections('master').model('User', UserSchema);
