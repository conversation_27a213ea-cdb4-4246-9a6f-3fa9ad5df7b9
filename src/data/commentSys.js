/**
 * Created by <PERSON><PERSON> on 2/4/2017.
 */
var CommentSystemSchema = new mongoose.Schema({
    comment_id: {
        type: Number
    },
    feed_id: {
        type: Number
    },
    parent_comment_id: {
        type: Number
    },
    member: {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
    salary: {
        type: String
    },
    message: {
        type: String
    },
    phone: {
        type: String
    },
    created_time: {
        type: Number
    },
});

// module.exports = mongoose.model('CommentSystem', CommentSystemSchema);
module.exports = mongoConnections('master').model('CommentSystem', CommentSystemSchema);
