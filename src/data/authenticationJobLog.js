var AuthenticationJobLogs = new mongoose.Schema({
  job: { // id
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AuthenticationJob'
  },
  action: { //Define in const.js
    type: String
  },
  oldData: {
    type: mongoose.Schema.Types.Mixed,
  },
  newData: {
    type: mongoose.Schema.Types.Mixed,
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  supporter: {//Người đăng ký sử lý
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approver: {//Người duyệt cuối
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
}, { id: false, versionKey: 'v' });

module.exports = mongoConnections('master').model('AuthenticationJobLogs', AuthenticationJobLogs);