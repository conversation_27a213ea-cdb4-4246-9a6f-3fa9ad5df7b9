const ShipperAuthentication = new mongoose.Schema({
  member: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Member'
  },
  licensePlate : {
    type: String
  },
  brand : {
    type: String
  },
  color : {
    type: String
  },
  bike : {
    type: Number
  },
  carType : {
    type: String
  },
  identitySimCardImg: {
    type: String
  },
  identityCard: {
    type: String
  },
  photo: {
    type: String
  },
  name: {
    type: String
  },
  hasUniform: {
    type: Number,
    default: 0
  },
  birthday:{
    type: Number
  },
  identityNumber: {
    type: String
  },
  registrationMotor: {
    type: String
  },
  supporter: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  regionDistrict: {
    type: String
  },
  uniformPromote: {
    type: Number
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  receivedUniformAt: {
    type: Number
  },
  onlineAuthen: {
    type: Number,
    default: 0
  },
  region:{
    type: String
  },
  regionTransaction:{
    type: String
  },
  media: {
    type: String
  },
  moneyBack: {type: 'Number'},
  phonesRelation: {
    type: mongoose.Schema.Types.Mixed
  },
  identityCardInf: {
    type: mongoose.Schema.Types.Mixed
  },
  licenseCardInf: {
    type: mongoose.Schema.Types.Mixed
  },
  registrationMotorInf: {
    type: mongoose.Schema.Types.Mixed
  },
  registrationCarInf: {
    type: mongoose.Schema.Types.Mixed
  },
  signatureImg: {
    type: String
  },
}, {id: false, versionKey: false});


// module.exports = mongoose.model('ShipperAuthentication', ShipperAuthentication);
module.exports = mongoConnections('master').model('ShipperAuthentication', ShipperAuthentication);
