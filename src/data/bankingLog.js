var BankingLog = new mongoose.Schema({
    member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
    order_id: {type: String},
    amount: {type: String},
    transRef: {type: String},
    initialCoints: {type: Number},
    finalCoints: {type: Number},
    resultBank: {
        type: mongoose.Schema.Types.Mixed
    },
    createdAt: {
        type: Number,
        default: Date.now
    },
    status: {type: Number},
    error: {
        type: mongoose.Schema.Types.Mixed
    }
});


//module.exports = mongoose.model('BankingLog', BankingLog);
module.exports = mongoConnections('master').model('BankingLog', BankingLog);
