const _ = require('lodash')
const generate = require('nanoid/generate')

var MemberSchema = new mongoose.Schema({
    phone : {type: String},
    password: {type: String, default: ''},
    name: {type: String, default: ''},
    email: {type: String, default: ''},
    address: {type: String, default: ''},
    appName: {type: String},
    birthday: {
        day: { type: Number },
        month: { type: Number },
        year: {type : Number}
    },
    facebook: {
        id: {type: String},
        realId: {type: String},
        name: {type: String, default: ''},
        email: {type: String, default: ''},
        birthday: {type: String, default: ''},
        token: {type: String, default: ''},
        picture: {type: String, default: ''}
    },
    status: {type: Number, default: 0},
    shop:{
      isAuthen: {
        type: 'Number',
        default: 0
      },
      totalPostOS: {type: 'Number', default: 0},
      rateStar: {
        type: 'Number',
        default: 5
      }
    },
    ship:{
      isAuthen: {
        type: 'Number',
        default: 0
      },
      totalRides: {
        type: 'Number',
        default: 0
      },
      totalRejects: {
        type: 'Number',
        default: 0
      },
      rateStar: {
        type: 'Number',
        default: 5
      },
      totalPushOrders: {
        type: 'Number',
        default: 0
      },
      totalAcceptPushOrders: {
        type: 'Number',
        default: 0
      },
      acceptedPushOrders: {
        type: 'Number',
        default: 0
      },
      pushedOrders: {
        type: 'Number',
        default: 0
      },
      disablePush: {
        type: 'Number',
        default: 0
      },
      amountDeposit: {
        type: 'Number',
        default: 3000000
      }
    },
    staff: {
      isAuthen: {
        type: 'Number',
        default: 0
      },
      rateStar: {
        type: 'Number',
        default: 5
      },
      totalRides: {
        type: 'Number',
        default: 0
      }
    },
    cointsStaff: {
      type: 'Number',
      default: 0
    },
    realMoneyStaff: {
      type: 'Number',
      default: 0
    },
    realMoneyShopStaff: {
      type: 'Number',
      default: 0
    },
    coints: {
      type: 'Number',
      default: 0
    },
    realMoney: {
      type: 'Number',
      default: 0
    },
    realMoneyShop: {
      type: 'Number',
      default: 0
    },
    moneyCOD: {
      type: 'Number'
    },
    money: {
      type: 'Number'
    },
    expireTime: {
      type: 'Number',
      default: 0
    },
    blockUtil: {
      type: 'Number',
      default: 0
    },
    blockOrderUtil: {
      type: 'Number',
      default: 0
    },
    training: {
      type: 'Number',
      default: 0
    },
    point: {
      type: 'Number',
      default: 0
    },
    fromTickBox: {
      type: 'Number'
    },
    blockCreateOrder: {
      type: 'Number',
      default: 0
    },
    isBlockStore: {
      type: 'Number'
    },
    rejectCount: {
      type: 'Number',
      default: 0
    },
    disablePushUtil: {
      type: 'Number',
      default: 0
    },
    code: {type: String},
    type: {type: Number}, // 1 for admin, 0 is shipper, 2 is shop, 3: merchant, 4: staff
    region: {type: String},
    regionDistrict: {type: String},
    receivePushOrder: {type: Number, default: 0},
    memberToken: {type: String},
    createdAt: { type: Number, default: Date.now },
    location: { type: mongoose.Schema.Types.Mixed },
    updatedAt: {type: Number, default: Date.now }
}, {id: false, versionKey: 'v'})

MemberSchema.pre('save', function(next){
  let model = this
  attempToGenerate(model, next)
})


const attempToGenerate = (model, callback) => {
  let newCode =  generate('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ', 5)
  model.constructor.findOne({
    'code': newCode
  }).then((course) => {
    if(course){
      attempToGenerate(model, callback)
    } else {
      model.code = newCode
      callback();
    }
  }, (err) => {
    callback(err)
  })
}

MemberSchema.virtual('likerList.user', {
  ref: 'Member',
  localField: 'likerList.userId',
  foreignField: '_id'
});
MemberSchema.virtual('dislikerList.user', {
  ref: 'Member',
  localField: 'dislikerList.userId',
  foreignField: '_id'
});


MemberSchema.statics.updateLocation = function (memberToken, location, cb) {
  // !_.isFunction(cb) && cb = () => {}

  if(memberToken && _.isPlainObject(location) && _.isFinite(location.lat) && _.isFinite(location.lng)) {
    const query = {memberToken}
    const update = {
      updatedAt: Date.now(),
      location: {
        coordinates: [location.lng, location.lat],
        type: 'Point'
      }
    }

    this.update(query, update).exec()
  }
}

MemberSchema.statics.getNearest = function (location, distance, query, fields, cb) {
  // distance = _.isFinite(distance) ? distance : 400
  distance = 1300
  this
    .find(query, fields)
    .near('location', {
      center: {
        coordinates: [location.lng, location.lat],
        type: 'Point'
      },
      maxDistance: distance
    })
    .lean()
    .exec(cb)
}

MemberSchema.statics.get = function(_id, fields, cb) {
  _.isFunction(fields) && ([fields, cb] = [null, fields])
  if(!_id) {
    cb(null, null)
  }

  this.findById(_id, fields).lean().exec(cb)
}

MemberSchema.statics.increaseCoint = function(_id, coint, cb) {
  this
    .findOneAndUpdate({_id}, {$inc: {coints: coint}}, {new: true})
    .exec(cb);
}


//module.exports = mongoose.model('Member', MemberSchema);
module.exports = mongoConnections('master').model('Member', MemberSchema);
