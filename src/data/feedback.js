var FeedBackSchema = new mongoose.Schema({
  member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
  image: {
    type: mongoose.Schema.Types.Mixed
  },
  message: {
    type: 'String'
  },
  type: {
    type: Number
  },
  note: {
    type: String
  },
  orders: {
    type:  [mongoose.Schema.Types.ObjectId]
  },
  jobId: {
    type: mongoose.Schema.Types.ObjectId
  },
  orderStore: {
    type:  [mongoose.Schema.Types.ObjectId]
  },
  mode: {
    type: String
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  region: {
    type: String
  },
  status: {
    type: Number,
    default: 0
  },
  mode: {
    type: String,
  }
}, {id: false, versionKey: false});

// module.exports = mongoose.model('FeedBack', FeedBackSchema);
module.exports = mongoConnections('master').model('FeedBack', FeedBackSchema);
