var LoyalCustomerSchema = new mongoose.Schema({
  member: { type: mongoose.Schema.Types.ObjectId, ref: 'Member' },
  status: { type: Number, default: 1 },
  location: { type: mongoose.Schema.Types.Mixed },
  name: { type: String },
  subName: { type: String, default: '' },
  nameSender: { type: String, default: '' },
  phoneSender: { type: String, default: '' },
  nameMain: { type: String, default: '' },
  nameSecondary: { type: String, default: '' },
  createdAt: { type: Number, default: Date.now },
  updatedAt: { type: Number, default: Date.now },
}, { id: false, versionKey: false });

// module.exports = mongoose.model('LoyalCustomer', LoyalCustomerSchema);
module.exports = mongoConnections('master').model('LoyalCustomer', LoyalCustomerSchema);
