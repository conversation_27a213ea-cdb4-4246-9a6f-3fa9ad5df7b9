var NotificationWebSchema = new mongoose.Schema({
  platform : String,
  notify_token: String,
  newVersion: {
    type: Number
  },
  member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
  isSafari: {type: Number, default: 0},
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number
  }
}, {id: false, versionKey: 'v'});


// module.exports = mongoose.model('Notification', NotificationWebSchema);
module.exports = mongoConnections('master').model('NotificationWeb', NotificationWebSchema);
