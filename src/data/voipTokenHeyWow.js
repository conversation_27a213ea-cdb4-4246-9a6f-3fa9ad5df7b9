var voipTokenHeyWow = new mongoose.Schema({
  member: { type: mongoose.Schema.Types.ObjectId, ref: 'Member' },
  notify_token: {
    type: String
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { versionKey: false });

// module.exports = mongoose.model('voipTokenHeyWow', voipTokenHeyWow);
module.exports = mongoConnections('master').model('voipTokenHeyWow', voipTokenHeyWow);
