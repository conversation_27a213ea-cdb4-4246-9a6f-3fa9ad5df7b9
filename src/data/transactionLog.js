const TransactionLog = new mongoose.Schema({
  member: {
    type: mongoose.Schema.Types.ObjectId
  },
  region: {
    type: String,
    default: ''
  },
  data: {
    type: mongoose.Schema.Types.Mixed
  },
  message: {
    type: String
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
}, {id: false, versionKey: false});


// module.exports = mongoose.model('TransactionLog', TransactionLog);
module.exports = mongoConnections('master').model('TransactionLog', TransactionLog);
