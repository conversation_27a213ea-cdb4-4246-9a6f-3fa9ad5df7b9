var DefaultLocationSchema = new mongoose.Schema({
  member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
  status: {type: Number, default: 1},
  location: { type: mongoose.Schema.Types.Mixed },
  name: {type: String},
  nameDefault: {type: String, default: ''},
  subNameDefault: {type: String, default: ''},
  nameSender: {type: String, default: ''},
  phoneSender: {type: String, default: ''},
  nameMain: {type: String, default: ''},
  nameSecondary: {type: String, default: ''},
  createdAt: {type: Number, default: Date.now},
  updatedAt: {type: Number, default: Date.now},
}, {id: false, versionKey: false});

// module.exports = mongoose.model('DefaultLocation', DefaultLocationSchema);
module.exports = mongoConnections('master').model('DefaultLocation', DefaultLocationSchema);
