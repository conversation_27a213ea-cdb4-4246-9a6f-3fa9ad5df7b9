var CardLog = new mongoose.Schema({
    member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
    pin: {type: String},
    serial: {type: String},
    type: {type: String},
    resultCard: {
        type: mongoose.Schema.Types.Mixed
    },
    createdAt: {
        type: Number,
        default: Date.now
    },
    error: {
        type: mongoose.Schema.Types.Mixed
    }
});


//module.exports = mongoose.model('CardLog', CardLog);
module.exports = mongoConnections('master').model('CardLog', CardLog);
