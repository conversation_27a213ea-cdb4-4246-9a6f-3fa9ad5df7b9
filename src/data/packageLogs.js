var PackageLog = new mongoose.Schema({
    member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
    initialCoints: {type: Number},
    initialRealMoney: {type: Number},
    finalRealMoney: {type: Number},
    finalCoints: {type: Number},
    initialExpireTime: {type: Number},
    finalExpireTime: {type: Number},
    moreTime: {type: Number},
    price: {type: Number},
    bonus: {type: Number},
    type: {type: String},
    createdAt: {
        type: Number,
        default: Date.now
    },
    error: {
        type: mongoose.Schema.Types.Mixed
    }
});


// module.exports = mongoose.model('PackageLog', PackageLog);
module.exports = mongoConnections('master').model('PackageLog', PackageLog);
