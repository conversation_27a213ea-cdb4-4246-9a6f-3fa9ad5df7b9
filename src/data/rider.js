const async = require('async');

const Rider = new mongoose.Schema({
    member: {
      type: mongoose.Schema.Types.ObjectId
    },
    serviceRunning: [{
      type: mongoose.Schema.Types.ObjectId
    }],
    serviceRegisted: [{
      type: mongoose.Schema.Types.ObjectId
    }],
    createdAt: {
      type: Number,
      default: Date.now
    }
});


// module.exports = mongoose.model('Rider', Rider);
module.exports = mongoConnections('master').model('Rider', Rider);
