import ms from 'ms';

var FeedSchema = new mongoose.Schema({
    member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
    feed_id: {type: String},
    createdAt: {
        type: Date,
        expires: ms('2 days')/1000 // 2 days in seconds
    }
});

FeedSchema.virtual('feedInf', {
    ref: 'NewFeeds',
    localField: 'feed_id',
    foreignField: 'id'
});



// module.exports = mongoose.model('Feed', FeedSchema);
module.exports = mongoConnections('master').model('Feed', FeedSchema);
