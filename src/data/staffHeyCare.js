const async = require('async');

const StaffHeyCare = new mongoose.Schema({
  member: {
    type: mongoose.Schema.Types.ObjectId
  },
  serviceRunning: [{
    type: mongoose.Schema.Types.ObjectId
  }],
  serviceRegisted: [{
    type: mongoose.Schema.Types.ObjectId
  }],
  updatedAt: {
    type: Number,
    default: Date.now
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
});

// module.exports = mongoose.model('StaffHeyCare', StaffHeyCare);
module.exports = mongoConnections('master').model('StaffHeyCare', StaffHeyCare);
