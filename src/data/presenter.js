const PresenterSchema = new mongoose.Schema({
    member : {
      type: mongoose.Schema.Types.ObjectId
    },
    presenter : {
      type: mongoose.Schema.Types.ObjectId
    },
    presenterCode: {
      type: String
    },
    type: {
      type: Number
    },
    region: {
      type: String
    },
    status: {
      type: Number
    },
    createdAt: {
      type: Number,
      default: Date.now
    }
}, {id: false, versionKey: false});

// module.exports = mongoose.model('presenter', PresenterSchema);
module.exports = mongoConnections('master').model('presenter', PresenterSchema);
