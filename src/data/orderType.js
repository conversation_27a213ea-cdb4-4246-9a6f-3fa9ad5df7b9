const mongoose = require('mongoose');
const Schema = mongoose.Schema;

var OrderType = new mongoose.Schema({
  name: {
    type: String
  },
  message: {
    type: String
  },
  icon: {
    type: String
  },
  showTip: {
    type: Number
  },
  hideList: {
    type: Number
  },
  forceMoney: {
    type: Number
  },
  carType : {
    type: Number
  },
  hireDriver : {
    type: Number
  },
  maxDeposit: {
    type: Number
  },
  depositEnsure: {
    type: Number
  },
  moneyStrategy: {
    type: Schema.Types.Mixed
  },
  forceAuthen: {
    type: Number
  },
  multipleDes: {
    type: Number
  },
  region: {
    type: Schema.Types.Mixed
  },
  order: {
    type: Number,
    default: 0
  },
  focus: {
    type: Number,
    default: 0
  },
  status: {
    type: Number
  },
  service: {
    type: Schema.Types.ObjectId
  }
}, {versionKey: false})

// module.exports = mongoose.model('ordertype', OrderType);
module.exports = mongoConnections('master').model('ordertype', OrderType);
