var NotificationsSchema = new mongoose.Schema({
    platform : String,
    notify_token: String,
    newVersion: {
      type: Number
    },
    member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
    createdAt: {
    	type: Number,
    	default: Date.now
    },
    updatedAt: {
    	type: Number
    }
}, {id: false, versionKey: 'v'});


// module.exports = mongoose.model('Notification', NotificationsSchema);
module.exports = mongoConnections('master').model('Notification', NotificationsSchema);
