const _ = require('lodash')

var GhostMemberSchema = new mongoose.Schema({
    name: {type: String},
    facebook: {
        id: {type: String},
        realId: {type: String},
    },
    status: {type: Number, default: 0},
    likes: {
      type: 'Number',
      default: 0
    },
    likerList:[
      {
        createdAt:{type: Number, default: Date.now },
        userId:{type: String,required: true}
      }
    ],
    dislikes: {
      type: 'Number',
      default: 0
    },
    dislikerList:[
      {
        createdAt:{type: Number, default: Date.now },
        userId:{type: String,required: true}
      }
    ],
    shop:{
      totalPost: {type: 'Number',default: 0},
      postList:[
        {
          postId: {type: String, required: true},
          createdAt: {type: Number, default: Date.now }
        }
      ]
    },
    ship:{
      totalComment: {type: 'Number',default: 0}
    },
    granted: <PERSON><PERSON><PERSON>,
    createdAt: { type: Number, default: Date.now },
    updatedAt: {type: Number, default: Date.now }
}, {id: false, versionKey: 'v'})

GhostMemberSchema.virtual('likerList.user', {
  ref: 'Member',
  localField: 'likerList.userId',
  foreignField: '_id'
});
GhostMemberSchema.virtual('dislikerList.user', {
  ref: 'Member',
  localField: 'dislikerList.userId',
  foreignField: '_id'
});

GhostMemberSchema.statics.get = function(_id, fields, cb) {
  _.isFunction(fields) && ([fields, cb] = [null, fields])
  if(!_id) {
    cb(null, null)
  }

  this.findById(_id, fields).lean().exec(cb)
}


// module.exports = mongoose.model('GhostMembers', GhostMemberSchema);
module.exports = mongoConnections('master').model('GhostMembers', GhostMemberSchema);
