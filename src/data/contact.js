const async = require('async');

const Contact = new mongoose.Schema({
  member: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Member'
  },
  data: {
    type: mongoose.Schema.Types.Mixed
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number
  }
}, {id: false, versionKey: false, strict: false});

// module.exports = mongoose.model('Contact', Contact);
module.exports = mongoConnections('master').model('Contact', Contact);
