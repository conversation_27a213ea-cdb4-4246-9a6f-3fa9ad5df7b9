const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const PartnerHeyCare = new mongoose.Schema({
  member: {
    type: Schema.Types.ObjectId
  },
  cashbackPercent: {
    type: Number
  },
  cashbackPercentCreate: {
    type: Number
  },
  status: {
    type: Number
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
}, {id: false, versionKey: false, strict: false});

module.exports = mongoConnections('master').model('PartnerHeyCare', PartnerHeyCare);
