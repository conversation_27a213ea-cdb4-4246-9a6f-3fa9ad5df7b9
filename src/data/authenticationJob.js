var AuthenticationJob = new mongoose.Schema({
  member: { // id phụ
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Member'
  },
  phone: { // id chính
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  note: {
    type: String,
    default: ''
  },
  reason: {
    type: String,
    default: ''
  },
  point: { //Thang điểm 10đ từ sup tìm thông tin
    type: Number,
    default: 0
  },
  status: { //0-xoá
    type: Number,
    default: 1
  },
  jobStatus: {//0-Free 1-<PERSON><PERSON> đăng ký 2-Chờ duyệt 3-Đã duyệt
    type: Number,
    default: 0
  },
  potentialPhoneStatus: {//0-<PERSON><PERSON> bổ sung thông tin 1-<PERSON><PERSON> thông tin 2-<PERSON><PERSON> bổ sung thông tin 3-Cần theo dõi thêm
    type: Number,
    default: 0
  },
  authenStatus: {//0-Chờ duyệt 1-<PERSON><PERSON> xác thực 2-<PERSON><PERSON> chối xác thực
    type: Number,
    default: 0
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  potentialPhone: {//Nguồn thông tin
    type: mongoose.Schema.Types.ObjectId,
    ref: 'PotentialPhone'
  },
  supporter: {//Người đăng ký sử lý
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approver: {//Người duyệt cuối
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
}, { id: false, versionKey: 'v' });

module.exports = mongoConnections('master').model('AuthenticationJob', AuthenticationJob);