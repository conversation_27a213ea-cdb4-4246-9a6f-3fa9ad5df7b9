var Cookie = new mongoose.Schema({
    member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
    cookies: {type: mongoose.Schema.Types.Mixed},
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
}, {versionKey: false});


// module.exports = mongoose.model('<PERSON><PERSON>', <PERSON><PERSON>);
module.exports = mongoConnections('master').model('<PERSON><PERSON>', <PERSON><PERSON>);
