import CONSTANTS from '../const.js';
var eBillLogSchema = new mongoose.Schema({
    member: {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
    supporter: {
        type: mongoose.Schema.Types.ObjectId, ref: 'User'
    },
    type: {
        type: Number,
    },
    createdAt: {
        type: Number,
        default: Date.now
    },
    data: {
        type: mongoose.Schema.Types.Mixed
    }
  }, {id: false, versionKey: false});

  // module.exports = mongoose.model('eBill', eBillSchema);
  module.exports = mongoConnections('master').model('EBillLog', eBillLogSchema);
