var MembershipPackageSchema = new mongoose.Schema({
  image: {
    type: String
  },
  price: {
    type: Number
  },
  priceHeywow: {
    type: Number
  },
  timeBuyPerUser: {
    type: Number
  },
  timeBuyLeft: {
    type: Number
  },
  title: {
    type: String
  },
  subTitle: {
    type: String
  },
  detail: {
    type: mongoose.Schema.Types.Mixed
  },
  config: {
    type: mongoose.Schema.Types.Mixed
  },
  region: {
    type: mongoose.Schema.Types.Mixed
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  order: {
    type: Number
  },
  status: {
    type: Number
  }
}, { id: false, versionKey: false })


module.exports = mongoConnections('master').model('MembershipPackage', MembershipPackageSchema);
