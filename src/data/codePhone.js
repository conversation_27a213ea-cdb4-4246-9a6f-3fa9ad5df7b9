var CodePhoneSchema = new mongoose.Schema({
  phone: {
    type: String
  },
  token: {
    type: String
  },
  code: {
    type: String
  },
  ip: {
    type: String
  },
  status: {
    type: Number,
    default: 0
  },
  messageId: {
    type: String
  },
  provider: {
    type: String
  },
  region: {
    type: String
  },
  responseVMG: {
    type: mongoose.Schema.Types.Mixed
  },
  responseSPEED: {
    type: mongoose.Schema.Types.Mixed
  },
  responseFPT: {
    type: mongoose.Schema.Types.Mixed
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number
  }
}, {id: false, versionKey: false});

module.exports = mongoConnections('master').model('CodePhone', CodePhoneSchema);
