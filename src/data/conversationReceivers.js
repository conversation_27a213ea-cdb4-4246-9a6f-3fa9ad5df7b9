const ConversationReceiver = new mongoose.Schema({
    name: {
      type: String,
      default: ''
    },
    users: [{
      type: String
    }],
    isGroup: {
      type: Number,
      default: 0
    },
    latestMessage: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Message'
    },
    orderId: {
      type: mongoose.Schema.Types.ObjectId
    },
    sequence: {
      type: Number
    },
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
});


// module.exports = mongoose.model('Conversation', Conversation);
module.exports = mongoConnections('master').model('ConversationReceiver', ConversationReceiver);
