var SavedNotification = new mongoose.Schema({
    title: {type: String},
    message: {type: String},
    roles: {type: Array},
    data: {type: mongoose.Schema.Types.Mixed},
    seen: {type: Array, default:[]},
    status: {type: Number, default:1},
    createdAt: {type: Number, default: Date.now}
}, {versionKey: false});


// module.exports = mongoose.model('SavedNotification', SavedNotification);
module.exports = mongoConnections('master').model('SavedNotification', SavedNotification);
