var BusinessReportLog = new mongoose.Schema({
    member: {type: mongoose.Schema.Types.ObjectId},
    business: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BusinessModel'
    },
    items: {type: [mongoose.Schema.Types.ObjectId]},
    report: {type: mongoose.Schema.Types.Mixed},
    createdAt: {
      type: Number,
      default: Date.now
    }
},{id: false, versionKey: false, strict: false});

module.exports = mongoConnections('master').model('BusinessReportLog', BusinessReportLog);
