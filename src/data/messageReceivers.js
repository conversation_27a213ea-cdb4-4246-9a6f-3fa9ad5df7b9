const MessageReceiver = new mongoose.Schema({
    conversation: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Conversation'
    },
    message: {
      type: String,
      default: ''
    },
    seen: {
      type: Number,
      default: 0
    },
    seenAt: {
      type: Number,
      default: Date.now
    },
    createdAt: {
      type: Number,
      default: Date.now
    },
    senderId: {
      type: String
    },
});


// module.exports = mongoose.model('Message', Message);
module.exports = mongoConnections('master').model('MessageReceiver', MessageReceiver);
