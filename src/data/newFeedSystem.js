var NewFeedSystemSchema = new mongoose.Schema({
    feed_id: {
        type: String
    },
    feed_facebook_id: {
        type: String
    },
    group_id: {
        type: String
    },
    member: {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
    origin_place: {
        "address": String,
        "geometry": {
            lat: String,
            long: String,
        }
    },
    destination_places: [{
        "address": String,
        "geometry": {
            lat: String,
            long: String,
        }
    }],
    distance: {
        type: String
    },
    deposit: {
        type: String
    },
    salary: {
        type: String
    },
    note: {
        type: String
    },
    phone: {
        type: String
    },

    created_time: {
        type: Number
    },
    lasttime_push: {
        type: Number
    },
    system_push_count: {
        type: Number
    },
    shop_push_count: {
        type: Number
    },
    status: {
        type: Number // 0-Processing, 1-Done, 3-Deleted
    },
    error: {
        type: String
    },
    total_comment: {
        type: Number
    },
    lasttime_comment: {
        type: Number
    },
    type: {
        type: Number // 0-FB, 1-System
    }
});



// module.exports = mongoose.model('NewFeedSystem', NewFeedSystemSchema);
module.exports = mongoConnections('master').model('NewFeedSystem', NewFeedSystemSchema);
