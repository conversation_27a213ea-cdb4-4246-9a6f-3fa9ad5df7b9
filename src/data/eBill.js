import CONSTANTS from '../const.js';
var eBillSchema = new mongoose.Schema({
    member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
    status: {
        type: Number,
        default: CONSTANTS.STATUS_EBILL.PENDING
    },
    message: {
        type: String,
    },
    taxCode: {
        type: String,
    },
    companyName: {
        type: String,
    },
    representative: {
        type: String,
    },
    email: {
        type: String,
    },
    businessAddress: {
        type: String,
    },
    receiveAddress: {
        type: String,
    },
    registeredPhoneNumber: {
        type: Array,
    },
    note: {
        type: String,
    },
    createdAt: {
        type: Number,
        default: Date.now
    },
    updatedAt: {
        type: Number,
        default: Date.now
    },
    active: {
        type: Number,
        default: 1
    },
    admin: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Member'
    }
  }, {id: false, versionKey: false});

  // module.exports = mongoose.model('eBill', eBillSchema);
  module.exports = mongoConnections('master').model('EBill', eBillSchema);
