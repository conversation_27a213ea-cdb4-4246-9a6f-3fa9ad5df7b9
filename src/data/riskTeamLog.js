const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const RiskTeamLog = new mongoose.Schema({
  message: {
    type:String
  },
  data: {
    type: Schema.Types.Mixed
  },
  resultPush: {
    type: Schema.Types.Mixed
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
}, {id: false, versionKey: false, strict: false});

module.exports = mongoConnections('master').model('RiskTeamLog', RiskTeamLog);
