const async = require('async');
const ms = require('ms');
import {environment} from '../config';

const Config = new mongoose.Schema({
  type: {
    type: Number
  },
  region: {
    type: mongoose.Schema.Types.Mixed
  }
}, {id: false, versionKey: false, strict: false});

Config.statics.get = function (type, region, cb) {
  let config;
  const getConfigExactRegion = (next) => {
    if(!region) {
      return next();
    }

    let func = this.findOne({type, 'region.allow': region}).lean()

    if (environment === 'production') {
      func = func.cache(ms('5m')/1000, `config:${type}:${region}`)
    }

    func
      .exec((err, result) => {
        if(err) {
          return next(err);
        }

        config = result;

        next();
      })
  }

  const getDefaultConfig = (next) => {
    if(config) {
      return next();
    }

    const query = {
      type,
      'region.allow': 'all'
    }

    if(region) {
      query['region.deny'] = {
        $ne: region
      }
    }

    let func = this.findOne(query).lean()

    if (environment === 'production') {
      func = func.cache(ms('5m')/1000, `config:${type}:all:except:${region ? region : ''}`)
    }

    func
      .exec((err, result) => {
        if(err) {
          return next(err);
        }

        config = result;

        next();
      })
  }

  async.waterfall([
    getConfigExactRegion,
    getDefaultConfig
  ], (err) => {
    if(err) {
      return cb(err);
    }

    cb(null, config);
  })
}

Config.statics.getData = function (obj, cb) {
  let config;

  const {type, region, regionDistrict} = obj

  const getConfigExactRegionDistrict = (next) => {
    if(!regionDistrict) {
      return next();
    }

    let func = this.findOne({type, 'region.allow': regionDistrict}).lean()

    if (environment === 'production') {
      func = func.cache(ms('5m')/1000, `config:${type}:${regionDistrict}`)
    }

    func
      .exec((err, result) => {
        if(err) {
          return next(err);
        }

        config = result;

        next();
      })
  }

  const getConfigExactRegion = (next) => {
    if(!region || config) {
      return next();
    }

    let func = this.findOne({type, 'region.allow': region}).lean()

    if (environment === 'production') {
      func = func.cache(ms('5m')/1000, `config:${type}:${region}`)
    }

    func
      .exec((err, result) => {
        if(err) {
          return next(err);
        }

        config = result;

        next();
      })
  }

  const getDefaultConfig = (next) => {
    if(config) {
      return next();
    }

    const query = {
      type,
      'region.allow': 'all'
    }

    if(region) {
      query['region.deny'] = {
        $ne: region
      }
    }

    let func = this.findOne(query).lean()

    if (environment === 'production') {
      func = func.cache(ms('5m')/1000, `config:${type}:all:except:${region ? region : ''}`)
    }

    func
      .exec((err, result) => {
        if(err) {
          return next(err);
        }

        config = result;

        next();
      })
  }

  async.waterfall([
    getConfigExactRegionDistrict,
    getConfigExactRegion,
    getDefaultConfig
  ], (err) => {
    if(err) {
      return cb(err);
    }

    cb(null, config);
  })
}

// module.exports = mongoose.model('Config', Config);
module.exports = mongoConnections('master').model('Config', Config);
