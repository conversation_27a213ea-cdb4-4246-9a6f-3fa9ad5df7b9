var NewFeedSchema = new mongoose.Schema({

    message: String,
    permalink_url: String,
    from: {
        name: String,
        id: String
    },
    created_time: String,
    id: String,
    phone: String,
    salary: Number, // unit k
    deposit: Number, // unit k
    origin_place : {
      address: String,
      geometry:[], // [[lat,long],[lat,long]]
    },
    destination_place : {
        address: String,
        geometry:[], // [[lat,long],[lat,long]]
    },
    system_created_time: Number,
    isOpen: Boolean
});



// module.exports = mongoose.model('NewFeeds', NewFeedSchema);
module.exports = mongoConnections('sub').model('NewFeeds', NewFeedSchema);
