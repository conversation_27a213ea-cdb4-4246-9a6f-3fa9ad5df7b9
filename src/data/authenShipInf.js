var AuthenShipInf = new mongoose.Schema({
  member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
  name: {
    type: mongoose.Schema.Types.Mixed
  },
  avatar: {
    type: mongoose.Schema.Types.Mixed
  },
  email: {
    type: mongoose.Schema.Types.Mixed
  },
  facebookImg: {
    type: mongoose.Schema.Types.Mixed
  },
  facebookUrl: {
    type: mongoose.Schema.Types.Mixed
  },
  identityFacadeImg: {
    type: mongoose.Schema.Types.Mixed
  },
  identityBacksideImg: {
    type: mongoose.Schema.Types.Mixed
  },
  identityNum: {
    type: mongoose.Schema.Types.Mixed
  },
  identityDate: {
    type: mongoose.Schema.Types.Mixed
  },
  identityPlace: {
    type: mongoose.Schema.Types.Mixed
  },
  dob: {
    type: mongoose.Schema.Types.Mixed
  },
  drivingLicenseFacadeImg: {
    type: mongoose.Schema.Types.Mixed
  },
  drivingLicenseBacksideImg: {
    type: mongoose.Schema.Types.Mixed
  },
  drivingLicenseNum: {
    type: mongoose.Schema.Types.Mixed
  },
  registrationLicenseFacadeImg: {
    type: mongoose.Schema.Types.Mixed
  },
  registrationLicenseBacksideImg: {
    type: mongoose.Schema.Types.Mixed
  },
  motobikeFacadeImg: {
    type: mongoose.Schema.Types.Mixed
  },
  motobikeBacksideImg: {
    type: mongoose.Schema.Types.Mixed
  },
  avatarWithIdentity: {
    type: mongoose.Schema.Types.Mixed
  },
  licensePlate: {
    type: mongoose.Schema.Types.Mixed
  },
  brand: {
    type: mongoose.Schema.Types.Mixed
  },
  color: {
    type: mongoose.Schema.Types.Mixed
  },
  address: {
    type: mongoose.Schema.Types.Mixed
  },
  note: {
    type: mongoose.Schema.Types.Mixed
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  paymentAt: {
    type: Number
  },
  status: {
    type: Number,
    default: 0
  },
  message: {
    type:  mongoose.Schema.Types.Mixed
  },
  region:{
    type: String
  },
  sizeUniform:{
    type: String
  },
  accommodation: {
    type: mongoose.Schema.Types.Mixed
  },
  phoneRelative1: {
    type: mongoose.Schema.Types.Mixed
  },
  phoneRelative2: {
    type: mongoose.Schema.Types.Mixed
  },
  currentStep: {
    type: Number,
    default:0
  },
  statusContract: {
    type: Number,
    default: -2
  },
  contract: {
    type: mongoose.Schema.Types.Mixed
  },
  signContract: {
    type: mongoose.Schema.Types.Mixed
  },
  statusPayment: {
    type: Number,
    default: -2
  },
  bill: {
    type: mongoose.Schema.Types.Mixed
  },
  receiveUniformAddress: {
    type: mongoose.Schema.Types.Mixed
  },
  statusTraining: {
    type: Number,
    default: -2
  },
  statusUniform: {
    type: Number,
    default: -2
  },
  requireUniform: {
    type: Number,
    default:0
  },
  requireBuyUniform: {
    type: Number,
    default:0
  },
  shipperRejected: {
    type: Number,
    default: 0
  },
  isTaxi: {
    type: Number,
    default: 0
  },
  uniform: {
    type: mongoose.Schema.Types.Mixed
  },
  hasDrivingLicense: {
    type: Number,
    default: 0
  },
  hasRegistrationLicense: {
    type: Number,
    default: 0
  },
  hasIdentity: {
    type: Number,
    default: 0
  }
}, {id: false, versionKey: false});

module.exports = mongoConnections('master').model('AuthenShipInf', AuthenShipInf);
