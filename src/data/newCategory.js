const NewCategory = new mongoose.Schema({
  title: {
    type: String,
    default: ''
  },
  active: {
    type: Number,
    default: 1
  },
  region: {
    type: mongoose.Schema.Types.Mixed
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
});


NewCategory.statics.list = function(region, from, cb) {
  this
    .find({
      active: 1,
      $or: [
        {
          'region.allow': region
        },
        {
          'region.allow': 'all',
          'region.deny': {
            $ne: region
          }
        }
      ]
    }, "-createdAt -active -region")
    .sort({createdAt: -1})
    .limit(3)
    .skip(from)
    .lean()
    .exec(cb)
}

// module.exports = mongoose.model('NewCategory', NewCategory);
module.exports = mongoConnections('master').model('NewCategory', NewCategory);
