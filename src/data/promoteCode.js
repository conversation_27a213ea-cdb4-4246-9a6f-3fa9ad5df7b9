const PromoteCode = new mongoose.Schema({
  code: {
    type: String
  },
  title: {
    type: String
  },
  icon: {
    type: String
  },
  status: {
    type: Number,
    default: 0
  },
  vnpay: {
    type: Number
  },
  alwaysShow: {
    type: Number
  },
  strategy: {
    type: mongoose.Schema.Types.Mixed
  },
  condition: {
    type: mongoose.Schema.Types.Mixed
  },
  notification: {
    type: mongoose.Schema.Types.ObjectId
  },
  service: {
    type: mongoose.Schema.Types.Mixed
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false });


PromoteCode.statics.get = function (query, cb) {
  query = query || {};
  query.status = 1;

  this
    .findOne(query)
    .sort("-createdAt")
    .lean()
    .exec(cb)
}

module.exports = mongoConnections('master').model('PromoteCode', PromoteCode);
