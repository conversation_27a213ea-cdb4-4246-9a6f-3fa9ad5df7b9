var ChangePhoneLogModel = new mongoose.Schema({
    member : {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Member'
    },
    oldPhone: String,
    newPhone: String,
    createdAt: {
      type: Number,
      default: Date.now
    }
}, {id: false, versionKey: false});


// module.exports = mongoose.model('ChangePhoneLog', ChangePhoneLogModel);
module.exports = mongoConnections('master').model('ChangePhoneLog', ChangePhoneLogModel);
