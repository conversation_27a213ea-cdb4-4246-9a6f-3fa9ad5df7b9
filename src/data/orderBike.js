var OrderBike = new mongoose.Schema({
  shipper: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Member'
  },
  shop: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Member'
  },
  origin_place: { type: mongoose.Schema.Types.Mixed },
  destination_places: { type: mongoose.Schema.Types.Mixed },
  promote: {
    type: mongoose.Schema.Types.ObjectId
  },
  salary: {
    type: Number,
    default: 0
  },
  salaryStrategy: {
    type: mongoose.Schema.Types.Mixed
  },
  serviceCharge: {
    type: Number,
    default: 0
  },
  distance: {
    type: Number,
    default: 0
  },
  note: {
    type: String,
    default: ""
  },
  phone: {
    type: String,
    default: ""
  },
  region: {
    type: String,
    default: ""
  },
  status: {
    type: Number,
    default: 0
  },
  rejects: {
    type: [String],
    default: []
  },
  takeOrderInf: {
    type: mongoose.Schema.Types.Mixed
  },
  doneOrderInf: {
    type: mongoose.Schema.Types.Mixed
  },
  cantTakeOrderInf: {
    type: mongoose.Schema.Types.Mixed
  },
  startReturningOrderInf: {
    type: mongoose.Schema.Types.Mixed
  },
  returnDoneOrderInf: {
    type: mongoose.Schema.Types.Mixed
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  acceptedAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  hasCalled: {
    type: Number,
    default: 0
  },
  shopHasCalled: {
    type: Number,
    default: 0
  },
  hasMessage: {
    type: Number,
    default: 0
  },
  shopHasMessage: {
    type: Number,
    default: 0
  },
  hideShipper: {
    type: Number,
    default: 0
  },
  needRating:{
    type: Number,
    default: 1
  },
  needRatingUniform:{
    type: Number,
    default: 1
  },
  orderType: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'OrderType'
  },
  service: {
    type: mongoose.Schema.Types.ObjectId
  }
}, {versionKey: false})


// module.exports = mongoose.model('orderbike', OrderBike);
module.exports = mongoConnections('master').model('orderbike', OrderBike);
