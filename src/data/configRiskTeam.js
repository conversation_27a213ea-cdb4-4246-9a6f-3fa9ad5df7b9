const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const ConfigRiskTeam = new mongoose.Schema({
  listUsers: {
    type: Schema.Types.Mixed
  },
  listRiskTeams: {
    type: Schema.Types.Mixed
  },
  cops: {
    type: Schema.Types.Mixed
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
}, {id: false, versionKey: false, strict: false});

module.exports = mongoConnections('master').model('ConfigRiskTeam', ConfigRiskTeam);
