const TrackingPhone = new mongoose.Schema({
  provider: { type: mongoose.Schema.Types.Mixed, ref: 'Provider' },
  phone: { type: String },
  reason: { type: String },
  status: { type: Number },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
});


// module.exports = mongoose.model('TrackingPhone', TrackingPhone);
module.exports = mongoConnections('master').model('TrackingPhone', TrackingPhone);