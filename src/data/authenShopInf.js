var AuthenShopInf = new mongoose.Schema({
    member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
    avatar: {
      type: String
    },
    frontCer: {
      type: String
    },
    storeName: {
      type: String
    },
    address: {
      type: String
    },
    backCer: {
      type: String
    },
    reason: {
      type: String,
      default: ''
    },
    phone: {
      type: String,
      default: ''
    },
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    status: {
      type: Number,
      default: 0
    }
}, {id: false, versionKey: 'v'});

// module.exports = mongoose.model('AuthenShopInf', AuthenShopInf);
module.exports = mongoConnections('master').model('AuthenShopInf', AuthenShopInf);
