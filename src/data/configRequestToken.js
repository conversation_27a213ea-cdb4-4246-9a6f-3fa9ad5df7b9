const async = require('async');
const ms = require('ms');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const mongoConnections = require('../connections/mongo')

const ConfigRequestToken = new mongoose.Schema({
  timeoutCheck: {
    type: Number
  },
  shouldSendMail: {
    type: Boolean
  },
  isOpen: {
    type: Number
  },
  whiteListIP: {
    type: Array
  },
  whiteListUrl: {
    type: Array
  },
  blackListIP:{
    type: Array
  }
}, {id: false, versionKey: false, strict: false});

module.exports = mongoConnections('master').model('ConfigRequestToken', ConfigRequestToken);
