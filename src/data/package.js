import _ from 'lodash';

var Package = new mongoose.Schema({
    name: {type: String},
    price: {type: Number},
    bonus: {type: Number},
    active: {type: Number},
    time: {type: Number},
    order: {type: Number},
    createdAt: {type: Number, default: Date.now},
    updatedAt: {type: Number, default: Date.now}
});

Package.statics.get = function(_id, cb) {
    const query = {_id, active: 1};
    this
        .findOne(query)
        .lean()
        // .cache(0, `package:${_id}`)
        .exec(cb);
}

Package.statics.list = function(fields, cb) {
    const query = {active: 1};
    fields = _.defaults({}, fields, {active: 0, order: 0, createdAt: 0, updatedAt: 0});
    const options = {
        sort: {
            order: 1
        }
    }

    this
        .find(query, fields, options)
        .lean()
        // .cache(0, `package:all`)
        .exec(cb);
}

// module.exports = mongoose.model('Package', Package);
module.exports = mongoConnections('master').model('Package', Package);
