var HotNewSchema = new mongoose.Schema({
  members : {
    type: Array,
    default: []
  },
  region : {
    type: Array
  },
  type: {
    type: Number
  },
  title: {
    type: String
  },
  body: {
    type: String
  },
  backgroundImage: {
    type: String,
    default: ''
  },
  validUtil: {
    type: Number,
    default: Date.now
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  actionURL: {
    type: String,
    default: ''
  },
  status: {
    type: Number,
    default: 0
  }
}, {id: false, versionKey: false});

HotNewSchema.statics.get = function (options, cb) {
  const hotnewQuery = {
    status: 1,
    type: options.type,
    members: {
      '$ne': options.userId
    },
    $or:[
      {
        'region.allow': 'all',
        'region.deny':{
          $ne: options.region
        }
      },
      {
        'region.allow': options.region
      }
    ],
    service: options.serviceId || 'all',
    validUtil: {
      $gte: Date.now()
    },
    hotNewType: {$ne: 'special'}
  }

  this
    .findOneAndUpdate(hotnewQuery, {
      '$push': {
        members: options.userId
      }
    })
    .sort("createdAt")
    .lean()
    .exec(cb)
}


// module.exports = mongoose.model('HotNew', HotNewSchema);
module.exports = mongoConnections('master').model('HotNew', HotNewSchema);
