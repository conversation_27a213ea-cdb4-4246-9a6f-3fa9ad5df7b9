var LocationSchema = new mongoose.Schema({
  member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
  location: { type: mongoose.Schema.Types.Mixed },
  bearing: {type: Number},
  speed: {type: Number},
  updatedAt: {
    type: Number,
    default: Date.now
  },
  sync: {
    type: Number,
    default: 0
  }
}, {id: false, versionKey: false});


// module.exports = mongoose.model('Location', LocationSchema);
module.exports = mongoConnections('master').model('Location', LocationSchema);
