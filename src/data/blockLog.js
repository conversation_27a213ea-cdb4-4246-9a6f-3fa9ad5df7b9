var BlockLogs = new mongoose.Schema({
  provider: { type: mongoose.Schema.Types.Mixed, ref: 'Provider' },
   createdAt: { type: Number, default: Date.now },
   updatedAt: { type: Number, default: Date.now },
   userId: {type: mongoose.Schema.Types.ObjectId, ref: 'User'},
   request: {type: mongoose.Schema.Types.ObjectId, ref: 'ReqUnlockMember'},
   amount: {type: Number},
   reTraining: {type: Number}, // 1- xe may, 2- oto, 3- lái hộ
   type: {type: String},
   blockType: {type: String},
   status: {type: Number},// -1 - <PERSON><PERSON><PERSON>, 0 - Huỷ yêu cầu, 1-<PERSON><PERSON><PERSON> yêu cầu kho<PERSON>, 2 - <PERSON><PERSON> du<PERSON>, -1 - Từ chối
   statusPayment: {type: Number}, // 0 - Ch<PERSON><PERSON> thanh toán, 1 - <PERSON><PERSON> thanh toán
   statusInfo: {type: Number}, // 0 - <PERSON><PERSON>, 1 - <PERSON><PERSON>, -1 - từ chối
   message: {type: String},
   data: {type: mongoose.Schema.Types.Mixed},
   member: {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
   identityFont: {type: String},
   identityBack: {type: String},
   avatarWithUniform: {type: String},
}, {id: false, versionKey: 'v'});
module.exports = mongoConnections('master').model('BlockLog', BlockLogs);