const mongoose = require("mongoose")
const Schema = mongoose.Schema
const _ = require("lodash")
const mongoConnections = require("../connections/mongo")

var CaregiverAuthentication = new mongoose.Schema(
  {
    member: {
      type: Schema.Types.ObjectId,
      ref: "Member",
    },
    photo: {
      type: String,
    },
    name: {
      type: String,
    },
    phone: {
      type: String,
    },
    levelStaff: {
      type: String,
      default: "caregiver",
    },
    region: {
      type: String,
    },
    identityCard: {
      type: String,
    },
    identityCardInf: {
      type: Schema.Types.Mixed,
    },
    certificationImg: {
      type: String,
    },
    certificationInf: {
      type: Schema.Types.Mixed,
    },
    diplomaImg: {
      type: String,
    },
    diplomaInf: {
      type: Schema.Types.Mixed,
    },
    signatureImg: {
      type: String,
    },
    supporter: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    status: {
      type: Number,
      default: 1,
    },
    createdAt: {
      type: Number,
      default: Date.now,
    },
  },
  { id: false, versionKey: "v" },
)

module.exports = mongoConnections("master").model("CaregiverAuthentication", CaregiverAuthentication)
