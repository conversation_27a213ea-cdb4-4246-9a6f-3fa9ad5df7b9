var MembershipPackageLogSchema = new mongoose.Schema({
  member: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Member'
  },
  package: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'MembershipPackage'
  },
  appName: {
    type: String
  },
  data: {
    type: mongoose.Schema.Types.Mixed
  },
  price: {
    type: Number
  },
  createdAt: { type: Number, default: Date.now }
}, { id: false, versionKey: false })


module.exports = mongoConnections('master').model('MembershipPackageLog', MembershipPackageLogSchema);
