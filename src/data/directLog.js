var DirectLog = new mongoose.Schema({
  member: { type: mongoose.Schema.Types.ObjectId, ref: 'Member' },
  amount: { type: String },
  initialCoints: { type: Number },
  finalCoints: { type: Number },
  initialRealMoney: { type: Number },
  finalRealMoney: { type: Number },
  createdAt: {
    type: Number,
    default: Date.now
  },
  type: { type: Number }, // 1: coints, 2: SSMoney
  status: { type: Number, default: 1 },
  maker: { type: String },
  region: { type: String }
});


// module.exports = mongoose.model('DirectLog', DirectLog);
module.exports = mongoConnections('master').model('DirectLog', DirectLog);
