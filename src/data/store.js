const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const Store = new mongoose.Schema({
  name: {
    type: String
  },
  region: {
    type: String
  },
  phone: [
    {
      type: String
    }
  ],
  member: {
    type: Schema.Types.ObjectId,
    ref: 'Member'
  },
  image:{
    type: String
  },
  background:{
    type: String
  },
  address: {
    type: String
  },
  description: {
    type: String
  },
  note : {
    type: String
  },
  status: {
    type: Number,
    default: 0
  },
  nameAlias: {
    type: String
  },
  location: {
    type: Schema.Types.Mixed,
  },
  category: {
    type: Schema.Types.ObjectId,
    ref: 'CategoryStore'
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  goliveAt: {
    type: Number
  },
  timeSettings: {
    type: Schema.Types.Mixed,
    default: {
        "0" : [
            {
                "endTime" : 79200000,
                "startTime" : 28800000
            }
        ],
        "1" : [
            {
                "endTime" : 79200000,
                "startTime" : 28800000
            }
        ],
        "2" : [
            {
                "endTime" : 79200000,
                "startTime" : 28800000
            }
        ],
        "3" : [
            {
                "endTime" : 79200000,
                "startTime" : 28800000
            }
        ],
        "4" : [
            {
                "endTime" : 79200000,
                "startTime" : 28800000
            }
        ],
        "5" : [
            {
                "endTime" : 79200000,
                "startTime" : 28800000
            }
        ],
        "6" : [
            {
                "endTime" : 79200000,
                "startTime" : 28800000
            }
        ]
    }
  },
  ref: {
    type: Schema.Types.Mixed
  },
  service: {
    type: Schema.Types.ObjectId
  },
  productSearch: [{
    _id: {
      type:Schema.Types.ObjectId
    },
    nameAlias: {
      type: String
    }
  }],
  hasProduct: {
    type: Number
  },
  golive: {
    type: Number,
    default: 0
  },
  messageGoLive: {
    type: String
  },
  golink: {
    type: Number,
    default: 0
  },
  messageGoLink: {
    type: String
  },
  type: {
    type: Schema.Types.ObjectId,
    ref: 'StoreTypes'
  },
  productTypes: [{
    type: Schema.Types.ObjectId,
    ref: 'ProductType'
  }],
  businessType: [{
    type: Schema.Types.ObjectId,
    ref: 'BusinessType'
  }],
  subType: [{
    type: Schema.Types.ObjectId,
    ref: 'SubType'
  }],
  totalOrders: {
    type: Number,
    default: 0
  },
  paymentMethod: {
    type: Schema.Types.Mixed
  }
}, { id: false, versionKey: false});

module.exports = mongoConnections('master').model('Store', Store);
