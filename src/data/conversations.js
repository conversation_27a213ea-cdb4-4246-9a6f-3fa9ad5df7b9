const Conversation = new mongoose.Schema({
    name: {
      type: String,
      default: ''
    },
    users: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Member'
    }],
    isGroup: {
      type: Number,
      default: 0
    },
    latestMessage: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Message'
    },
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
});


// module.exports = mongoose.model('Conversation', Conversation);
module.exports = mongoConnections('master').model('Conversation', Conversation);
