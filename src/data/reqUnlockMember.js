var ReqUnlockMember = new mongoose.Schema({
  provider: { type: mongoose.Schema.Types.Mixed, ref: 'Provider' },
   member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
   amount: {type: Number},
   reTraining: {type: Number},
   description: {type: String},
   images: {type: mongoose.Schema.Types.Mixed},
   type: {type: String},
   blockType: {type: String},
   createdAt: { type: Number, default: Date.now },
   approvedAt: { type: Number },
   approvedReason: { type: String },
   requester: {type: mongoose.Schema.Types.ObjectId, ref: 'User'},
   approver: {type: mongoose.Schema.Types.ObjectId, ref: 'User'},
   status: {type: Number, default: 1}, //1-Ch<PERSON> duy<PERSON>t, 2 - <PERSON><PERSON> duy<PERSON>t, -1 - T<PERSON> chối, 0 - Huỷ yêu cầu
   region: {type: String},
   identityFont: {type: String},
   identityBack: {type: String},
   avatarWithUniform: {type: String},
}, {id: false, versionKey: 'v'});

module.exports = mongoConnections('master').model('ReqUnlockMember', ReqUnlockMember);