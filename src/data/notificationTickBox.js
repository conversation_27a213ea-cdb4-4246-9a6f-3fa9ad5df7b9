var NotificationTickBoxSchema = new mongoose.Schema({
    platform : String,
    notify_token: String,
    newVersion: {
      type: Number
    },
    member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
    createdAt: {
    	type: Number,
    	default: Date.now
    },
    updatedAt: {
    	type: Number
    }
}, {id: false, versionKey: 'v'});


module.exports = mongoConnections('master').model('NotificationTickBox', NotificationTickBoxSchema);
