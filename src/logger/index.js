const winston = require('winston')
import {loggingAddr} from '../config';
const _ = require('lodash')
const fs = require('fs')
const rp = require('request-promise')
function setUp(dir) {
  fs.existsSync(dir) || fs.mkdirSync(dir)

  const logLevel = 'info'

  const logger = new winston.Logger({
    levels: {
      info: 1,
      error: 0
    },
    colors: {
      info: 'green',
      error: 'red'
    },
    transports: [
      new (winston.transports.Console)({
        level: 'info',
        colorize: true,
        datePattern: 'yyyy-MM-DD',
        timestamp: function () {
          return new Date().toLocaleString('vi-VN')
        }
      }),
      new (require('winston-daily-rotate-file'))({
        level: 'error',
  			datePattern: 'yyyy-MM-DD',
  			filename: dir + '/system-',
  			json: false,
  			timestamp: function () {
          return new Date().toLocaleString('vi-VN')
        }
  		})
    ]
  })

  const obj = {
    logInfo: () => {},
    logError: (args, path = '', body = {}) => {
      logger.error(...args, {})
      const options = {
        method: 'POST',
        uri: `${loggingAddr}/api/save`,
        body: {
          path,
          body,
          error: args.toString(),
          service: "ss-api"
        },
        json: true 
      };

      rp(options)
    }
  }

  if(logLevel === 'info') {
    obj.logInfo = (...args) => {
      logger.info(...args, {})
    }
  }

  return obj
}

module.exports = setUp
